{"version": 3, "file": "helpers.segment.js", "sources": ["../../src/helpers/helpers.core.ts", "../../src/helpers/helpers.math.ts", "../../src/helpers/helpers.collection.ts", "../../src/helpers/helpers.extras.ts", "../../src/helpers/helpers.easing.ts", "../../src/helpers/helpers.color.ts", "../../src/core/core.animations.defaults.js", "../../src/core/core.layouts.defaults.js", "../../src/helpers/helpers.intl.ts", "../../src/core/core.ticks.js", "../../src/core/core.scale.defaults.js", "../../src/core/core.defaults.js", "../../src/helpers/helpers.canvas.ts", "../../src/helpers/helpers.options.ts", "../../src/helpers/helpers.config.ts", "../../src/helpers/helpers.curve.ts", "../../src/helpers/helpers.dom.ts", "../../src/helpers/helpers.interpolation.ts", "../../src/helpers/helpers.rtl.ts", "../../src/helpers/helpers.segment.js"], "sourcesContent": ["/**\n * @namespace Chart.helpers\n */\n\nimport type {AnyObject} from '../types/basic.js';\nimport type {ActiveDataPoint, ChartEvent} from '../types/index.js';\n\n/**\n * An empty function that can be used, for example, for optional callback.\n */\nexport function noop() {\n  /* noop */\n}\n\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nexport const uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isNullOrUndef(value: unknown): value is null | undefined {\n  return value === null || typeof value === 'undefined';\n}\n\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nexport function isArray<T = unknown>(value: unknown): value is T[] {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isObject(value: unknown): value is AnyObject {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value: unknown): value is number {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\nexport {\n  isNumberFinite as isFinite,\n};\n\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nexport function finiteOrDefault(value: unknown, defaultValue: number) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nexport function valueOrDefault<T>(value: T | undefined, defaultValue: T) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\n\nexport const toPercentage = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : +value / dimension;\n\nexport const toDimension = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\n\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nexport function callback<T extends (this: TA, ...restArgs: unknown[]) => R, TA, R>(\n  fn: T | undefined,\n  args: unknown[],\n  thisArg?: TA\n): R | undefined {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\n\n/**\n * Note(SB) for performance sake, this method should only be used when loopable type\n * is unknown or in none intensive code (not called often and small loopable). Else\n * it's preferable to use a regular for() loop and save extra function calls.\n * @param loopable - The object or array to be iterated.\n * @param fn - The function to call for each item.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n * @param [reverse] - If true, iterates backward on the loopable.\n */\nexport function each<T, TA>(\n  loopable: Record<string, T>,\n  fn: (this: TA, v: T, i: string) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[],\n  fn: (this: TA, v: T, i: number) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[] | Record<string, T>,\n  fn: (this: TA, v: T, i: any) => void,\n  thisArg?: TA,\n  reverse?: boolean\n) {\n  let i: number, len: number, keys: string[];\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nexport function _elementsEqual(a0: ActiveDataPoint[], a1: ActiveDataPoint[]) {\n  let i: number, ilen: number, v0: ActiveDataPoint, v1: ActiveDataPoint;\n\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nexport function clone<T>(source: T): T {\n  if (isArray(source)) {\n    return source.map(clone) as unknown as T;\n  }\n\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n\n    return target;\n  }\n\n  return source;\n}\n\nfunction isValidKey(key: string) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nexport function _merger(key: string, target: AnyObject, source: AnyObject, options: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\n\nexport interface MergeOptions {\n  merger?: (key: string, target: AnyObject, source: AnyObject, options?: AnyObject) => void;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` with the given `options`.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @param [options] - Merging options:\n * @param [options.merger] - The merge method (key, target, source, options)\n * @returns The `target` object.\n */\nexport function merge<T>(target: T, source: [], options?: MergeOptions): T;\nexport function merge<T, S1>(target: T, source: S1, options?: MergeOptions): T & S1;\nexport function merge<T, S1>(target: T, source: [S1], options?: MergeOptions): T & S1;\nexport function merge<T, S1, S2>(target: T, source: [S1, S2], options?: MergeOptions): T & S1 & S2;\nexport function merge<T, S1, S2, S3>(target: T, source: [S1, S2, S3], options?: MergeOptions): T & S1 & S2 & S3;\nexport function merge<T, S1, S2, S3, S4>(\n  target: T,\n  source: [S1, S2, S3, S4],\n  options?: MergeOptions\n): T & S1 & S2 & S3 & S4;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n\n  if (!isObject(target)) {\n    return target as AnyObject;\n  }\n\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current: AnyObject;\n\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options as AnyObject);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` *only* if not defined in target.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @returns The `target` object.\n */\nexport function mergeIf<T>(target: T, source: []): T;\nexport function mergeIf<T, S1>(target: T, source: S1): T & S1;\nexport function mergeIf<T, S1>(target: T, source: [S1]): T & S1;\nexport function mergeIf<T, S1, S2>(target: T, source: [S1, S2]): T & S1 & S2;\nexport function mergeIf<T, S1, S2, S3>(target: T, source: [S1, S2, S3]): T & S1 & S2 & S3;\nexport function mergeIf<T, S1, S2, S3, S4>(target: T, source: [S1, S2, S3, S4]): T & S1 & S2 & S3 & S4;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge<T>(target, source, {merger: _mergerIf});\n}\n\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nexport function _mergerIf(key: string, target: AnyObject, source: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n\n/**\n * @private\n */\nexport function _deprecated(scope: string, value: unknown, previous: string, current: string) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n      '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n\n/**\n * @private\n */\nexport function _splitKey(key: string) {\n  const parts = key.split('.');\n  const keys: string[] = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\n\nfunction _getKeyResolver(key: string) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        // For backward compatibility:\n        // Chart.helpers.core resolveObjectKey should break at empty key\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\n\nexport function resolveObjectKey(obj: AnyObject, key: string): any {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n\n/**\n * @private\n */\nexport function _capitalize(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n\nexport const defined = (value: unknown) => typeof value !== 'undefined';\n\nexport const isFunction = (value: unknown): value is (...args: any[]) => any => typeof value === 'function';\n\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nexport const setsEqual = <T>(a: Set<T>, b: Set<T>) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * @param e - The event\n * @private\n */\nexport function _isClickEvent(e: ChartEvent) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n", "import type {Point} from '../types/geometric.js';\nimport {isFinite as isFiniteNumber} from './helpers.core.js';\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\n\nexport const PI = Math.PI;\nexport const TAU = 2 * PI;\nexport const PITAU = TAU + PI;\nexport const INFINITY = Number.POSITIVE_INFINITY;\nexport const RAD_PER_DEG = PI / 180;\nexport const HALF_PI = PI / 2;\nexport const QUARTER_PI = PI / 4;\nexport const TWO_THIRDS_PI = PI * 2 / 3;\n\nexport const log10 = Math.log10;\nexport const sign = Math.sign;\n\nexport function almostEquals(x: number, y: number, epsilon: number) {\n  return Math.abs(x - y) < epsilon;\n}\n\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nexport function niceNum(range: number) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nexport function _factorize(value: number) {\n  const result: number[] = [];\n  const sqrt = Math.sqrt(value);\n  let i: number;\n\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) { // if value is a square number\n    result.push(sqrt);\n  }\n\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n\nexport function isNumber(n: unknown): n is number {\n  return !isNaN(parseFloat(n as string)) && isFinite(n as number);\n}\n\nexport function almostWhole(x: number, epsilon: number) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\n\n/**\n * @private\n */\nexport function _setMinAndMaxByKey(\n  array: Record<string, number>[],\n  target: { min: number, max: number },\n  property: string\n) {\n  let i: number, ilen: number, value: number;\n\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\n\nexport function toRadians(degrees: number) {\n  return degrees * (PI / 180);\n}\n\nexport function toDegrees(radians: number) {\n  return radians * (180 / PI);\n}\n\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nexport function _decimalPlaces(x: number) {\n  if (!isFiniteNumber(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n\n// Gets the angle from vertical upright to the point about a centre.\nexport function getAngleFromPoint(\n  centrePoint: Point,\n  anglePoint: Point\n) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n\n  if (angle < (-0.5 * PI)) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\n\nexport function distanceBetweenPoints(pt1: Point, pt2: Point) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nexport function _angleDiff(a: number, b: number) {\n  return (a - b + PITAU) % TAU - PI;\n}\n\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nexport function _normalizeAngle(a: number) {\n  return (a % TAU + TAU) % TAU;\n}\n\n/**\n * @private\n */\nexport function _angleBetween(angle: number, start: number, end: number, sameAngleIsFullCircle?: boolean) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\n\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nexport function _limitValue(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/**\n * @param {number} value\n * @private\n */\nexport function _int16Range(value: number) {\n  return _limitValue(value, -32768, 32767);\n}\n\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nexport function _isBetween(value: number, start: number, end: number, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n", "import {_capitalize} from './helpers.core.js';\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param value - value to find\n * @param cmp\n * @private\n */\nexport function _lookup(\n  table: number[],\n  value: number,\n  cmp?: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup<T>(\n  table: T[],\n  value: number,\n  cmp: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup(\n  table: unknown[],\n  value: number,\n  cmp?: (value: number) => boolean\n) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid: number;\n\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n\n  return {lo, hi};\n}\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nexport const _lookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number,\n  last?: boolean\n) =>\n  _lookup(table, value, last\n    ? index => {\n      const ti = table[index][key];\n      return ti < value || ti === value && table[index + 1][key] === value;\n    }\n    : index => table[index][key] < value);\n\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nexport const _rlookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number\n) =>\n  _lookup(table, value, index => table[index][key] >= value);\n\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nexport function _filterBetween(values: number[], min: number, max: number) {\n  let start = 0;\n  let end = values.length;\n\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\n\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'] as const;\n\nexport interface ArrayListener<T> {\n  _onDataPush?(...item: T[]): void;\n  _onDataPop?(): void;\n  _onDataShift?(): void;\n  _onDataSplice?(index: number, deleteCount: number, ...items: T[]): void;\n  _onDataUnshift?(...item: T[]): void;\n}\n\n/**\n * Hooks the array methods that add or remove values ('push', pop', 'shift', 'splice',\n * 'unshift') and notify the listener AFTER the array has been altered. Listeners are\n * called on the '_onData*' callbacks (e.g. _onDataPush, etc.) with same arguments.\n */\nexport function listenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n\n        return res;\n      }\n    });\n  });\n}\n\n\n/**\n * Removes the given array event listener and cleanup extra attached properties (such as\n * the _chartjs stub and overridden methods) if array doesn't have any more listeners.\n */\nexport function unlistenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n\n  if (listeners.length > 0) {\n    return;\n  }\n\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n\n  delete array._chartjs;\n}\n\n/**\n * @param items\n */\nexport function _arrayUnique<T>(items: T[]) {\n  const set = new Set<T>(items);\n\n  if (set.size === items.length) {\n    return items;\n  }\n\n  return Array.from(set);\n}\n", "import type {ChartMeta, PointElement} from '../types/index.js';\n\nimport {_limitValue} from './helpers.math.js';\nimport {_lookupByKey} from './helpers.collection.js';\n\nexport function fontString(pixelSize: number, fontStyle: string, fontFamily: string) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n\n/**\n* Request animation polyfill\n*/\nexport const requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\n\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nexport function throttled<TArgs extends Array<any>>(\n  fn: (...args: TArgs) => void,\n  thisArg: any,\n) {\n  let argsToUse = [] as TArgs;\n  let ticking = false;\n\n  return function(...args: TArgs) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n\n/**\n * Debounces calling `fn` for `delay` ms\n */\nexport function debounce<TArgs extends Array<any>>(fn: (...args: TArgs) => void, delay: number) {\n  let timeout;\n  return function(...args: TArgs) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nexport const _toLeftRightCenter = (align: 'start' | 'end' | 'center') => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nexport const _alignStartEnd = (align: 'start' | 'end' | 'center', start: number, end: number) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nexport const _textX = (align: 'left' | 'right' | 'center', left: number, right: number, rtl: boolean) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\n/**\n * Return start and count of visible points.\n * @private\n */\nexport function _getStartAndCountOfVisiblePoints(meta: ChartMeta<'line' | 'scatter'>, points: PointElement[], animationsDisabled: boolean) {\n  const pointCount = points.length;\n\n  let start = 0;\n  let count = pointCount;\n\n  if (meta._sorted) {\n    const {iScale, _parsed} = meta;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n\n    if (minDefined) {\n      start = _limitValue(Math.min(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, axis, min).lo,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo),\n      0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1),\n      start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n\n  return {start, count};\n}\n\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nexport function _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n", "import {PI, TAU, HALF_PI} from './helpers.math.js';\n\nconst atEdge = (t: number) => t === 0 || t === 1;\nconst elasticIn = (t: number, s: number, p: number) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t: number, s: number, p: number) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n\n/**\n * Easing functions adapted from <PERSON>'s easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: (t: number) => t,\n\n  easeInQuad: (t: number) => t * t,\n\n  easeOutQuad: (t: number) => -t * (t - 2),\n\n  easeInOutQuad: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n\n  easeInCubic: (t: number) => t * t * t,\n\n  easeOutCubic: (t: number) => (t -= 1) * t * t + 1,\n\n  easeInOutCubic: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n\n  easeInQuart: (t: number) => t * t * t * t,\n\n  easeOutQuart: (t: number) => -((t -= 1) * t * t * t - 1),\n\n  easeInOutQuart: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n\n  easeInQuint: (t: number) => t * t * t * t * t,\n\n  easeOutQuint: (t: number) => (t -= 1) * t * t * t * t + 1,\n\n  easeInOutQuint: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n\n  easeInSine: (t: number) => -Math.cos(t * HALF_PI) + 1,\n\n  easeOutSine: (t: number) => Math.sin(t * HALF_PI),\n\n  easeInOutSine: (t: number) => -0.5 * (Math.cos(PI * t) - 1),\n\n  easeInExpo: (t: number) => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n\n  easeOutExpo: (t: number) => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n\n  easeInOutExpo: (t: number) => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n\n  easeInCirc: (t: number) => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n\n  easeOutCirc: (t: number) => Math.sqrt(1 - (t -= 1) * t),\n\n  easeInOutCirc: (t: number) => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n\n  easeInElastic: (t: number) => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n\n  easeOutElastic: (t: number) => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n\n  easeInOutElastic(t: number) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n\n  easeInBack(t: number) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n\n  easeOutBack(t: number) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n\n  easeInOutBack(t: number) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n\n  easeInBounce: (t: number) => 1 - effects.easeOutBounce(1 - t),\n\n  easeOutBounce(t: number) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n\n  easeInOutBounce: (t: number) => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n} as const;\n\nexport type EasingFunction = keyof typeof effects\n\nexport default effects;\n", "import {Color} from '@kurkle/color';\n\nexport function isPatternOrGradient(value: unknown): value is CanvasPattern | CanvasGradient {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n\n  return false;\n}\n\nexport function color(value: CanvasGradient): CanvasGradient;\nexport function color(value: CanvasPattern): CanvasPattern;\nexport function color(\n  value:\n  | string\n  | { r: number; g: number; b: number; a: number }\n  | [number, number, number]\n  | [number, number, number, number]\n): Color;\nexport function color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\n\nexport function getHoverColor(value: CanvasGradient): CanvasGradient;\nexport function getHoverColor(value: CanvasPattern): CanvasPattern;\nexport function getHoverColor(value: string): string;\nexport function getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n", "const numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\n\nexport function applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined,\n  });\n\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: (name) => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn',\n  });\n\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    },\n  });\n\n  defaults.describe('animations', {\n    _fallback: 'animation',\n  });\n\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0 // show immediately\n        },\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0 // for keeping the dataset visible all the way through the animation\n        },\n      }\n    }\n  });\n}\n", "export function applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\n", "\nconst intlCache = new Map<string, Intl.NumberFormat>();\n\nfunction getNumberFormat(locale: string, options?: Intl.NumberFormatOptions) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\n\nexport function formatNumber(num: number, locale: string, options?: Intl.NumberFormatOptions) {\n  return getNumberFormat(locale, options).format(num);\n}\n", "import {isArray} from '../helpers/helpers.core.js';\nimport {formatNumber} from '../helpers/helpers.intl.js';\nimport {log10} from '../helpers/helpers.math.js';\n\n/**\n * Namespace to hold formatters for different types of ticks\n * @namespace Chart.Ticks.formatters\n */\nconst formatters = {\n  /**\n   * Formatter for value labels\n   * @method Chart.Ticks.formatters.values\n   * @param value the value to display\n   * @return {string|string[]} the label to display\n   */\n  values(value) {\n    return isArray(value) ? /** @type {string[]} */ (value) : '' + value;\n  },\n\n  /**\n   * Formatter for numeric ticks\n   * @method Chart.Ticks.formatters.numeric\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0'; // never show decimal places for 0\n    }\n\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue; // This is used when there are less than 2 ticks as the tick interval.\n\n    if (ticks.length > 1) {\n      // all ticks are small or there huge numbers; use scientific notation\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n\n      delta = calculateDelta(tickValue, ticks);\n    }\n\n    const logDelta = log10(Math.abs(delta));\n\n    // When datasets have values approaching Number.MAX_VALUE, the tick calculations might result in\n    // infinity and eventually NaN. Passing NaN for minimumFractionDigits or maximumFractionDigits\n    // will make the number formatter throw. So instead we check for isNaN and use a fallback value.\n    //\n    // toFixed has a max of 20 decimal places\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n\n    const options = {notation, minimumFractionDigits: numDecimal, maximumFractionDigits: numDecimal};\n    Object.assign(options, this.options.ticks.format);\n\n    return formatNumber(tickValue, locale, options);\n  },\n\n\n  /**\n   * Formatter for logarithmic ticks\n   * @method Chart.Ticks.formatters.logarithmic\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || (tickValue / (Math.pow(10, Math.floor(log10(tickValue)))));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n\n};\n\n\nfunction calculateDelta(tickValue, ticks) {\n  // Figure out how many digits to show\n  // The space between the first two ticks might be smaller than normal spacing\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n\n  // If we have a number like 2.5 as the delta, figure out how many decimal places we need\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    // not an integer\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\n\n/**\n * Namespace to hold static tick generation functions\n * @namespace Chart.Ticks\n */\nexport default {formatters};\n", "import Ticks from './core.ticks.js';\n\nexport function applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n\n    /**\n     * Scale boundary strategy (bypassed by min/max time options)\n     * - `data`: make sure data are fully visible, ticks outside are removed\n     * - `ticks`: make sure ticks are fully visible, data outside are truncated\n     * @see https://github.com/chartjs/Chart.js/pull/4556\n     * @since 3.0.0\n     */\n    bounds: 'ticks',\n\n    clip: true,\n\n    /**\n     * Addition grace added to max and reduced from min data value.\n     * @since 3.0.0\n     */\n    grace: 0,\n\n    // grid line settings\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false,\n    },\n\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n\n    // scale title\n    title: {\n      // display property\n      display: false,\n\n      // actual label\n      text: '',\n\n      // top/bottom padding\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n\n    // label settings\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      // We pass through arrays to be rendered as multiline labels, we convert Others to strings here.\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2,\n    }\n  });\n\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: (name) => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: (name) => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash',\n  });\n\n  defaults.describe('scales', {\n    _fallback: 'scale',\n  });\n\n  defaults.describe('scale.ticks', {\n    _scriptable: (name) => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: (name) => name !== 'backdropPadding',\n  });\n}\n", "import {getHoverColor} from '../helpers/helpers.color.js';\nimport {isObject, merge, valueOrDefault} from '../helpers/helpers.core.js';\nimport {applyAnimationsDefaults} from './core.animations.defaults.js';\nimport {applyLayoutsDefaults} from './core.layouts.defaults.js';\nimport {applyScaleDefaults} from './core.scale.defaults.js';\n\nexport const overrides = Object.create(null);\nexport const descriptors = Object.create(null);\n\n/**\n * @param {object} node\n * @param {string} key\n * @return {object}\n */\nfunction getScope(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\n\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope(root, scope), values);\n  }\n  return merge(getScope(root, ''), scope);\n}\n\n/**\n * Please use the module's default export which provides a singleton instance\n * Note: class is exported for typedoc\n */\nexport class Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n\n  /**\n\t * @param {string} scope\n\t */\n  get(scope) {\n    return getScope(this, scope);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n\n  /**\n\t * Routes the named defaults to fallback to another scope/name.\n\t * This routing is useful when those target values, like defaults.color, are changed runtime.\n\t * If the values would be copied, the runtime change would not take effect. By routing, the\n\t * fallback is evaluated at each access, so its always up to date.\n\t *\n\t * Example:\n\t *\n\t * \tdefaults.route('elements.arc', 'backgroundColor', '', 'color')\n\t *   - reads the backgroundColor from defaults.color when undefined locally\n\t *\n\t * @param {string} scope Scope this route applies to.\n\t * @param {string} name Property name that should be routed to different namespace when not defined here.\n\t * @param {string} targetScope The namespace where those properties should be routed to.\n\t * Empty string ('') is the root of defaults.\n\t * @param {string} targetName The target name in the target scope the property should be routed to.\n\t */\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope(this, scope);\n    const targetScopeObject = getScope(this, targetScope);\n    const privateName = '_' + name;\n\n    Object.defineProperties(scopeObject, {\n      // A private property is defined to hold the actual value, when this property is set in its scope (set in the setter)\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      // The actual property is defined as getter/setter so we can do the routing when value is not locally set.\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n\n  apply(appliers) {\n    appliers.forEach((apply) => apply(this));\n  }\n}\n\n// singleton instance\nexport default /* #__PURE__ */ new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n", "import type {\n  Chart,\n  Point,\n  FontSpec,\n  CanvasFontSpec,\n  PointStyle,\n  RenderTextOpts,\n  BackdropOptions\n} from '../types/index.js';\nimport type {\n  TRBL,\n  SplinePoint,\n  RoundedRect,\n  TRBLCorners\n} from '../types/geometric.js';\nimport {isArray, isNullOrUndef} from './helpers.core.js';\nimport {PI, TAU, HALF_PI, QUARTER_PI, TWO_THIRDS_PI, RAD_PER_DEG} from './helpers.math.js';\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nexport function toFontString(font: FontSpec) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\n\n/**\n * @private\n */\nexport function _measureText(\n  ctx: CanvasRenderingContext2D,\n  data: Record<string, number>,\n  gc: string[],\n  longest: number,\n  string: string\n) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n\ntype Thing = string | undefined | null\ntype Things = (Thing | Thing[])[]\n\n/**\n * @private\n */\n// eslint-disable-next-line complexity\nexport function _longestText(\n  ctx: CanvasRenderingContext2D,\n  font: string,\n  arrayOfThings: Things,\n  cache?: {data?: Record<string, number>, garbageCollect?: string[], font?: string}\n) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n\n  ctx.save();\n\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i: number, j: number, jlen: number, thing: Thing | Thing[], nestedThing: Thing | Thing[];\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n\n  ctx.restore();\n\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nexport function _alignPixel(chart: Chart, pixel: number, width: number) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n\n/**\n * Clears the entire canvas.\n */\nexport function clearCanvas(canvas?: HTMLCanvasElement, ctx?: CanvasRenderingContext2D) {\n  if (!ctx && !canvas) {\n    return;\n  }\n\n  ctx = ctx || canvas.getContext('2d');\n\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\n\nexport interface DrawPointOptions {\n  pointStyle: PointStyle;\n  rotation?: number;\n  radius: number;\n  borderWidth: number;\n}\n\nexport function drawPoint(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number\n) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n\n// eslint-disable-next-line complexity\nexport function drawPointLegend(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number,\n  w: number\n) {\n  let type: string, xOffset: number, yOffset: number, size: number, cornerRadius: number, width: number, xOffsetW: number, yOffsetW: number;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nexport function _isPointInArea(\n  point: Point,\n  area: TRBL,\n  margin?: number\n) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\n\nexport function clipArea(ctx: CanvasRenderingContext2D, area: TRBL) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\n\nexport function unclipArea(ctx: CanvasRenderingContext2D) {\n  ctx.restore();\n}\n\n/**\n * @private\n */\nexport function _steppedLineTo(\n  ctx: CanvasRenderingContext2D,\n  previous: Point,\n  target: Point,\n  flip?: boolean,\n  mode?: string\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n\n/**\n * @private\n */\nexport function _bezierCurveTo(\n  ctx: CanvasRenderingContext2D,\n  previous: SplinePoint,\n  target: SplinePoint,\n  flip?: boolean\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\n\nfunction setRenderOpts(ctx: CanvasRenderingContext2D, opts: RenderTextOpts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\n\nfunction decorateText(\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  line: string,\n  opts: RenderTextOpts\n) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\n\nfunction drawBackdrop(ctx: CanvasRenderingContext2D, opts: BackdropOptions) {\n  const oldColor = ctx.fillStyle;\n\n  ctx.fillStyle = opts.color as string;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n\n/**\n * Render text onto the canvas\n */\nexport function renderText(\n  ctx: CanvasRenderingContext2D,\n  text: string | string[],\n  x: number,\n  y: number,\n  font: CanvasFontSpec,\n  opts: RenderTextOpts = {}\n) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i: number, line: string;\n\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n\n    y += Number(font.lineHeight);\n  }\n\n  ctx.restore();\n}\n\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nexport function addRoundedRectPath(\n  ctx: CanvasRenderingContext2D,\n  rect: RoundedRect & { radius: TRBLCorners }\n) {\n  const {x, y, w, h, radius} = rect;\n\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\n", "import defaults from '../core/core.defaults.js';\nimport {isArray, isObject, toDimension, valueOrDefault} from './helpers.core.js';\nimport {toFontString} from './helpers.canvas.js';\nimport type {ChartArea, FontSpec, Point} from '../types/index.js';\nimport type {TRBL, TRBLCorners} from '../types/geometric.js';\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n\n/**\n * @alias Chart.helpers.options\n * @namespace\n */\n/**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */\nexport function toLineHeight(value: number | string, size: number): number {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n\n  value = +matches[2];\n\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n    default:\n      break;\n  }\n\n  return size * value;\n}\n\nconst numberOrZero = (v: unknown) => +v || 0;\n\n/**\n * @param value\n * @param props\n */\nexport function _readValueToProps<K extends string>(value: number | Record<K, number>, props: K[]): Record<K, number>;\nexport function _readValueToProps<K extends string, T extends string>(value: number | Record<K & T, number>, props: Record<T, K>): Record<T, number>;\nexport function _readValueToProps(value: number | Record<string, number>, props: string[] | Record<string, string>) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nexport function toTRBL(value: number | TRBL | Point) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\n\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nexport function toTRBLCorners(value: number | TRBLCorners) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nexport function toPadding(value?: number | TRBL): ChartArea {\n  const obj = toTRBL(value) as ChartArea;\n\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n\n  return obj;\n}\n\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\n\nexport function toFont(options: Partial<FontSpec>, fallback?: Partial<FontSpec>) {\n  options = options || {};\n  fallback = fallback || defaults.font as FontSpec;\n\n  let size = valueOrDefault(options.size, fallback.size);\n\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n\n  font.string = toFontString(font);\n  return font;\n}\n\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nexport function resolve(inputs: Array<unknown>, context?: object, index?: number, info?: { cacheable: boolean }) {\n  let cacheable = true;\n  let i: number, ilen: number, value: unknown;\n\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nexport function _addGrace(minmax: { min: number; max: number; }, grace: number | string, beginAtZero: boolean) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value: number, add: number) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\n\n/**\n * Create a context inheriting parentContext\n * @param parentContext\n * @param context\n * @returns\n */\nexport function createContext<T extends object>(parentContext: null, context: T): T;\nexport function createContext<T extends object, P extends T>(parentContext: P, context: T): P & T;\nexport function createContext(parentContext: object, context: object) {\n  return Object.assign(Object.create(parentContext), context);\n}\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport type {AnyObject} from '../types/basic.js';\nimport type {ChartMeta} from '../types/index.js';\nimport type {\n  ResolverObjectKey,\n  ResolverCache,\n  ResolverProxy,\n  DescriptorDefaults,\n  Descriptor,\n  ContextCache,\n  ContextProxy\n} from './helpers.config.types.js';\nimport {isArray, isFunction, isObject, resolveObjectKey, _capitalize} from './helpers.core.js';\n\nexport * from './helpers.config.types.js';\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nexport function _createResolver<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  scopes: T,\n  prefixes = [''],\n  rootScopes?: R,\n  fallback?: ResolverObjectKey,\n  getTarget = () => scopes[0]\n) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache: ResolverCache<T, R> = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope: AnyObject) => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop: string) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop: string) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop: string, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  }) as ResolverProxy<T, R>;\n}\n\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nexport function _attachContext<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  proxy: ResolverProxy<T, R>,\n  context: AnyObject,\n  subProxy?: ResolverProxy<T, R>,\n  descriptorDefaults?: DescriptorDefaults\n) {\n  const cache: ContextCache<T, R> = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx: AnyObject) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope: AnyObject) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  }) as ContextProxy<T, R>;\n}\n\n/**\n * @private\n */\nexport function _descriptors(\n  proxy: ResolverCache,\n  defaults: DescriptorDefaults = {scriptable: true, indexable: true}\n): Descriptor {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\n\nconst readKey = (prefix: string, name: string) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop: string, value: unknown) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\n\nfunction _cached(\n  target: AnyObject,\n  prop: string,\n  resolve: () => unknown\n) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\n\nfunction _resolveWithContext(\n  target: ContextCache,\n  prop: string,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop]; // resolve from proxy\n\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\n\nfunction _resolveScriptable(\n  prop: string,\n  getValue: (ctx: AnyObject, sub: AnyObject) => unknown,\n  target: ContextCache,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\n\nfunction _resolveArray(\n  prop: string,\n  value: unknown[],\n  target: ContextCache,\n  isIndexable: (key: string) => boolean\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\n\nfunction resolveFallback(\n  fallback: ResolverObjectKey | ((prop: ResolverObjectKey, value: unknown) => ResolverObjectKey),\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\n\nconst getScope = (key: ResolverObjectKey, parent: AnyObject) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\n\nfunction addScopes(\n  set: Set<AnyObject>,\n  parentScopes: AnyObject[],\n  key: ResolverObjectKey,\n  parentFallback: ResolverObjectKey,\n  value: unknown\n) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\n\nfunction createSubResolver(\n  parentScopes: AnyObject[],\n  resolver: ResolverCache,\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set<AnyObject>();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop as string, value));\n}\n\nfunction addScopesFromKey(\n  set: Set<AnyObject>,\n  allScopes: AnyObject[],\n  key: ResolverObjectKey,\n  fallback: ResolverObjectKey,\n  item: unknown\n) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\n\nfunction subGetTarget(\n  resolver: ResolverCache,\n  prop: string,\n  value: unknown\n) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\n\nfunction _resolveWithPrefixes(\n  prop: string,\n  prefixes: string[],\n  scopes: AnyObject[],\n  proxy: ResolverProxy\n) {\n  let value: unknown;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\n\nfunction _resolve(key: string, scopes: AnyObject[]) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\n\nfunction getKeysFromAllScopes(target: ResolverCache) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\n\nfunction resolveKeysFromAllScopes(scopes: AnyObject[]) {\n  const set = new Set<string>();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\n\nexport function _parseObjectDataRadialScale(\n  meta: ChartMeta<'line' | 'scatter'>,\n  data: AnyObject[],\n  start: number,\n  count: number\n) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array<{r: unknown}>(count);\n  let i: number, ilen: number, index: number, item: AnyObject;\n\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n", "import {almostEquals, distanceBetweenPoints, sign} from './helpers.math.js';\nimport {_isPointInArea} from './helpers.canvas.js';\nimport type {ChartArea} from '../types/index.js';\nimport type {SplinePoint} from '../types/geometric.js';\n\nconst EPSILON = Number.EPSILON || 1e-14;\n\ntype OptionalSplinePoint = SplinePoint | false\nconst getPoint = (points: SplinePoint[], i: number): OptionalSplinePoint => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis: 'x' | 'y') => indexAxis === 'x' ? 'y' : 'x';\n\nexport function splineCurve(\n  firstPoint: SplinePoint,\n  middlePoint: SplinePoint,\n  afterPoint: SplinePoint,\n  t: number\n): {\n    previous: SplinePoint\n    next: SplinePoint\n  } {\n  // Props to <PERSON> at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n\n  // This function must also respect \"skipped\" points\n\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points: SplinePoint[], deltaK: number[], mK: number[]) {\n  const pointsLen = points.length;\n\n  let alphaK: number, betaK: number, tauK: number, squaredMagnitude: number, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\n\nfunction monotoneCompute(points: SplinePoint[], mK: number[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta: number, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nexport function splineCurveMonotone(points: SplinePoint[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK: number[] = Array(pointsLen).fill(0);\n  const mK: number[] = Array(pointsLen);\n\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n        : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n          : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n\n  monotoneAdjust(points, deltaK, mK);\n\n  monotoneCompute(points, mK, indexAxis);\n}\n\nfunction capControlPoint(pt: number, min: number, max: number) {\n  return Math.max(Math.min(pt, max), min);\n}\n\nfunction capBezierPoints(points: SplinePoint[], area: ChartArea) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n\n/**\n * @private\n */\nexport function _updateBezierControlPoints(\n  points: SplinePoint[],\n  options,\n  area: ChartArea,\n  loop: boolean,\n  indexAxis: 'x' | 'y'\n) {\n  let i: number, ilen: number, point: SplinePoint, controlPoints: ReturnType<typeof splineCurve>;\n\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n", "import type {ChartArea, Scale} from '../types/index.js';\nimport type Chart from '../core/core.controller.js';\nimport type {ChartEvent} from '../types.js';\nimport {INFINITY} from './helpers.math.js';\n\n/**\n * Note: typedefs are auto-exported, so use a made-up `dom` namespace where\n * necessary to avoid duplicates with `export * from './helpers`; see\n * https://github.com/microsoft/TypeScript/issues/46011\n * @typedef { import('../core/core.controller.js').default } dom.Chart\n * @typedef { import('../../types').ChartEvent } ChartEvent\n */\n\n/**\n * @private\n */\nexport function _isDomSupported(): boolean {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\n/**\n * @private\n */\nexport function _getParentNode(domNode: HTMLCanvasElement): HTMLCanvasElement {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = (parent as ShadowRoot).host;\n  }\n  return parent as HTMLCanvasElement;\n}\n\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\n\nfunction parseMaxStyle(styleValue: string | number, node: HTMLElement, parentProperty: string) {\n  let valueInPixels: number;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = (valueInPixels / 100) * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n\n  return valueInPixels;\n}\n\nconst getComputedStyle = (element: HTMLElement): CSSStyleDeclaration =>\n  element.ownerDocument.defaultView.getComputedStyle(element, null);\n\nexport function getStyle(el: HTMLElement, property: string): string {\n  return getComputedStyle(el).getPropertyValue(property);\n}\n\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles: CSSStyleDeclaration, style: string, suffix?: string): ChartArea {\n  const result = {} as ChartArea;\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\n\nconst useOffsetPos = (x: number, y: number, target: HTMLElement | EventTarget) =>\n  (x > 0 || y > 0) && (!target || !(target as HTMLElement).shadowRoot);\n\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(\n  e: Event | TouchEvent | MouseEvent,\n  canvas: HTMLCanvasElement\n): {\n    x: number;\n    y: number;\n    box: boolean;\n  } {\n  const touches = (e as TouchEvent).touches;\n  const source = (touches && touches.length ? touches[0] : e) as MouseEvent;\n  const {offsetX, offsetY} = source as MouseEvent;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\n\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\n\nexport function getRelativePosition(\n  event: Event | ChartEvent | TouchEvent | MouseEvent,\n  chart: Chart\n): { x: number; y: number } {\n  if ('native' in event) {\n    return event;\n  }\n\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\n\nfunction getContainerSize(canvas: HTMLCanvasElement, width: number, height: number): Partial<Scale> {\n  let maxWidth: number, maxHeight: number;\n\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\n\nconst round1 = (v: number) => Math.round(v * 10) / 10;\n\n// eslint-disable-next-line complexity\nexport function getMaximumSize(\n  canvas: HTMLCanvasElement,\n  bbWidth?: number,\n  bbHeight?: number,\n  aspectRatio?: number\n): { width: number; height: number } {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n\n  return {width, height};\n}\n\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nexport function retinaScale(\n  chart: Chart,\n  forceRatio: number,\n  forceStyle?: boolean\n): boolean | void {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n\n  const canvas = chart.canvas;\n\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nexport const supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() { // This function will be called when the browser attempts to access the passive property.\n        passiveSupported = true;\n        return false;\n      }\n    } as EventListenerOptions;\n\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}());\n\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\n\nexport function readUsedSize(\n  element: HTMLElement,\n  property: 'width' | 'height'\n): number | undefined {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n", "import type {Point, SplinePoint} from '../types/geometric.js';\n\n/**\n * @private\n */\nexport function _pointInLine(p1: Point, p2: Point, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n\n/**\n * @private\n */\nexport function _steppedInterpolation(\n  p1: Point,\n  p2: Point,\n  t: number, mode: 'middle' | 'after' | unknown\n) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n      : mode === 'after' ? t < 1 ? p1.y : p2.y\n        : t > 0 ? p2.y : p1.y\n  };\n}\n\n/**\n * @private\n */\nexport function _bezierInterpolation(p1: SplinePoint, p2: SplinePoint, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n", "export interface RTLAdapter {\n  x(x: number): number;\n  setWidth(w: number): void;\n  textAlign(align: 'center' | 'left' | 'right'): 'center' | 'left' | 'right';\n  xPlus(x: number, value: number): number;\n  leftForLtr(x: number, itemWidth: number): number;\n}\n\nconst getRightToLeftAdapter = function(rectX: number, width: number): RTLAdapter {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\n\nconst getLeftToRightAdapter = function(): RTLAdapter {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) { // eslint-disable-line no-unused-vars\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) { // eslint-disable-line @typescript-eslint/no-unused-vars\n      return x;\n    },\n  };\n};\n\nexport function getRtlAdapter(rtl: boolean, rectX: number, width: number) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\n\nexport function overrideTextDirection(ctx: CanvasRenderingContext2D, direction: 'ltr' | 'rtl') {\n  let style: CSSStyleDeclaration, original: [string, string];\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n\n    style.setProperty('direction', direction, 'important');\n    (ctx as { prevTextDirection?: [string, string] }).prevTextDirection = original;\n  }\n}\n\nexport function restoreTextDirection(ctx: CanvasRenderingContext2D, original?: [string, string]) {\n  if (original !== undefined) {\n    delete (ctx as { prevTextDirection?: [string, string] }).prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n", "import {_angleBetween, _angleDiff, _isBetween, _normalizeAngle} from './helpers.math.js';\nimport {createContext} from './helpers.options.js';\nimport {isPatternOrGradient} from './helpers.color.js';\n\n/**\n * @typedef { import('../elements/element.line.js').default } LineElement\n * @typedef { import('../elements/element.point.js').default } PointElement\n * @typedef {{start: number, end: number, loop: boolean, style?: any}} Segment\n */\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\n\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\n\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  // eslint-disable-next-line prefer-const\n  let {start, end, loop} = segment;\n  let i, ilen;\n\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\n\n/**\n * Returns the sub-segment(s) of a line segment that fall in the given bounds\n * @param {object} segment\n * @param {number} segment.start - start index of the segment, referring the points array\n * @param {number} segment.end - end index of the segment, referring the points array\n * @param {boolean} segment.loop - indicates that the segment is a loop\n * @param {object} [segment.style] - segment style\n * @param {PointElement[]} points - the points that this segment refers to\n * @param {object} [bounds]\n * @param {string} bounds.property - the property of a `PointElement` we are bounding. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the property\n * @param {number} bounds.end - end value of the property\n * @private\n **/\nexport function _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n\n    if (point.skip) {\n      continue;\n    }\n\n    value = normalize(point[property]);\n\n    if (value === prevValue) {\n      continue;\n    }\n\n    inside = between(value, startBound, endBound);\n\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n\n  return result;\n}\n\n\n/**\n * Returns the segments of the line that are inside given bounds\n * @param {LineElement} line\n * @param {object} [bounds]\n * @param {string} bounds.property - the property we are bounding with. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the `property`\n * @param {number} bounds.end - end value of the `property`\n * @private\n */\nexport function _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\n\n/**\n * Find start and end index of a line.\n */\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n\n  if (loop && !spanGaps) {\n    // loop and not spanning gaps, first find a gap to start from\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n\n  // find first non skipped point (after the first gap possibly)\n  while (start < count && points[start].skip) {\n    start++;\n  }\n\n  // if we looped to count, start needs to be 0\n  start %= count;\n\n  if (loop) {\n    // loop will go past count, if start > 0\n    end += start;\n  }\n\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n\n  // end could be more than count, normalize\n  end %= count;\n\n  return {start, end};\n}\n\n/**\n * Compute solid segments from Points, when spanGaps === false\n * @param {PointElement[]} points - the points\n * @param {number} start - start index\n * @param {number} max - max index (can go past count on a loop)\n * @param {boolean} loop - boolean indicating that this would be a loop if no gaps are found\n */\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        // @ts-ignore\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n\n  return result;\n}\n\n/**\n * Compute the continuous segments that define the whole line\n * There can be skipped points within a segment, if spanGaps is true.\n * @param {LineElement} line\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n * @private\n */\nexport function _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n\n  if (!count) {\n    return [];\n  }\n\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n\n/**\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\n\n/**\n * @param {LineElement} line\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    // Style can not start/end on a skipped point, adjust indices accordingly\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n\n  return result;\n}\n\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\n\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function(key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n"], "names": ["noop", "uid", "id", "isNullOrUndef", "value", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "undefined", "console", "warn", "keyResolvers", "v", "x", "o", "y", "_splitKey", "parts", "split", "tmp", "part", "push", "_getKeyResolver", "obj", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNumber", "n", "isNaN", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "isFiniteNumber", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "argsToUse", "ticking", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "_parsed", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "color", "Color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "duration", "easing", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "numeric", "tickValue", "ticks", "chart", "notation", "delta", "maxTick", "calculateDelta", "log<PERSON><PERSON><PERSON>", "numDecimal", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "clip", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "Ticks", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "descriptors", "getScope", "node", "root", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "appliers", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "point", "area", "margin", "clipArea", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "parseInt", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "getValue", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "spanGaps", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer"], "mappings": ";;;;;;;;AAAA;;;;IAUO,SAASA,IAAO,GAAA;AACrB,YACD;AAED;;AAEC,IACM,MAAMC,GAAM,GAAC,CAAA,IAAM;AACxB,IAAA,IAAIC,EAAK,GAAA,CAAA,CAAA;AACT,IAAA,OAAO,IAAMA,EAAAA,EAAAA,CAAAA;AACf,CAAA,IAAK;AAEL;;;;AAIC,IACM,SAASC,aAAcC,CAAAA,KAAc,EAA6B;AACvE,IAAA,OAAOA,KAAU,KAAA,IAAI,IAAI,OAAOA,KAAU,KAAA,WAAA,CAAA;AAC5C,CAAC;AAED;;;;AAIC,IACM,SAASC,OAAqBD,CAAAA,KAAc,EAAgB;AACjE,IAAA,IAAIE,MAAMD,OAAO,IAAIC,KAAMD,CAAAA,OAAO,CAACD,KAAQ,CAAA,EAAA;AACzC,QAAA,OAAO,IAAI,CAAA;KACZ;AACD,IAAA,MAAMG,OAAOC,MAAOC,CAAAA,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAAA,CAAAA,CAAAA;IAC5C,IAAIG,IAAAA,CAAKK,KAAK,CAAC,CAAG,EAAA,CAAA,CAAA,KAAO,SAAaL,IAAAA,IAAAA,CAAKK,KAAK,CAAC,CAAC,CAAA,CAAA,KAAO,QAAU,EAAA;AACjE,QAAA,OAAO,IAAI,CAAA;KACZ;AACD,IAAA,OAAO,KAAK,CAAA;AACd,CAAC;AAED;;;;AAIC,IACM,SAASC,QAAST,CAAAA,KAAc,EAAsB;IAC3D,OAAOA,KAAAA,KAAU,IAAI,IAAII,MAAOC,CAAAA,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAW,CAAA,KAAA,iBAAA,CAAA;AACrE,CAAC;AAED;;;IAIA,SAASU,cAAeV,CAAAA,KAAc,EAAmB;IACvD,OAAQ,CAAA,OAAOA,KAAAA,KAAU,YAAYA,KAAiBW,YAAAA,MAAK,KAAMC,QAAAA,CAAS,CAACZ,KAAAA,CAAAA,CAAAA;AAC7E,CAAA;AAKA;;;;AAIC,IACM,SAASa,eAAAA,CAAgBb,KAAc,EAAEc,YAAoB,EAAE;IACpE,OAAOJ,cAAAA,CAAeV,KAASA,CAAAA,GAAAA,KAAAA,GAAQc,YAAY,CAAA;AACrD,CAAC;AAED;;;;AAIC,IACM,SAASC,cAAAA,CAAkBf,KAAoB,EAAEc,YAAe,EAAE;AACvE,IAAA,OAAO,OAAOd,KAAAA,KAAU,WAAcc,GAAAA,YAAAA,GAAed,KAAK,CAAA;AAC5D,CAAC;MAEYgB,YAAe,GAAA,CAAChB,OAAwBiB,SACnD,GAAA,OAAOjB,UAAU,QAAYA,IAAAA,KAAAA,CAAMkB,QAAQ,CAAC,OAC1CC,UAAWnB,CAAAA,KAAAA,CAAAA,GAAS,MAClB,CAACA,KAAAA,GAAQiB,UAAU;MAEZG,WAAc,GAAA,CAACpB,OAAwBiB,SAClD,GAAA,OAAOjB,UAAU,QAAYA,IAAAA,KAAAA,CAAMkB,QAAQ,CAAC,OAC1CC,UAAWnB,CAAAA,KAAAA,CAAAA,GAAS,MAAMiB,SACxB,GAAA,CAACjB,MAAM;AAEb;;;;;;IAOO,SAASqB,QACdC,CAAAA,EAAiB,EACjBC,IAAe,EACfC,OAAY,EACG;AACf,IAAA,IAAIF,EAAM,IAAA,OAAOA,EAAGf,CAAAA,IAAI,KAAK,UAAY,EAAA;QACvC,OAAOe,EAAAA,CAAGG,KAAK,CAACD,OAASD,EAAAA,IAAAA,CAAAA,CAAAA;KAC1B;AACH,CAAC;AAuBM,SAASG,KACdC,QAAiC,EACjCL,EAAoC,EACpCE,OAAY,EACZI,OAAiB,EACjB;AACA,IAAA,IAAIC,GAAWC,GAAaC,EAAAA,IAAAA,CAAAA;AAC5B,IAAA,IAAI9B,QAAQ0B,QAAW,CAAA,EAAA;AACrBG,QAAAA,GAAAA,GAAMH,SAASK,MAAM,CAAA;AACrB,QAAA,IAAIJ,OAAS,EAAA;AACX,YAAA,IAAKC,CAAIC,GAAAA,GAAAA,GAAM,CAAGD,EAAAA,CAAAA,IAAK,GAAGA,CAAK,EAAA,CAAA;AAC7BP,gBAAAA,EAAAA,CAAGf,IAAI,CAACiB,OAAAA,EAASG,QAAQ,CAACE,EAAE,EAAEA,CAAAA,CAAAA,CAAAA;AAChC,aAAA;SACK,MAAA;AACL,YAAA,IAAKA,CAAI,GAAA,CAAA,EAAGA,CAAIC,GAAAA,GAAAA,EAAKD,CAAK,EAAA,CAAA;AACxBP,gBAAAA,EAAAA,CAAGf,IAAI,CAACiB,OAAAA,EAASG,QAAQ,CAACE,EAAE,EAAEA,CAAAA,CAAAA,CAAAA;AAChC,aAAA;SACD;KACI,MAAA,IAAIpB,SAASkB,QAAW,CAAA,EAAA;QAC7BI,IAAO3B,GAAAA,MAAAA,CAAO2B,IAAI,CAACJ,QAAAA,CAAAA,CAAAA;AACnBG,QAAAA,GAAAA,GAAMC,KAAKC,MAAM,CAAA;AACjB,QAAA,IAAKH,CAAI,GAAA,CAAA,EAAGA,CAAIC,GAAAA,GAAAA,EAAKD,CAAK,EAAA,CAAA;AACxBP,YAAAA,EAAAA,CAAGf,IAAI,CAACiB,OAASG,EAAAA,QAAQ,CAACI,IAAI,CAACF,CAAAA,CAAE,CAAC,EAAEE,IAAI,CAACF,CAAE,CAAA,CAAA,CAAA;AAC7C,SAAA;KACD;AACH,CAAC;AAED;;;;;AAKC,IACM,SAASI,cAAAA,CAAeC,EAAqB,EAAEC,EAAqB,EAAE;IAC3E,IAAIN,CAAAA,EAAWO,MAAcC,EAAqBC,EAAAA,EAAAA,CAAAA;IAElD,IAAI,CAACJ,MAAM,CAACC,EAAAA,IAAMD,GAAGF,MAAM,KAAKG,EAAGH,CAAAA,MAAM,EAAE;AACzC,QAAA,OAAO,KAAK,CAAA;KACb;IAED,IAAKH,CAAAA,GAAI,GAAGO,IAAOF,GAAAA,EAAAA,CAAGF,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;QAC3CQ,EAAKH,GAAAA,EAAE,CAACL,CAAE,CAAA,CAAA;QACVS,EAAKH,GAAAA,EAAE,CAACN,CAAE,CAAA,CAAA;QAEV,IAAIQ,EAAAA,CAAGE,YAAY,KAAKD,EAAGC,CAAAA,YAAY,IAAIF,EAAAA,CAAGG,KAAK,KAAKF,EAAGE,CAAAA,KAAK,EAAE;AAChE,YAAA,OAAO,KAAK,CAAA;SACb;AACH,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,CAAC;AAED;;;AAGC,IACM,SAASC,KAASC,CAAAA,MAAS,EAAK;AACrC,IAAA,IAAIzC,QAAQyC,MAAS,CAAA,EAAA;QACnB,OAAOA,MAAAA,CAAOC,GAAG,CAACF,KAAAA,CAAAA,CAAAA;KACnB;AAED,IAAA,IAAIhC,SAASiC,MAAS,CAAA,EAAA;AACpB,QAAA,MAAME,MAASxC,GAAAA,MAAAA,CAAOyC,MAAM,CAAC,IAAI,CAAA,CAAA;QACjC,MAAMd,IAAAA,GAAO3B,MAAO2B,CAAAA,IAAI,CAACW,MAAAA,CAAAA,CAAAA;QACzB,MAAMI,IAAAA,GAAOf,KAAKC,MAAM,CAAA;AACxB,QAAA,IAAIe,CAAI,GAAA,CAAA,CAAA;QAER,MAAOA,CAAAA,GAAID,IAAM,EAAA,EAAEC,CAAG,CAAA;AACpBH,YAAAA,MAAM,CAACb,IAAI,CAACgB,CAAAA,CAAE,CAAC,GAAGN,KAAMC,CAAAA,MAAM,CAACX,IAAI,CAACgB,CAAAA,CAAE,CAAC,CAAA,CAAA;AACzC,SAAA;QAEA,OAAOH,MAAAA,CAAAA;KACR;IAED,OAAOF,MAAAA,CAAAA;AACT,CAAC;AAED,SAASM,UAAAA,CAAWC,GAAW,EAAE;IAC/B,OAAO;AAAC,QAAA,WAAA;AAAa,QAAA,WAAA;AAAa,QAAA,aAAA;KAAc,CAACC,OAAO,CAACD,GAAAA,CAAAA,KAAS,CAAC,CAAA,CAAA;AACrE,CAAA;AAEA;;;;IAKO,SAASE,OAAAA,CAAQF,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAEU,OAAkB,EAAE;IAC7F,IAAI,CAACJ,WAAWC,GAAM,CAAA,EAAA;AACpB,QAAA,OAAA;KACD;IAED,MAAMI,IAAAA,GAAOT,MAAM,CAACK,GAAI,CAAA,CAAA;IACxB,MAAMK,IAAAA,GAAOZ,MAAM,CAACO,GAAI,CAAA,CAAA;IAExB,IAAIxC,QAAAA,CAAS4C,IAAS5C,CAAAA,IAAAA,QAAAA,CAAS6C,IAAO,CAAA,EAAA;;AAEpCC,QAAAA,KAAAA,CAAMF,MAAMC,IAAMF,EAAAA,OAAAA,CAAAA,CAAAA;KACb,MAAA;QACLR,MAAM,CAACK,GAAI,CAAA,GAAGR,KAAMa,CAAAA,IAAAA,CAAAA,CAAAA;KACrB;AACH,CAAC;AA0BM,SAASC,KAASX,CAAAA,MAAS,EAAEF,MAAmB,EAAEU,OAAsB,EAAa;IAC1F,MAAMI,OAAAA,GAAUvD,OAAQyC,CAAAA,MAAAA,CAAAA,GAAUA,MAAS,GAAA;AAACA,QAAAA,MAAAA;AAAO,KAAA,CAAA;IACnD,MAAMN,IAAAA,GAAOoB,QAAQxB,MAAM,CAAA;IAE3B,IAAI,CAACvB,SAASmC,MAAS,CAAA,EAAA;QACrB,OAAOA,MAAAA,CAAAA;KACR;AAEDQ,IAAAA,OAAAA,GAAUA,WAAW,EAAC,CAAA;IACtB,MAAMK,MAAAA,GAASL,OAAQK,CAAAA,MAAM,IAAIN,OAAAA,CAAAA;IACjC,IAAIO,OAAAA,CAAAA;AAEJ,IAAA,IAAK,IAAI7B,CAAI,GAAA,CAAA,EAAGA,CAAIO,GAAAA,IAAAA,EAAM,EAAEP,CAAG,CAAA;QAC7B6B,OAAUF,GAAAA,OAAO,CAAC3B,CAAE,CAAA,CAAA;QACpB,IAAI,CAACpB,SAASiD,OAAU,CAAA,EAAA;YACtB,SAAS;SACV;QAED,MAAM3B,IAAAA,GAAO3B,MAAO2B,CAAAA,IAAI,CAAC2B,OAAAA,CAAAA,CAAAA;QACzB,IAAK,IAAIX,CAAI,GAAA,CAAA,EAAGD,IAAOf,GAAAA,IAAAA,CAAKC,MAAM,EAAEe,CAAAA,GAAID,IAAM,EAAA,EAAEC,CAAG,CAAA;AACjDU,YAAAA,MAAAA,CAAO1B,IAAI,CAACgB,CAAE,CAAA,EAAEH,QAAQc,OAASN,EAAAA,OAAAA,CAAAA,CAAAA;AACnC,SAAA;AACF,KAAA;IAEA,OAAOR,MAAAA,CAAAA;AACT,CAAC;AAgBM,SAASe,OAAAA,CAAWf,MAAS,EAAEF,MAAmB,EAAa;;IAEpE,OAAOa,KAAAA,CAASX,QAAQF,MAAQ,EAAA;QAACe,MAAQG,EAAAA,SAAAA;AAAS,KAAA,CAAA,CAAA;AACpD,CAAC;AAED;;;IAIO,SAASA,SAAUX,CAAAA,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAE;IAC3E,IAAI,CAACM,WAAWC,GAAM,CAAA,EAAA;AACpB,QAAA,OAAA;KACD;IAED,MAAMI,IAAAA,GAAOT,MAAM,CAACK,GAAI,CAAA,CAAA;IACxB,MAAMK,IAAAA,GAAOZ,MAAM,CAACO,GAAI,CAAA,CAAA;IAExB,IAAIxC,QAAAA,CAAS4C,IAAS5C,CAAAA,IAAAA,QAAAA,CAAS6C,IAAO,CAAA,EAAA;AACpCK,QAAAA,OAAAA,CAAQN,IAAMC,EAAAA,IAAAA,CAAAA,CAAAA;KACT,MAAA,IAAI,CAAClD,MAAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAAA,EAAQK,GAAM,CAAA,EAAA;QAC7DL,MAAM,CAACK,GAAI,CAAA,GAAGR,KAAMa,CAAAA,IAAAA,CAAAA,CAAAA;KACrB;AACH,CAAC;AAED;;IAGO,SAASQ,WAAAA,CAAYC,KAAa,EAAE/D,KAAc,EAAEgE,QAAgB,EAAEN,OAAe,EAAE;AAC5F,IAAA,IAAI1D,UAAUiE,SAAW,EAAA;AACvBC,QAAAA,OAAAA,CAAQC,IAAI,CAACJ,KAAAA,GAAQ,KAAQC,GAAAA,QAAAA,GAC3B,kCAAkCN,OAAU,GAAA,WAAA,CAAA,CAAA;KAC/C;AACH,CAAC;AAED;AACA,MAAMU,YAAe,GAAA;;AAEnB,IAAA,EAAA,EAAIC,CAAAA,CAAKA,GAAAA,CAAAA;;IAETC,CAAGC,EAAAA,CAAAA,CAAKA,GAAAA,CAAAA,CAAED,CAAC;IACXE,CAAGD,EAAAA,CAAAA,CAAKA,GAAAA,CAAAA,CAAEC,CAAC;AACb,CAAA,CAAA;AAEA;;AAEC,IACM,SAASC,SAAUxB,CAAAA,GAAW,EAAE;IACrC,MAAMyB,KAAAA,GAAQzB,GAAI0B,CAAAA,KAAK,CAAC,GAAA,CAAA,CAAA;AACxB,IAAA,MAAM5C,OAAiB,EAAE,CAAA;AACzB,IAAA,IAAI6C,GAAM,GAAA,EAAA,CAAA;IACV,KAAK,MAAMC,QAAQH,KAAO,CAAA;QACxBE,GAAOC,IAAAA,IAAAA,CAAAA;QACP,IAAID,GAAAA,CAAI1D,QAAQ,CAAC,IAAO,CAAA,EAAA;AACtB0D,YAAAA,GAAAA,GAAMA,GAAIpE,CAAAA,KAAK,CAAC,CAAA,EAAG,CAAC,CAAK,CAAA,GAAA,GAAA,CAAA;SACpB,MAAA;AACLuB,YAAAA,IAAAA,CAAK+C,IAAI,CAACF,GAAAA,CAAAA,CAAAA;YACVA,GAAM,GAAA,EAAA,CAAA;SACP;AACH,KAAA;IACA,OAAO7C,IAAAA,CAAAA;AACT,CAAC;AAED,SAASgD,eAAAA,CAAgB9B,GAAW,EAAE;AACpC,IAAA,MAAMlB,OAAO0C,SAAUxB,CAAAA,GAAAA,CAAAA,CAAAA;AACvB,IAAA,OAAO+B,CAAAA,GAAO,GAAA;QACZ,KAAK,MAAMjC,KAAKhB,IAAM,CAAA;AACpB,YAAA,IAAIgB,MAAM,EAAI,EAAA;gBAGZ,MAAM;aACP;YACDiC,GAAMA,GAAAA,GAAAA,IAAOA,GAAG,CAACjC,CAAE,CAAA,CAAA;AACrB,SAAA;QACA,OAAOiC,GAAAA,CAAAA;AACT,KAAA,CAAA;AACF,CAAA;AAEO,SAASC,gBAAAA,CAAiBD,GAAc,EAAE/B,GAAW,EAAO;IACjE,MAAMiC,QAAAA,GAAWd,YAAY,CAACnB,GAAI,CAAA,KAAKmB,YAAY,CAACnB,GAAAA,CAAI,GAAG8B,eAAAA,CAAgB9B,GAAG,CAAA,CAAA,CAAA;AAC9E,IAAA,OAAOiC,QAASF,CAAAA,GAAAA,CAAAA,CAAAA;AAClB,CAAC;AAED;;AAEC,IACM,SAASG,WAAYC,CAAAA,GAAW,EAAE;IACvC,OAAOA,GAAAA,CAAIC,MAAM,CAAC,CAAA,CAAA,CAAGC,WAAW,EAAKF,GAAAA,GAAAA,CAAI5E,KAAK,CAAC,CAAA,CAAA,CAAA;AACjD,CAAC;MAGY+E,OAAU,GAAA,CAACvF,KAAmB,GAAA,OAAOA,UAAU,YAAY;MAE3DwF,UAAa,GAAA,CAACxF,KAAqD,GAAA,OAAOA,UAAU,WAAW;AAE5G;AACayF,MAAAA,SAAAA,GAAY,CAAIC,CAAAA,EAAWC,CAAc,GAAA;AACpD,IAAA,IAAID,CAAEE,CAAAA,IAAI,KAAKD,CAAAA,CAAEC,IAAI,EAAE;AACrB,QAAA,OAAO,KAAK,CAAA;KACb;IAED,KAAK,MAAMC,QAAQH,CAAG,CAAA;AACpB,QAAA,IAAI,CAACC,CAAAA,CAAEG,GAAG,CAACD,IAAO,CAAA,EAAA;AAChB,YAAA,OAAO,KAAK,CAAA;SACb;AACH,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,EAAE;AAEF;;;AAGC,IACM,SAASE,aAAcC,CAAAA,CAAa,EAAE;IAC3C,OAAOA,CAAAA,CAAE7F,IAAI,KAAK,SAAa6F,IAAAA,CAAAA,CAAE7F,IAAI,KAAK,OAAA,IAAW6F,CAAE7F,CAAAA,IAAI,KAAK,aAAA,CAAA;AAClE;;AC5ZA;;;AAGC,IAEM,MAAM8F,EAAKC,GAAAA,IAAAA,CAAKD,GAAG;AACnB,MAAME,GAAM,GAAA,CAAA,GAAIF,GAAG;AACnB,MAAMG,KAAQD,GAAAA,GAAAA,GAAMF,GAAG;AACjBI,MAAAA,QAAAA,GAAW1F,MAAO2F,CAAAA,kBAAkB;AAC1C,MAAMC,WAAcN,GAAAA,EAAAA,GAAK,IAAI;AAC7B,MAAMO,OAAUP,GAAAA,EAAAA,GAAK,EAAE;AACvB,MAAMQ,UAAaR,GAAAA,EAAAA,GAAK,EAAE;AACpBS,MAAAA,aAAAA,GAAgBT,EAAK,GAAA,CAAA,GAAI,EAAE;AAE3BU,MAAAA,KAAAA,GAAQT,IAAKS,CAAAA,MAAM;AACnBC,MAAAA,IAAAA,GAAOV,IAAKU,CAAAA,KAAK;AAEvB,SAASC,YAAavC,CAAAA,CAAS,EAAEE,CAAS,EAAEsC,OAAe,EAAE;AAClE,IAAA,OAAOZ,IAAKa,CAAAA,GAAG,CAACzC,CAAAA,GAAIE,CAAKsC,CAAAA,GAAAA,OAAAA,CAAAA;AAC3B,CAAC;AAED;;AAEC,IACM,SAASE,OAAQC,CAAAA,KAAa,EAAE;IACrC,MAAMC,YAAAA,GAAehB,IAAKiB,CAAAA,KAAK,CAACF,KAAAA,CAAAA,CAAAA;AAChCA,IAAAA,KAAAA,GAAQJ,aAAaI,KAAOC,EAAAA,YAAAA,EAAcD,KAAQ,GAAA,IAAA,CAAA,GAAQC,eAAeD,KAAK,CAAA;IAC9E,MAAMG,SAAAA,GAAYlB,KAAKmB,GAAG,CAAC,IAAInB,IAAKoB,CAAAA,KAAK,CAACX,KAAMM,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAAA;AAChD,IAAA,MAAMM,WAAWN,KAAQG,GAAAA,SAAAA,CAAAA;IACzB,MAAMI,YAAAA,GAAeD,QAAY,IAAA,CAAA,GAAI,CAAIA,GAAAA,QAAAA,IAAY,IAAI,CAAIA,GAAAA,QAAAA,IAAY,CAAI,GAAA,CAAA,GAAI,EAAE,CAAA;AACnF,IAAA,OAAOC,YAAeJ,GAAAA,SAAAA,CAAAA;AACxB,CAAC;AAED;;;AAGC,IACM,SAASK,UAAWzH,CAAAA,KAAa,EAAE;AACxC,IAAA,MAAM0H,SAAmB,EAAE,CAAA;IAC3B,MAAMC,IAAAA,GAAOzB,IAAKyB,CAAAA,IAAI,CAAC3H,KAAAA,CAAAA,CAAAA;IACvB,IAAI6B,CAAAA,CAAAA;AAEJ,IAAA,IAAKA,CAAI,GAAA,CAAA,EAAGA,CAAI8F,GAAAA,IAAAA,EAAM9F,CAAK,EAAA,CAAA;QACzB,IAAI7B,KAAAA,GAAQ6B,MAAM,CAAG,EAAA;AACnB6F,YAAAA,MAAAA,CAAO5C,IAAI,CAACjD,CAAAA,CAAAA,CAAAA;YACZ6F,MAAO5C,CAAAA,IAAI,CAAC9E,KAAQ6B,GAAAA,CAAAA,CAAAA,CAAAA;SACrB;AACH,KAAA;AACA,IAAA,IAAI8F,IAAUA,MAAAA,IAAO,GAAA,CAAA,CAAI,EAAA;AACvBD,QAAAA,MAAAA,CAAO5C,IAAI,CAAC6C,IAAAA,CAAAA,CAAAA;KACb;AAEDD,IAAAA,MAAAA,CAAOE,IAAI,CAAC,CAAClC,GAAGC,CAAMD,GAAAA,CAAAA,GAAIC,GAAGkC,GAAG,EAAA,CAAA;IAChC,OAAOH,MAAAA,CAAAA;AACT,CAAC;AAEM,SAASI,QAASC,CAAAA,CAAU,EAAe;AAChD,IAAA,OAAO,CAACC,KAAAA,CAAM7G,UAAW4G,CAAAA,CAAAA,CAAAA,CAAAA,IAAiBnH,QAASmH,CAAAA,CAAAA,CAAAA,CAAAA;AACrD,CAAC;AAEM,SAASE,WAAAA,CAAY3D,CAAS,EAAEwC,OAAe,EAAE;IACtD,MAAMoB,OAAAA,GAAUhC,IAAKiB,CAAAA,KAAK,CAAC7C,CAAAA,CAAAA,CAAAA;AAC3B,IAAA,OAAO,OAAYwC,GAAAA,OAAAA,IAAYxC,CAAO,IAAC4D,UAAUpB,OAAYxC,IAAAA,CAAAA,CAAAA;AAC/D,CAAC;AAED;;IAGO,SAAS6D,kBACdC,CAAAA,KAA+B,EAC/BxF,MAAoC,EACpCyF,QAAgB,EAChB;AACA,IAAA,IAAIxG,GAAWO,IAAcpC,EAAAA,KAAAA,CAAAA;IAE7B,IAAK6B,CAAAA,GAAI,GAAGO,IAAOgG,GAAAA,KAAAA,CAAMpG,MAAM,EAAEH,CAAAA,GAAIO,MAAMP,CAAK,EAAA,CAAA;AAC9C7B,QAAAA,KAAAA,GAAQoI,KAAK,CAACvG,CAAE,CAAA,CAACwG,QAAS,CAAA,CAAA;QAC1B,IAAI,CAACL,MAAMhI,KAAQ,CAAA,EAAA;AACjB4C,YAAAA,MAAAA,CAAO0F,GAAG,GAAGpC,IAAAA,CAAKoC,GAAG,CAAC1F,MAAAA,CAAO0F,GAAG,EAAEtI,KAAAA,CAAAA,CAAAA;AAClC4C,YAAAA,MAAAA,CAAO2F,GAAG,GAAGrC,IAAAA,CAAKqC,GAAG,CAAC3F,MAAAA,CAAO2F,GAAG,EAAEvI,KAAAA,CAAAA,CAAAA;SACnC;AACH,KAAA;AACF,CAAC;AAEM,SAASwI,SAAUC,CAAAA,OAAe,EAAE;IACzC,OAAOA,OAAAA,IAAWxC,EAAAA,GAAK,GAAE,CAAA,CAAA;AAC3B,CAAC;AAEM,SAASyC,SAAUC,CAAAA,OAAe,EAAE;IACzC,OAAOA,OAAAA,IAAW,GAAA,GAAM1C,EAAC,CAAA,CAAA;AAC3B,CAAC;AAED;;;;;;AAMC,IACM,SAAS2C,cAAetE,CAAAA,CAAS,EAAE;IACxC,IAAI,CAACuE,eAAevE,CAAI,CAAA,EAAA;AACtB,QAAA,OAAA;KACD;AACD,IAAA,IAAI0B,CAAI,GAAA,CAAA,CAAA;AACR,IAAA,IAAI8C,CAAI,GAAA,CAAA,CAAA;AACR,IAAA,MAAO5C,KAAKiB,KAAK,CAAC7C,CAAI0B,GAAAA,CAAAA,CAAAA,GAAKA,MAAM1B,CAAG,CAAA;QAClC0B,CAAK,IAAA,EAAA,CAAA;AACL8C,QAAAA,CAAAA,EAAAA,CAAAA;AACF,KAAA;IACA,OAAOA,CAAAA,CAAAA;AACT,CAAC;AAED;AACO,SAASC,iBAAAA,CACdC,WAAkB,EAClBC,UAAiB,EACjB;AACA,IAAA,MAAMC,mBAAsBD,GAAAA,UAAAA,CAAW3E,CAAC,GAAG0E,YAAY1E,CAAC,CAAA;AACxD,IAAA,MAAM6E,mBAAsBF,GAAAA,UAAAA,CAAWzE,CAAC,GAAGwE,YAAYxE,CAAC,CAAA;AACxD,IAAA,MAAM4E,2BAA2BlD,IAAKyB,CAAAA,IAAI,CAACuB,mBAAAA,GAAsBA,sBAAsBC,mBAAsBA,GAAAA,mBAAAA,CAAAA,CAAAA;AAE7G,IAAA,IAAIE,KAAQnD,GAAAA,IAAAA,CAAKoD,KAAK,CAACH,mBAAqBD,EAAAA,mBAAAA,CAAAA,CAAAA;IAE5C,IAAIG,KAAAA,GAAS,CAAC,GAAA,GAAMpD,EAAK,EAAA;AACvBoD,QAAAA,KAAAA,IAASlD;KACV;IAED,OAAO;AACLkD,QAAAA,KAAAA;QACAE,QAAUH,EAAAA,wBAAAA;AACZ,KAAA,CAAA;AACF,CAAC;AAEM,SAASI,qBAAAA,CAAsBC,GAAU,EAAEC,GAAU,EAAE;IAC5D,OAAOxD,IAAAA,CAAKyB,IAAI,CAACzB,IAAAA,CAAKmB,GAAG,CAACqC,GAAAA,CAAIpF,CAAC,GAAGmF,GAAAA,CAAInF,CAAC,EAAE,CAAA,CAAA,GAAK4B,KAAKmB,GAAG,CAACqC,IAAIlF,CAAC,GAAGiF,GAAIjF,CAAAA,CAAC,EAAE,CAAA,CAAA,CAAA,CAAA;AACxE,CAAC;AAED;;;AAGC,IACM,SAASmF,UAAAA,CAAWjE,CAAS,EAAEC,CAAS,EAAE;AAC/C,IAAA,OAAO,CAACD,CAAAA,GAAIC,CAAIS,GAAAA,KAAI,IAAKD,GAAMF,GAAAA,EAAAA,CAAAA;AACjC,CAAC;AAED;;;AAGC,IACM,SAAS2D,eAAgBlE,CAAAA,CAAS,EAAE;AACzC,IAAA,OAAO,CAACA,CAAIS,GAAAA,GAAAA,GAAMA,GAAE,IAAKA,GAAAA,CAAAA;AAC3B,CAAC;AAED;;IAGO,SAAS0D,aAAAA,CAAcR,KAAa,EAAES,KAAa,EAAEC,GAAW,EAAEC,qBAA+B,EAAE;AACxG,IAAA,MAAMtE,IAAIkE,eAAgBP,CAAAA,KAAAA,CAAAA,CAAAA;AAC1B,IAAA,MAAMY,IAAIL,eAAgBE,CAAAA,KAAAA,CAAAA,CAAAA;AAC1B,IAAA,MAAM9D,IAAI4D,eAAgBG,CAAAA,GAAAA,CAAAA,CAAAA;IAC1B,MAAMG,YAAAA,GAAeN,gBAAgBK,CAAIvE,GAAAA,CAAAA,CAAAA,CAAAA;IACzC,MAAMyE,UAAAA,GAAaP,gBAAgB5D,CAAIN,GAAAA,CAAAA,CAAAA,CAAAA;IACvC,MAAM0E,YAAAA,GAAeR,gBAAgBlE,CAAIuE,GAAAA,CAAAA,CAAAA,CAAAA;IACzC,MAAMI,UAAAA,GAAaT,gBAAgBlE,CAAIM,GAAAA,CAAAA,CAAAA,CAAAA;IACvC,OAAON,CAAAA,KAAMuE,KAAKvE,CAAMM,KAAAA,CAAAA,IAAMgE,yBAAyBC,CAAMjE,KAAAA,CAAAA,IACvDkE,YAAeC,GAAAA,UAAAA,IAAcC,YAAeC,GAAAA,UAAAA,CAAAA;AACpD,CAAC;AAED;;;;;;IAOO,SAASC,WAAYtK,CAAAA,KAAa,EAAEsI,GAAW,EAAEC,GAAW,EAAE;AACnE,IAAA,OAAOrC,KAAKqC,GAAG,CAACD,KAAKpC,IAAKoC,CAAAA,GAAG,CAACC,GAAKvI,EAAAA,KAAAA,CAAAA,CAAAA,CAAAA;AACrC,CAAC;AAED;;;AAGC,IACM,SAASuK,WAAYvK,CAAAA,KAAa,EAAE;IACzC,OAAOsK,WAAAA,CAAYtK,KAAO,EAAA,CAAC,KAAO,EAAA,KAAA,CAAA,CAAA;AACpC,CAAC;AAED;;;;;;IAOO,SAASwK,UAAAA,CAAWxK,KAAa,EAAE8J,KAAa,EAAEC,GAAW,EAAEjD,OAAU,GAAA,IAAI,EAAE;AACpF,IAAA,OAAO9G,KAASkG,IAAAA,IAAAA,CAAKoC,GAAG,CAACwB,KAAOC,EAAAA,GAAAA,CAAAA,GAAOjD,OAAW9G,IAAAA,KAAAA,IAASkG,IAAKqC,CAAAA,GAAG,CAACuB,KAAAA,EAAOC,GAAOjD,CAAAA,GAAAA,OAAAA,CAAAA;AACpF;;ACpLO,SAAS2D,OACdC,CAAAA,KAAgB,EAChB1K,KAAa,EACb2K,GAAgC,EAChC;IACAA,GAAMA,GAAAA,GAAAA,KAAQ,CAACnI,KAAAA,GAAUkI,KAAK,CAAClI,KAAAA,CAAM,GAAGxC,KAAI,CAAA,CAAA;IAC5C,IAAI4K,EAAAA,GAAKF,KAAM1I,CAAAA,MAAM,GAAG,CAAA,CAAA;AACxB,IAAA,IAAI6I,EAAK,GAAA,CAAA,CAAA;IACT,IAAIC,GAAAA,CAAAA;IAEJ,MAAOF,EAAAA,GAAKC,KAAK,CAAG,CAAA;QAClBC,GAAM,GAACD,KAAKD,EAAO,IAAA,CAAA,CAAA;AACnB,QAAA,IAAID,IAAIG,GAAM,CAAA,EAAA;YACZD,EAAKC,GAAAA,GAAAA,CAAAA;SACA,MAAA;YACLF,EAAKE,GAAAA,GAAAA,CAAAA;SACN;AACH,KAAA;IAEA,OAAO;AAACD,QAAAA,EAAAA;AAAID,QAAAA,EAAAA;AAAE,KAAA,CAAA;AAChB,CAAC;AAED;;;;;;;AAOC,IACM,MAAMG,YAAe,GAAA,CAC1BL,KACAzH,EAAAA,GAAAA,EACAjD,KACAgL,EAAAA,IAAAA,GAEAP,OAAQC,CAAAA,KAAAA,EAAO1K,KAAOgL,EAAAA,IAAAA,GAClBxI,CAAAA,KAAS,GAAA;AACT,QAAA,MAAMyI,EAAKP,GAAAA,KAAK,CAAClI,KAAAA,CAAM,CAACS,GAAI,CAAA,CAAA;QAC5B,OAAOgI,EAAAA,GAAKjL,KAASiL,IAAAA,EAAAA,KAAOjL,KAAS0K,IAAAA,KAAK,CAAClI,KAAQ,GAAA,CAAA,CAAE,CAACS,GAAAA,CAAI,KAAKjD,KAAAA,CAAAA;KAE/DwC,GAAAA,CAAAA,QAASkI,KAAK,CAAClI,MAAM,CAACS,GAAAA,CAAI,GAAGjD,KAAK,EAAE;AAE1C;;;;;;AAMC,IACYkL,MAAAA,aAAAA,GAAgB,CAC3BR,KACAzH,EAAAA,GAAAA,EACAjD,QAEAyK,OAAQC,CAAAA,KAAAA,EAAO1K,KAAOwC,EAAAA,CAAAA,QAASkI,KAAK,CAAClI,MAAM,CAACS,GAAAA,CAAI,IAAIjD,KAAO,EAAA;AAE7D;;;;;;IAOO,SAASmL,cAAeC,CAAAA,MAAgB,EAAE9C,GAAW,EAAEC,GAAW,EAAE;AACzE,IAAA,IAAIuB,KAAQ,GAAA,CAAA,CAAA;IACZ,IAAIC,GAAAA,GAAMqB,OAAOpJ,MAAM,CAAA;AAEvB,IAAA,MAAO8H,QAAQC,GAAOqB,IAAAA,MAAM,CAACtB,KAAAA,CAAM,GAAGxB,GAAK,CAAA;AACzCwB,QAAAA,KAAAA,EAAAA,CAAAA;AACF,KAAA;AACA,IAAA,MAAOC,MAAMD,KAASsB,IAAAA,MAAM,CAACrB,GAAM,GAAA,CAAA,CAAE,GAAGxB,GAAK,CAAA;AAC3CwB,QAAAA,GAAAA,EAAAA,CAAAA;AACF,KAAA;IAEA,OAAOD,KAAAA,GAAQ,CAAKC,IAAAA,GAAAA,GAAMqB,MAAOpJ,CAAAA,MAAM,GACnCoJ,MAAAA,CAAO5K,KAAK,CAACsJ,KAAOC,EAAAA,GAAAA,CAAAA,GACpBqB,MAAM,CAAA;AACZ,CAAC;AAED,MAAMC,WAAc,GAAA;AAAC,IAAA,MAAA;AAAQ,IAAA,KAAA;AAAO,IAAA,OAAA;AAAS,IAAA,QAAA;AAAU,IAAA,SAAA;AAAU,CAAA,CAAA;AAgB1D,SAASC,iBAAAA,CAAkBlD,KAAK,EAAEmD,QAAQ,EAAE;IACjD,IAAInD,KAAAA,CAAMoD,QAAQ,EAAE;AAClBpD,QAAAA,KAAAA,CAAMoD,QAAQ,CAACC,SAAS,CAAC3G,IAAI,CAACyG,QAAAA,CAAAA,CAAAA;AAC9B,QAAA,OAAA;KACD;IAEDnL,MAAOsL,CAAAA,cAAc,CAACtD,KAAAA,EAAO,UAAY,EAAA;AACvCuD,QAAAA,YAAAA,EAAc,IAAI;AAClBC,QAAAA,UAAAA,EAAY,KAAK;QACjB5L,KAAO,EAAA;YACLyL,SAAW,EAAA;AAACF,gBAAAA,QAAAA;AAAS,aAAA;AACvB,SAAA;AACF,KAAA,CAAA,CAAA;IAEAF,WAAYQ,CAAAA,OAAO,CAAC,CAAC5I,GAAQ,GAAA;QAC3B,MAAM6I,MAAAA,GAAS,YAAY3G,WAAYlC,CAAAA,GAAAA,CAAAA,CAAAA;QACvC,MAAM8I,IAAAA,GAAO3D,KAAK,CAACnF,GAAI,CAAA,CAAA;QAEvB7C,MAAOsL,CAAAA,cAAc,CAACtD,KAAAA,EAAOnF,GAAK,EAAA;AAChC0I,YAAAA,YAAAA,EAAc,IAAI;AAClBC,YAAAA,UAAAA,EAAY,KAAK;YACjB5L,KAAM,CAAA,CAAA,GAAGuB,IAAI,EAAE;AACb,gBAAA,MAAMyK,GAAMD,GAAAA,IAAAA,CAAKtK,KAAK,CAAC,IAAI,EAAEF,IAAAA,CAAAA,CAAAA;AAE7B6G,gBAAAA,KAAAA,CAAMoD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAC,CAACI,MAAW,GAAA;AAC3C,oBAAA,IAAI,OAAOA,MAAM,CAACH,MAAAA,CAAO,KAAK,UAAY,EAAA;wBACxCG,MAAM,CAACH,OAAO,CAAIvK,GAAAA,IAAAA,CAAAA,CAAAA;qBACnB;AACH,iBAAA,CAAA,CAAA;gBAEA,OAAOyK,GAAAA,CAAAA;AACT,aAAA;AACF,SAAA,CAAA,CAAA;AACF,KAAA,CAAA,CAAA;AACF,CAAC;AAQM,SAASE,mBAAAA,CAAoB9D,KAAK,EAAEmD,QAAQ,EAAE;IACnD,MAAMY,IAAAA,GAAO/D,MAAMoD,QAAQ,CAAA;AAC3B,IAAA,IAAI,CAACW,IAAM,EAAA;AACT,QAAA,OAAA;KACD;IAED,MAAMV,SAAAA,GAAYU,KAAKV,SAAS,CAAA;IAChC,MAAMjJ,KAAAA,GAAQiJ,SAAUvI,CAAAA,OAAO,CAACqI,QAAAA,CAAAA,CAAAA;IAChC,IAAI/I,KAAAA,KAAU,CAAC,CAAG,EAAA;QAChBiJ,SAAUW,CAAAA,MAAM,CAAC5J,KAAO,EAAA,CAAA,CAAA,CAAA;KACzB;IAED,IAAIiJ,SAAAA,CAAUzJ,MAAM,GAAG,CAAG,EAAA;AACxB,QAAA,OAAA;KACD;IAEDqJ,WAAYQ,CAAAA,OAAO,CAAC,CAAC5I,GAAQ,GAAA;QAC3B,OAAOmF,KAAK,CAACnF,GAAI,CAAA,CAAA;AACnB,KAAA,CAAA,CAAA;AAEA,IAAA,OAAOmF,MAAMoD,QAAQ,CAAA;AACvB,CAAC;AAED;;AAEC,IACM,SAASa,YAAgBC,CAAAA,KAAU,EAAE;IAC1C,MAAMC,GAAAA,GAAM,IAAIC,GAAOF,CAAAA,KAAAA,CAAAA,CAAAA;AAEvB,IAAA,IAAIC,GAAI3G,CAAAA,IAAI,KAAK0G,KAAAA,CAAMtK,MAAM,EAAE;QAC7B,OAAOsK,KAAAA,CAAAA;KACR;IAED,OAAOpM,KAAAA,CAAMuM,IAAI,CAACF,GAAAA,CAAAA,CAAAA;AACpB;;AC1LO,SAASG,UAAWC,CAAAA,SAAiB,EAAEC,SAAiB,EAAEC,UAAkB,EAAE;IACnF,OAAOD,SAAAA,GAAY,GAAMD,GAAAA,SAAAA,GAAY,KAAQE,GAAAA,UAAAA,CAAAA;AAC/C,CAAC;AAED;;AAEA,GACaC,MAAAA,gBAAAA,GAAoB,WAAW;IAC1C,IAAI,OAAOC,WAAW,WAAa,EAAA;QACjC,OAAO,SAAS1L,QAAQ,EAAE;YACxB,OAAOA,QAAAA,EAAAA,CAAAA;AACT,SAAA,CAAA;KACD;AACD,IAAA,OAAO0L,OAAOC,qBAAqB,CAAA;AACrC,CAAK,GAAA;AAEL;;;AAGC,IACM,SAASC,SAAAA,CACd3L,EAA4B,EAC5BE,OAAY,EACZ;AACA,IAAA,IAAI0L,YAAY,EAAE,CAAA;AAClB,IAAA,IAAIC,UAAU,KAAK,CAAA;IAEnB,OAAO,SAAS,GAAG5L,IAAW,EAAE;;QAE9B2L,SAAY3L,GAAAA,IAAAA,CAAAA;AACZ,QAAA,IAAI,CAAC4L,OAAS,EAAA;AACZA,YAAAA,OAAAA,GAAU,IAAI,CAAA;YACdL,gBAAiBvM,CAAAA,IAAI,CAACwM,MAAAA,EAAQ,IAAM;AAClCI,gBAAAA,OAAAA,GAAU,KAAK,CAAA;gBACf7L,EAAGG,CAAAA,KAAK,CAACD,OAAS0L,EAAAA,SAAAA,CAAAA,CAAAA;AACpB,aAAA,CAAA,CAAA;SACD;AACH,KAAA,CAAA;AACF,CAAC;AAED;;AAEC,IACM,SAASE,QAAAA,CAAmC9L,EAA4B,EAAE+L,KAAa,EAAE;IAC9F,IAAIC,OAAAA,CAAAA;IACJ,OAAO,SAAS,GAAG/L,IAAW,EAAE;AAC9B,QAAA,IAAI8L,KAAO,EAAA;YACTE,YAAaD,CAAAA,OAAAA,CAAAA,CAAAA;YACbA,OAAUE,GAAAA,UAAAA,CAAWlM,IAAI+L,KAAO9L,EAAAA,IAAAA,CAAAA,CAAAA;SAC3B,MAAA;YACLD,EAAGG,CAAAA,KAAK,CAAC,IAAI,EAAEF,IAAAA,CAAAA,CAAAA;SAChB;QACD,OAAO8L,KAAAA,CAAAA;AACT,KAAA,CAAA;AACF,CAAC;AAED;;;AAGC,IACM,MAAMI,kBAAqB,GAAA,CAACC,KAAsCA,GAAAA,KAAAA,KAAU,OAAU,GAAA,MAAA,GAASA,KAAU,KAAA,KAAA,GAAQ,OAAU,GAAA,SAAS;AAE3I;;;AAGC,IACYC,MAAAA,cAAAA,GAAiB,CAACD,KAAmC5D,EAAAA,KAAAA,EAAeC,MAAgB2D,KAAU,KAAA,OAAA,GAAU5D,QAAQ4D,KAAU,KAAA,KAAA,GAAQ3D,MAAM,CAACD,QAAQC,GAAE,IAAK,EAAE;AAEvK;;;AAGC,IACY6D,MAAAA,MAAAA,GAAS,CAACF,KAAoCG,EAAAA,IAAAA,EAAcC,OAAeC,GAAiB,GAAA;IACvG,MAAMC,KAAAA,GAAQD,GAAM,GAAA,MAAA,GAAS,OAAO,CAAA;IACpC,OAAOL,KAAAA,KAAUM,KAAQF,GAAAA,KAAAA,GAAQJ,KAAU,KAAA,QAAA,GAAW,CAACG,IAAOC,GAAAA,KAAI,IAAK,CAAA,GAAID,IAAI,CAAA;AACjF,EAAE;AAEF;;;IAIO,SAASI,gCAAiCC,CAAAA,IAAmC,EAAEC,MAAsB,EAAEC,kBAA2B,EAAE;IACzI,MAAMC,UAAAA,GAAaF,OAAOnM,MAAM,CAAA;AAEhC,IAAA,IAAI8H,KAAQ,GAAA,CAAA,CAAA;AACZ,IAAA,IAAIwE,KAAQD,GAAAA,UAAAA,CAAAA;IAEZ,IAAIH,IAAAA,CAAKK,OAAO,EAAE;AAChB,QAAA,MAAM,EAACC,MAAAA,GAAQC,OAAAA,GAAQ,GAAGP,IAAAA,CAAAA;QAC1B,MAAMQ,IAAAA,GAAOF,OAAOE,IAAI,CAAA;QACxB,MAAM,EAACpG,GAAG,GAAEC,GAAG,GAAEoG,UAAU,GAAEC,UAAU,GAAC,GAAGJ,MAAAA,CAAOK,aAAa,EAAA,CAAA;AAE/D,QAAA,IAAIF,UAAY,EAAA;AACd7E,YAAAA,KAAAA,GAAQQ,WAAYpE,CAAAA,IAAAA,CAAKoC,GAAG;AAE1ByC,YAAAA,YAAAA,CAAa0D,OAASC,EAAAA,IAAAA,EAAMpG,GAAKuC,CAAAA,CAAAA,EAAE;YAEnCuD,kBAAqBC,GAAAA,UAAAA,GAAatD,YAAaoD,CAAAA,MAAAA,EAAQO,IAAMF,EAAAA,MAAAA,CAAOM,gBAAgB,CAACxG,GAAMuC,CAAAA,CAAAA,CAAAA,EAAE,CAC/F,EAAA,CAAA,EAAGwD,UAAa,GAAA,CAAA,CAAA,CAAA;SACjB;AACD,QAAA,IAAIO,UAAY,EAAA;AACdN,YAAAA,KAAAA,GAAQhE,WAAYpE,CAAAA,IAAAA,CAAKqC,GAAG;YAE1BwC,YAAa0D,CAAAA,OAAAA,EAASD,MAAOE,CAAAA,IAAI,EAAEnG,GAAAA,EAAK,IAAI,CAAEqC,CAAAA,EAAE,GAAG,CAAA;AAEnDwD,YAAAA,kBAAAA,GAAqB,CAAIrD,GAAAA,YAAAA,CAAaoD,MAAQO,EAAAA,IAAAA,EAAMF,OAAOM,gBAAgB,CAACvG,GAAM,CAAA,EAAA,IAAI,EAAEqC,EAAE,GAAG,CAAC,CAAA,EAChGd,OAAOuE,UAAcvE,CAAAA,GAAAA,KAAAA,CAAAA;SAChB,MAAA;AACLwE,YAAAA,KAAAA,GAAQD,UAAavE,GAAAA,KAAAA,CAAAA;SACtB;KACF;IAED,OAAO;AAACA,QAAAA,KAAAA;AAAOwE,QAAAA,KAAAA;AAAK,KAAA,CAAA;AACtB,CAAC;AAED;;;;;AAKC,IACM,SAASS,mBAAoBb,CAAAA,IAAI,EAAE;AACxC,IAAA,MAAM,EAACc,MAAM,GAAEC,SAAQC,YAAAA,GAAa,GAAGhB,IAAAA,CAAAA;AACvC,IAAA,MAAMiB,SAAY,GAAA;AAChBC,QAAAA,IAAAA,EAAMJ,OAAO1G,GAAG;AAChB+G,QAAAA,IAAAA,EAAML,OAAOzG,GAAG;AAChB+G,QAAAA,IAAAA,EAAML,OAAO3G,GAAG;AAChBiH,QAAAA,IAAAA,EAAMN,OAAO1G,GAAG;AAClB,KAAA,CAAA;AACA,IAAA,IAAI,CAAC2G,YAAc,EAAA;AACjBhB,QAAAA,IAAAA,CAAKgB,YAAY,GAAGC,SAAAA,CAAAA;AACpB,QAAA,OAAO,IAAI,CAAA;KACZ;IACD,MAAMK,OAAAA,GAAUN,aAAaE,IAAI,KAAKJ,OAAO1G,GAAG,IAC7C4G,YAAaG,CAAAA,IAAI,KAAKL,MAAAA,CAAOzG,GAAG,IAChC2G,YAAAA,CAAaI,IAAI,KAAKL,MAAO3G,CAAAA,GAAG,IAChC4G,YAAaK,CAAAA,IAAI,KAAKN,MAAAA,CAAO1G,GAAG,CAAA;IAEnCnI,MAAOqP,CAAAA,MAAM,CAACP,YAAcC,EAAAA,SAAAA,CAAAA,CAAAA;IAC5B,OAAOK,OAAAA,CAAAA;AACT;;AC/IA,MAAME,MAAS,GAAA,CAACC,CAAcA,GAAAA,CAAAA,KAAM,KAAKA,CAAM,KAAA,CAAA,CAAA;AAC/C,MAAMC,SAAAA,GAAY,CAACD,CAAAA,EAAW1F,CAAWnB,EAAAA,CAAAA,GAAc,EAAE5C,IAAAA,CAAKmB,GAAG,CAAC,CAAG,EAAA,EAAA,IAAMsI,CAAK,IAAA,CAAA,CAAMzJ,CAAAA,GAAAA,IAAAA,CAAK2J,GAAG,CAAC,CAACF,CAAI1F,GAAAA,CAAAA,IAAK9D,GAAAA,GAAM2C,CAAC,CAAA,CAAA,CAAA;AAChH,MAAMgH,UAAAA,GAAa,CAACH,CAAW1F,EAAAA,CAAAA,EAAWnB,IAAc5C,IAAKmB,CAAAA,GAAG,CAAC,CAAG,EAAA,CAAC,KAAKsI,CAAKzJ,CAAAA,GAAAA,IAAAA,CAAK2J,GAAG,CAAEF,CAAAA,CAAI1F,GAAAA,CAAAA,IAAK9D,GAAAA,GAAM2C,CAAK,CAAA,GAAA,CAAA,CAAA;AAE7G;;;;AAIC,UACKiH,OAAU,GAAA;AACdC,IAAAA,MAAAA,EAAQ,CAACL,CAAcA,GAAAA,CAAAA;IAEvBM,UAAY,EAAA,CAACN,IAAcA,CAAIA,GAAAA,CAAAA;AAE/BO,IAAAA,WAAAA,EAAa,CAACP,CAAc,GAAA,CAACA,CAAKA,IAAAA,IAAI,CAAA,CAAA;IAEtCQ,aAAe,EAAA,CAACR,IAAgBA,CAAAA,CAAK,IAAA,GAAE,IAAK,CAAA,GACxC,GAAMA,GAAAA,CAAAA,GAAIA,IACV,CAAC,GAAA,IAAQ,EAAEA,CAAAA,IAAMA,CAAI,GAAA,CAAA,CAAK,GAAA,CAAA,CAAE;IAEhCS,WAAa,EAAA,CAACT,CAAcA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA;IAEpCU,YAAc,EAAA,CAACV,IAAc,CAACA,KAAK,CAAA,IAAKA,IAAIA,CAAI,GAAA,CAAA;IAEhDW,cAAgB,EAAA,CAACX,IAAgBA,CAAAA,CAAK,IAAA,GAAE,IAAK,CAAA,GACzC,GAAMA,GAAAA,CAAAA,GAAIA,IAAIA,CACd,GAAA,GAAA,IAAQA,CAAAA,CAAAA,IAAK,CAAA,IAAKA,CAAAA,GAAIA,CAAI,GAAA,CAAA,CAAE;AAEhCY,IAAAA,WAAAA,EAAa,CAACZ,CAAAA,GAAcA,CAAIA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA;AAExCa,IAAAA,YAAAA,EAAc,CAACb,CAAAA,GAAc,EAAE,CAACA,CAAK,IAAA,CAAA,IAAKA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAI,CAAA,CAAA;IAEtDc,cAAgB,EAAA,CAACd,CAAc,GAAC,CAACA,CAAK,IAAA,GAAE,IAAK,CAAA,GACzC,GAAMA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,IAClB,CAAC,GAAA,IAAQA,CAAAA,CAAAA,IAAK,CAAA,IAAKA,CAAIA,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,CAAE;AAErCe,IAAAA,WAAAA,EAAa,CAACf,CAAAA,GAAcA,CAAIA,GAAAA,CAAAA,GAAIA,IAAIA,CAAIA,GAAAA,CAAAA;IAE5CgB,YAAc,EAAA,CAAChB,CAAc,GAACA,CAAAA,CAAAA,IAAK,CAAA,IAAKA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA;IAExDiB,cAAgB,EAAA,CAACjB,CAAc,GAAC,CAACA,CAAK,IAAA,GAAE,IAAK,CAAA,GACzC,GAAMA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GACtB,GAAO,IAAA,CAACA,CAAK,IAAA,CAAA,IAAKA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,CAAE;AAExCkB,IAAAA,UAAAA,EAAY,CAAClB,CAAc,GAAA,CAACzJ,KAAK4K,GAAG,CAACnB,IAAInJ,OAAW,CAAA,GAAA,CAAA;AAEpDuK,IAAAA,WAAAA,EAAa,CAACpB,CAAAA,GAAczJ,IAAK2J,CAAAA,GAAG,CAACF,CAAInJ,GAAAA,OAAAA,CAAAA;IAEzCwK,aAAe,EAAA,CAACrB,CAAc,GAAA,CAAC,GAAOzJ,IAAAA,KAAK4K,GAAG,CAAC7K,EAAK0J,GAAAA,CAAAA,CAAAA,GAAK,CAAA,CAAA;AAEzDsB,IAAAA,UAAAA,EAAY,CAACtB,CAAAA,GAAc,CAACA,KAAM,IAAK,CAAIzJ,GAAAA,IAAAA,CAAKmB,GAAG,CAAC,CAAG,EAAA,EAAA,IAAMsI,CAAAA,GAAI,CAAA,CAAG,CAAA;AAEpEuB,IAAAA,WAAAA,EAAa,CAACvB,CAAAA,GAAc,CAACA,KAAM,IAAK,CAAI,GAAA,CAACzJ,IAAKmB,CAAAA,GAAG,CAAC,CAAA,EAAG,CAAC,EAAA,GAAKsI,KAAK,CAAC;AAErEwB,IAAAA,aAAAA,EAAe,CAACxB,CAAAA,GAAcD,MAAOC,CAAAA,CAAAA,CAAAA,GAAKA,IAAIA,CAAI,GAAA,GAAA,GAC9C,GAAMzJ,GAAAA,IAAAA,CAAKmB,GAAG,CAAC,CAAG,EAAA,EAAA,IAAMsI,CAAI,GAAA,CAAA,GAAI,CAAA,CAAA,CAAA,GAChC,GAAO,IAAA,CAACzJ,IAAAA,CAAKmB,GAAG,CAAC,CAAA,EAAG,CAAC,EAAA,IAAMsI,CAAI,GAAA,CAAA,GAAI,CAAA,CAAA,CAAA,GAAM,CAAA,CAAE;AAE/CyB,IAAAA,UAAAA,EAAY,CAACzB,CAAAA,GAAc,CAACA,IAAK,IAAKA,CAAI,GAAA,EAAEzJ,IAAAA,CAAKyB,IAAI,CAAC,CAAA,GAAIgI,CAAIA,GAAAA,CAAAA,CAAAA,GAAK,CAAA,CAAE;IAErE0B,WAAa,EAAA,CAAC1B,CAAczJ,GAAAA,IAAAA,CAAKyB,IAAI,CAAC,IAAI,CAACgI,CAAK,IAAA,CAAA,IAAKA,CAAAA,CAAAA;AAErD2B,IAAAA,aAAAA,EAAe,CAAC3B,CAAAA,GAAc,CAAEA,CAAK,IAAA,GAAE,IAAK,CAAA,GACxC,CAAC,GAAA,IAAOzJ,IAAAA,CAAKyB,IAAI,CAAC,CAAA,GAAIgI,CAAIA,GAAAA,CAAAA,CAAAA,GAAK,CAAA,CAAA,GAC/B,GAAOzJ,IAAAA,KAAKyB,IAAI,CAAC,CAAI,GAACgI,CAAAA,CAAK,IAAA,CAAA,IAAKA,CAAAA,CAAAA,GAAK,CAAA,CAAE;IAE3C4B,aAAe,EAAA,CAAC5B,IAAcD,MAAOC,CAAAA,CAAAA,CAAAA,GAAKA,IAAIC,SAAUD,CAAAA,CAAAA,EAAG,OAAO,GAAI,CAAA;IAEtE6B,cAAgB,EAAA,CAAC7B,IAAcD,MAAOC,CAAAA,CAAAA,CAAAA,GAAKA,IAAIG,UAAWH,CAAAA,CAAAA,EAAG,OAAO,GAAI,CAAA;AAExE8B,IAAAA,gBAAAA,CAAAA,CAAiB9B,CAAS,EAAE;AAC1B,QAAA,MAAM1F,CAAI,GAAA,MAAA,CAAA;AACV,QAAA,MAAMnB,CAAI,GAAA,IAAA,CAAA;AACV,QAAA,OAAO4G,OAAOC,CAAKA,CAAAA,GAAAA,CAAAA,GACjBA,IAAI,GACA,GAAA,GAAA,GAAMC,UAAUD,CAAI,GAAA,CAAA,EAAG1F,CAAGnB,EAAAA,CAAAA,CAAAA,GAC1B,MAAM,GAAMgH,GAAAA,UAAAA,CAAWH,IAAI,CAAI,GAAA,CAAA,EAAG1F,GAAGnB,CAAE,CAAA,CAAA;AAC/C,KAAA;AAEA4I,IAAAA,UAAAA,CAAAA,CAAW/B,CAAS,EAAE;AACpB,QAAA,MAAM1F,CAAI,GAAA,OAAA,CAAA;QACV,OAAO0F,CAAAA,GAAIA,KAAM1F,CAAAA,CAAI,GAAA,CAAA,IAAK0F,CAAAA,GAAI1F,CAAAA,CAAAA,CAAAA;AAChC,KAAA;AAEA0H,IAAAA,WAAAA,CAAAA,CAAYhC,CAAS,EAAE;AACrB,QAAA,MAAM1F,CAAI,GAAA,OAAA,CAAA;AACV,QAAA,OAAO,CAAC0F,CAAK,IAAA,CAAA,IAAKA,CAAK,IAAA,CAAC1F,CAAI,GAAA,CAAA,IAAK0F,CAAAA,GAAI1F,CAAAA,CAAK,GAAA,CAAA,CAAA;AAC5C,KAAA;AAEA2H,IAAAA,aAAAA,CAAAA,CAAcjC,CAAS,EAAE;AACvB,QAAA,IAAI1F,CAAI,GAAA,OAAA,CAAA;AACR,QAAA,IAAI,CAAC0F,CAAK,IAAA,GAAE,IAAK,CAAG,EAAA;AAClB,YAAA,OAAO,OAAOA,CAAAA,GAAIA,CAAK,IAAA,CAAE1F,CAAAA,CAAAA,IAAM,KAAK,IAAK,CAAA,IAAK0F,CAAAA,GAAI1F,CAAAA,CAAC,CAAA,CAAA;SACpD;QACD,OAAO,GAAA,IAAO,CAAC0F,KAAK,CAAA,IAAKA,KAAM,CAAA,CAAC1F,KAAM,KAAK,IAAK,CAAA,IAAK0F,CAAAA,GAAI1F,CAAAA,CAAAA,GAAK,CAAA,CAAA,CAAA;AAChE,KAAA;AAEA4H,IAAAA,YAAAA,EAAc,CAAClC,CAAc,GAAA,CAAA,GAAII,OAAQ+B,CAAAA,aAAa,CAAC,CAAInC,GAAAA,CAAAA,CAAAA;AAE3DmC,IAAAA,aAAAA,CAAAA,CAAcnC,CAAS,EAAE;AACvB,QAAA,MAAMoC,CAAI,GAAA,MAAA,CAAA;AACV,QAAA,MAAMC,CAAI,GAAA,IAAA,CAAA;QACV,IAAIrC,CAAAA,GAAK,IAAIqC,CAAI,EAAA;AACf,YAAA,OAAOD,IAAIpC,CAAIA,GAAAA,CAAAA,CAAAA;SAChB;QACD,IAAIA,CAAAA,GAAK,IAAIqC,CAAI,EAAA;AACf,YAAA,OAAOD,KAAKpC,CAAAA,IAAM,GAAMqC,GAAAA,CAAC,IAAKrC,CAAI,GAAA,IAAA,CAAA;SACnC;QACD,IAAIA,CAAAA,GAAK,MAAMqC,CAAI,EAAA;AACjB,YAAA,OAAOD,KAAKpC,CAAAA,IAAM,IAAOqC,GAAAA,CAAC,IAAKrC,CAAI,GAAA,MAAA,CAAA;SACpC;AACD,QAAA,OAAOoC,KAAKpC,CAAAA,IAAM,KAAQqC,GAAAA,CAAC,IAAKrC,CAAI,GAAA,QAAA,CAAA;AACtC,KAAA;AAEAsC,IAAAA,eAAAA,EAAiB,CAACtC,CAAc,GAACA,IAAI,GACjCI,GAAAA,OAAAA,CAAQ8B,YAAY,CAAClC,CAAAA,GAAI,CAAK,CAAA,GAAA,GAAA,GAC9BI,QAAQ+B,aAAa,CAACnC,IAAI,CAAI,GAAA,CAAA,CAAA,GAAK,MAAM,GAAG;AAClD;;ACrHO,SAASuC,mBAAoBlS,CAAAA,KAAc,EAA2C;IAC3F,IAAIA,KAAAA,IAAS,OAAOA,KAAAA,KAAU,QAAU,EAAA;QACtC,MAAMG,IAAAA,GAAOH,MAAMM,QAAQ,EAAA,CAAA;QAC3B,OAAOH,IAAAA,KAAS,4BAA4BA,IAAS,KAAA,yBAAA,CAAA;KACtD;AAED,IAAA,OAAO,KAAK,CAAA;AACd,CAAC;AAWM,SAASgS,KAAMnS,CAAAA,KAAK,EAAE;AAC3B,IAAA,OAAOkS,mBAAoBlS,CAAAA,KAAAA,CAAAA,GAASA,KAAQ,GAAA,IAAIoS,MAAMpS,KAAM,CAAA,CAAA;AAC9D,CAAC;AAKM,SAASqS,aAAcrS,CAAAA,KAAK,EAAE;AACnC,IAAA,OAAOkS,mBAAoBlS,CAAAA,KAAAA,CAAAA,GACvBA,KACA,GAAA,IAAIoS,KAAMpS,CAAAA,KAAAA,CAAAA,CAAOsS,QAAQ,CAAC,GAAKC,CAAAA,CAAAA,MAAM,CAAC,GAAA,CAAA,CAAKC,SAAS,EAAE,CAAA;AAC5D;;AC/BA,MAAMC,OAAU,GAAA;AAAC,IAAA,GAAA;AAAK,IAAA,GAAA;AAAK,IAAA,aAAA;AAAe,IAAA,QAAA;AAAU,IAAA,SAAA;AAAU,CAAA,CAAA;AAC9D,MAAMC,MAAS,GAAA;AAAC,IAAA,OAAA;AAAS,IAAA,aAAA;AAAe,IAAA,iBAAA;AAAkB,CAAA,CAAA;AAEnD,SAASC,uBAAwBC,CAAAA,QAAQ,EAAE;IAChDA,QAASrG,CAAAA,GAAG,CAAC,WAAa,EAAA;QACxBc,KAAOpJ,EAAAA,SAAAA;QACP4O,QAAU,EAAA,IAAA;QACVC,MAAQ,EAAA,cAAA;QACRxR,EAAI2C,EAAAA,SAAAA;QACJwI,IAAMxI,EAAAA,SAAAA;QACN8O,IAAM9O,EAAAA,SAAAA;QACN+O,EAAI/O,EAAAA,SAAAA;QACJ9D,IAAM8D,EAAAA,SAAAA;AACR,KAAA,CAAA,CAAA;IAEA2O,QAASK,CAAAA,QAAQ,CAAC,WAAa,EAAA;AAC7BC,QAAAA,SAAAA,EAAW,KAAK;AAChBC,QAAAA,UAAAA,EAAY,KAAK;AACjBC,QAAAA,WAAAA,EAAa,CAACC,IAASA,GAAAA,IAAAA,KAAS,YAAgBA,IAAAA,IAAAA,KAAS,gBAAgBA,IAAS,KAAA,IAAA;AACpF,KAAA,CAAA,CAAA;IAEAT,QAASrG,CAAAA,GAAG,CAAC,YAAc,EAAA;QACzBmG,MAAQ,EAAA;YACNvS,IAAM,EAAA,OAAA;YACNmT,UAAYZ,EAAAA,MAAAA;AACd,SAAA;QACAD,OAAS,EAAA;YACPtS,IAAM,EAAA,QAAA;YACNmT,UAAYb,EAAAA,OAAAA;AACd,SAAA;AACF,KAAA,CAAA,CAAA;IAEAG,QAASK,CAAAA,QAAQ,CAAC,YAAc,EAAA;QAC9BC,SAAW,EAAA,WAAA;AACb,KAAA,CAAA,CAAA;IAEAN,QAASrG,CAAAA,GAAG,CAAC,aAAe,EAAA;QAC1BgH,MAAQ,EAAA;YACNC,SAAW,EAAA;gBACTX,QAAU,EAAA,GAAA;AACZ,aAAA;AACF,SAAA;QACAY,MAAQ,EAAA;YACND,SAAW,EAAA;gBACTX,QAAU,EAAA,CAAA;AACZ,aAAA;AACF,SAAA;QACAa,IAAM,EAAA;YACJC,UAAY,EAAA;gBACVjB,MAAQ,EAAA;oBACNjG,IAAM,EAAA,aAAA;AACR,iBAAA;gBACAmH,OAAS,EAAA;oBACPzT,IAAM,EAAA,SAAA;AACN0S,oBAAAA,QAAAA,EAAU;AACZ,iBAAA;AACF,aAAA;AACF,SAAA;QACAgB,IAAM,EAAA;YACJF,UAAY,EAAA;gBACVjB,MAAQ,EAAA;oBACNM,EAAI,EAAA,aAAA;AACN,iBAAA;gBACAY,OAAS,EAAA;oBACPzT,IAAM,EAAA,SAAA;oBACN2S,MAAQ,EAAA,QAAA;AACRxR,oBAAAA,EAAAA,EAAI+C,CAAAA,CAAAA,GAAKA,CAAI,GAAA,CAAA;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA,CAAA,CAAA;AACF;;ACvEO,SAASyP,oBAAqBlB,CAAAA,QAAQ,EAAE;IAC7CA,QAASrG,CAAAA,GAAG,CAAC,QAAU,EAAA;AACrBwH,QAAAA,WAAAA,EAAa,IAAI;QACjBC,OAAS,EAAA;YACPC,GAAK,EAAA,CAAA;YACLnG,KAAO,EAAA,CAAA;YACPoG,MAAQ,EAAA,CAAA;YACRrG,IAAM,EAAA,CAAA;AACR,SAAA;AACF,KAAA,CAAA,CAAA;AACF;;ACTA,MAAMsG,YAAY,IAAIC,GAAAA,EAAAA,CAAAA;AAEtB,SAASC,eAAgBC,CAAAA,MAAc,EAAElR,OAAkC,EAAE;AAC3EA,IAAAA,OAAAA,GAAUA,WAAW,EAAC,CAAA;AACtB,IAAA,MAAMmR,QAAWD,GAAAA,MAAAA,GAASE,IAAKC,CAAAA,SAAS,CAACrR,OAAAA,CAAAA,CAAAA;IACzC,IAAIsR,SAAAA,GAAYP,SAAUQ,CAAAA,GAAG,CAACJ,QAAAA,CAAAA,CAAAA;AAC9B,IAAA,IAAI,CAACG,SAAW,EAAA;AACdA,QAAAA,SAAAA,GAAY,IAAIE,IAAAA,CAAKC,YAAY,CAACP,MAAQlR,EAAAA,OAAAA,CAAAA,CAAAA;QAC1C+Q,SAAU5H,CAAAA,GAAG,CAACgI,QAAUG,EAAAA,SAAAA,CAAAA,CAAAA;KACzB;IACD,OAAOA,SAAAA,CAAAA;AACT,CAAA;AAEO,SAASI,YAAaC,CAAAA,GAAW,EAAET,MAAc,EAAElR,OAAkC,EAAE;AAC5F,IAAA,OAAOiR,eAAgBC,CAAAA,MAAAA,EAAQlR,OAAS4R,CAAAA,CAAAA,MAAM,CAACD,GAAAA,CAAAA,CAAAA;AACjD;;ACRA,MAAME,UAAa,GAAA;AAOjB7J,CAAAA,MAAAA,CAAAA,CAAOpL,KAAK,EAAE;AACZ,QAAA,OAAOC,QAAQD,KAAS,CAAA,IAAyBA,KAAAA,GAAS,KAAKA,KAAK,CAAA;AACtE,KAAA;AASC,CACDkV,SAAQC,SAAS,EAAE3S,KAAK,EAAE4S,KAAK,EAAE;AAC/B,QAAA,IAAID,cAAc,CAAG,EAAA;AACnB,YAAA,OAAO;SACR;AAED,QAAA,MAAMb,SAAS,IAAI,CAACe,KAAK,CAACjS,OAAO,CAACkR,MAAM,CAAA;QACxC,IAAIgB,QAAAA,CAAAA;QACJ,IAAIC,KAAAA,GAAQJ;QAEZ,IAAIC,KAAAA,CAAMpT,MAAM,GAAG,CAAG,EAAA;YAEpB,MAAMwT,OAAAA,GAAUtP,KAAKqC,GAAG,CAACrC,KAAKa,GAAG,CAACqO,KAAK,CAAC,CAAE,CAAA,CAACpV,KAAK,CAAGkG,EAAAA,IAAAA,CAAKa,GAAG,CAACqO,KAAK,CAACA,MAAMpT,MAAM,GAAG,CAAE,CAAA,CAAChC,KAAK,CAAA,CAAA,CAAA;YACzF,IAAIwV,OAAAA,GAAU,IAAQA,IAAAA,OAAAA,GAAU,KAAO,EAAA;gBACrCF,QAAW,GAAA,YAAA,CAAA;aACZ;AAEDC,YAAAA,KAAAA,GAAQE,eAAeN,SAAWC,EAAAA,KAAAA,CAAAA,CAAAA;SACnC;AAED,QAAA,MAAMM,QAAW/O,GAAAA,KAAAA,CAAMT,IAAKa,CAAAA,GAAG,CAACwO,KAAAA,CAAAA,CAAAA,CAAAA;AAOhC,QAAA,MAAMI,aAAa3N,KAAM0N,CAAAA,QAAAA,CAAAA,GAAY,CAAIxP,GAAAA,IAAAA,CAAKqC,GAAG,CAACrC,IAAAA,CAAKoC,GAAG,CAAC,CAAC,CAAIpC,GAAAA,IAAAA,CAAKoB,KAAK,CAACoO,QAAAA,CAAAA,EAAW,KAAK,CAAE,CAAA,CAAA;AAE7F,QAAA,MAAMtS,OAAU,GAAA;AAACkS,YAAAA,QAAAA;YAAUM,qBAAuBD,EAAAA,UAAAA;YAAYE,qBAAuBF,EAAAA,UAAAA;AAAU,SAAA,CAAA;QAC/FvV,MAAOqP,CAAAA,MAAM,CAACrM,OAAS,EAAA,IAAI,CAACA,OAAO,CAACgS,KAAK,CAACJ,MAAM,CAAA,CAAA;QAEhD,OAAOF,YAAAA,CAAaK,WAAWb,MAAQlR,EAAAA,OAAAA,CAAAA,CAAAA;AACzC,KAAA;AAUC,CACD0S,aAAYX,SAAS,EAAE3S,KAAK,EAAE4S,KAAK,EAAE;AACnC,QAAA,IAAID,cAAc,CAAG,EAAA;YACnB,OAAO,GAAA,CAAA;SACR;AACD,QAAA,MAAMY,MAASX,GAAAA,KAAK,CAAC5S,KAAAA,CAAM,CAACwT,WAAW,IAAKb,SAAajP,GAAAA,IAAAA,CAAKmB,GAAG,CAAC,EAAA,EAAInB,IAAKoB,CAAAA,KAAK,CAACX,KAAMwO,CAAAA,SAAAA,CAAAA,CAAAA,CAAAA,CAAAA;QACvF,IAAI;AAAC,YAAA,CAAA;AAAG,YAAA,CAAA;AAAG,YAAA,CAAA;AAAG,YAAA,CAAA;AAAG,YAAA,EAAA;AAAI,YAAA,EAAA;AAAG,SAAA,CAACc,QAAQ,CAACF,MAAAA,CAAAA,IAAWvT,QAAQ,GAAM4S,GAAAA,KAAAA,CAAMpT,MAAM,EAAE;YACvE,OAAOiT,UAAAA,CAAWC,OAAO,CAAC3U,IAAI,CAAC,IAAI,EAAE4U,WAAW3S,KAAO4S,EAAAA,KAAAA,CAAAA,CAAAA;SACxD;QACD,OAAO,EAAA,CAAA;AACT,KAAA;AAEF,CAAA,CAAA;AAGA,SAASK,cAAeN,CAAAA,SAAS,EAAEC,KAAK,EAAE;IAGxC,IAAIG,KAAAA,GAAQH,KAAMpT,CAAAA,MAAM,GAAG,CAAA,GAAIoT,KAAK,CAAC,CAAE,CAAA,CAACpV,KAAK,GAAGoV,KAAK,CAAC,CAAE,CAAA,CAACpV,KAAK,GAAGoV,KAAK,CAAC,CAAE,CAAA,CAACpV,KAAK,GAAGoV,KAAK,CAAC,CAAE,CAAA,CAACpV,KAAK,CAAA;IAGhG,IAAIkG,IAAAA,CAAKa,GAAG,CAACwO,KAAAA,CAAAA,IAAU,KAAKJ,SAAcjP,KAAAA,IAAAA,CAAKoB,KAAK,CAAC6N,SAAY,CAAA,EAAA;QAE/DI,KAAQJ,GAAAA,SAAAA,GAAYjP,IAAKoB,CAAAA,KAAK,CAAC6N,SAAAA,CAAAA,CAAAA;KAChC;IACD,OAAOI,KAAAA,CAAAA;AACT,CAAA;AAKC,CACD,YAAe;AAACN,IAAAA,UAAAA;AAAU,CAAE;;ACnGrB,SAASiB,kBAAmBtD,CAAAA,QAAQ,EAAE;IAC3CA,QAASrG,CAAAA,GAAG,CAAC,OAAS,EAAA;AACpB4J,QAAAA,OAAAA,EAAS,IAAI;AACbC,QAAAA,MAAAA,EAAQ,KAAK;AACbxU,QAAAA,OAAAA,EAAS,KAAK;AACdyU,QAAAA,WAAAA,EAAa,KAAK;AAQjB,CACDC,MAAQ,EAAA,OAAA;AAERC,QAAAA,IAAAA,EAAM,IAAI;AAKT,CACDC,KAAO,EAAA,CAAA;QAGPC,IAAM,EAAA;AACJN,YAAAA,OAAAA,EAAS,IAAI;YACbO,SAAW,EAAA,CAAA;AACXC,YAAAA,eAAAA,EAAiB,IAAI;AACrBC,YAAAA,SAAAA,EAAW,IAAI;YACfC,UAAY,EAAA,CAAA;AACZC,YAAAA,SAAAA,EAAW,CAACC,IAAAA,EAAM3T,OAAYA,GAAAA,OAAAA,CAAQsT,SAAS;AAC/CM,YAAAA,SAAAA,EAAW,CAACD,IAAAA,EAAM3T,OAAYA,GAAAA,OAAAA,CAAQ+O,KAAK;AAC3CiE,YAAAA,MAAAA,EAAQ,KAAK;AACf,SAAA;QAEAa,MAAQ,EAAA;AACNd,YAAAA,OAAAA,EAAS,IAAI;AACbe,YAAAA,IAAAA,EAAM,EAAE;YACRC,UAAY,EAAA,GAAA;YACZC,KAAO,EAAA,CAAA;AACT,SAAA;QAGAC,KAAO,EAAA;AAELlB,YAAAA,OAAAA,EAAS,KAAK;YAGdmB,IAAM,EAAA,EAAA;YAGNtD,OAAS,EAAA;gBACPC,GAAK,EAAA,CAAA;gBACLC,MAAQ,EAAA,CAAA;AACV,aAAA;AACF,SAAA;QAGAkB,KAAO,EAAA;YACLmC,WAAa,EAAA,CAAA;YACbC,WAAa,EAAA,EAAA;AACbC,YAAAA,MAAAA,EAAQ,KAAK;YACbC,eAAiB,EAAA,CAAA;YACjBC,eAAiB,EAAA,EAAA;YACjB3D,OAAS,EAAA,CAAA;AACTmC,YAAAA,OAAAA,EAAS,IAAI;AACbyB,YAAAA,QAAAA,EAAU,IAAI;YACdC,eAAiB,EAAA,CAAA;YACjBC,WAAa,EAAA,CAAA;YAEbzW,QAAU0W,EAAAA,KAAAA,CAAM9C,UAAU,CAAC7J,MAAM;AACjC4M,YAAAA,KAAAA,EAAO,EAAC;AACRC,YAAAA,KAAAA,EAAO,EAAC;YACRvK,KAAO,EAAA,QAAA;YACPwK,UAAY,EAAA,MAAA;AAEZC,YAAAA,iBAAAA,EAAmB,KAAK;YACxBC,aAAe,EAAA,2BAAA;YACfC,eAAiB,EAAA,CAAA;AACnB,SAAA;AACF,KAAA,CAAA,CAAA;AAEAzF,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,aAAe,EAAA,OAAA,EAAS,EAAI,EAAA,OAAA,CAAA,CAAA;AAC3C1F,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,YAAc,EAAA,OAAA,EAAS,EAAI,EAAA,aAAA,CAAA,CAAA;AAC1C1F,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,cAAgB,EAAA,OAAA,EAAS,EAAI,EAAA,aAAA,CAAA,CAAA;AAC5C1F,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,aAAe,EAAA,OAAA,EAAS,EAAI,EAAA,OAAA,CAAA,CAAA;IAE3C1F,QAASK,CAAAA,QAAQ,CAAC,OAAS,EAAA;AACzBC,QAAAA,SAAAA,EAAW,KAAK;AAChBE,QAAAA,WAAAA,EAAa,CAACC,IAAAA,GAAS,CAACA,IAAAA,CAAKkF,UAAU,CAAC,QAAA,CAAA,IAAa,CAAClF,IAAAA,CAAKkF,UAAU,CAAC,OAAYlF,CAAAA,IAAAA,IAAAA,KAAS,cAAcA,IAAS,KAAA,QAAA;AAClHF,QAAAA,UAAAA,EAAY,CAACE,IAASA,GAAAA,IAAAA,KAAS,YAAgBA,IAAAA,IAAAA,KAAS,oBAAoBA,IAAS,KAAA,MAAA;AACvF,KAAA,CAAA,CAAA;IAEAT,QAASK,CAAAA,QAAQ,CAAC,QAAU,EAAA;QAC1BC,SAAW,EAAA,OAAA;AACb,KAAA,CAAA,CAAA;IAEAN,QAASK,CAAAA,QAAQ,CAAC,aAAe,EAAA;AAC/BG,QAAAA,WAAAA,EAAa,CAACC,IAAAA,GAASA,IAAS,KAAA,iBAAA,IAAqBA,IAAS,KAAA,UAAA;QAC9DF,UAAY,EAAA,CAACE,OAASA,IAAS,KAAA,iBAAA;AACjC,KAAA,CAAA,CAAA;AACF;;MClGamF,SAAYpY,GAAAA,MAAAA,CAAOyC,MAAM,CAAC,IAAI,EAAE;MAChC4V,WAAcrY,GAAAA,MAAAA,CAAOyC,MAAM,CAAC,IAAI,EAAE;AAM9C,CACD,SAAS6V,UAAAA,CAASC,IAAI,EAAE1V,GAAG,EAAE;AAC3B,IAAA,IAAI,CAACA,GAAK,EAAA;QACR,OAAO0V,IAAAA,CAAAA;KACR;IACD,MAAM5W,IAAAA,GAAOkB,GAAI0B,CAAAA,KAAK,CAAC,GAAA,CAAA,CAAA;IACvB,IAAK,IAAI9C,CAAI,GAAA,CAAA,EAAGkG,CAAIhG,GAAAA,IAAAA,CAAKC,MAAM,EAAEH,CAAAA,GAAIkG,CAAG,EAAA,EAAElG,CAAG,CAAA;QAC3C,MAAMkB,CAAAA,GAAIhB,IAAI,CAACF,CAAE,CAAA,CAAA;AACjB8W,QAAAA,IAAAA,GAAOA,IAAI,CAAC5V,CAAE,CAAA,KAAK4V,IAAI,CAAC5V,CAAAA,CAAE,GAAG3C,MAAAA,CAAOyC,MAAM,CAAC,IAAI,CAAA,CAAA,CAAA;AACjD,KAAA;IACA,OAAO8V,IAAAA,CAAAA;AACT,CAAA;AAEA,SAASpM,IAAIqM,IAAI,EAAE7U,KAAK,EAAEqH,MAAM,EAAE;IAChC,IAAI,OAAOrH,UAAU,QAAU,EAAA;QAC7B,OAAOR,KAAAA,CAAMmV,UAASE,CAAAA,IAAAA,EAAM7U,KAAQqH,CAAAA,EAAAA,MAAAA,CAAAA,CAAAA;KACrC;IACD,OAAO7H,KAAAA,CAAMmV,UAASE,CAAAA,IAAAA,EAAM,EAAK7U,CAAAA,EAAAA,KAAAA,CAAAA,CAAAA;AACnC,CAAA;AAKC,CACM,MAAM8U,QAAAA,CAAAA;IACXC,WAAYC,CAAAA,YAAY,EAAEC,SAAS,CAAE;QACnC,IAAI,CAACxF,SAAS,GAAGvP,SAAAA,CAAAA;QACjB,IAAI,CAACgV,eAAe,GAAG,iBAAA,CAAA;QACvB,IAAI,CAACC,WAAW,GAAG,iBAAA,CAAA;QACnB,IAAI,CAAC/G,KAAK,GAAG,MAAA,CAAA;QACb,IAAI,CAACgH,QAAQ,GAAG,EAAC,CAAA;QACjB,IAAI,CAACC,gBAAgB,GAAG,CAACC,OAAAA,GAAYA,QAAQhE,KAAK,CAACiE,QAAQ,CAACC,mBAAmB,EAAA,CAAA;QAC/E,IAAI,CAACC,QAAQ,GAAG,EAAC,CAAA;QACjB,IAAI,CAACC,MAAM,GAAG;AACZ,YAAA,WAAA;AACA,YAAA,UAAA;AACA,YAAA,OAAA;AACA,YAAA,YAAA;AACA,YAAA,WAAA;AACD,SAAA,CAAA;QACD,IAAI,CAACC,IAAI,GAAG;YACVC,MAAQ,EAAA,oDAAA;YACR/T,IAAM,EAAA,EAAA;YACNgU,KAAO,EAAA,QAAA;YACPC,UAAY,EAAA,GAAA;AACZC,YAAAA,MAAAA,EAAQ,IAAI;AACd,SAAA,CAAA;QACA,IAAI,CAACC,KAAK,GAAG,EAAC,CAAA;QACd,IAAI,CAACC,oBAAoB,GAAG,CAACC,KAAK7W,OAAYiP,GAAAA,aAAAA,CAAcjP,QAAQ6V,eAAe,CAAA,CAAA;QACnF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,KAAK7W,OAAYiP,GAAAA,aAAAA,CAAcjP,QAAQ8V,WAAW,CAAA,CAAA;QAC3E,IAAI,CAACiB,UAAU,GAAG,CAACF,KAAK7W,OAAYiP,GAAAA,aAAAA,CAAcjP,QAAQ+O,KAAK,CAAA,CAAA;QAC/D,IAAI,CAACiI,SAAS,GAAG,GAAA,CAAA;QACjB,IAAI,CAACC,WAAW,GAAG;YACjBC,IAAM,EAAA,SAAA;AACNC,YAAAA,SAAAA,EAAW,IAAI;AACfC,YAAAA,gBAAAA,EAAkB,KAAK;AACzB,SAAA,CAAA;QACA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAACC,OAAO,GAAG,EAAC,CAAA;QAChB,IAAI,CAACC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,CAACC,KAAK,GAAG9W,SAAAA,CAAAA;QACb,IAAI,CAAC+W,MAAM,GAAG,EAAC,CAAA;QACf,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;QACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAAA;QAEnC,IAAI,CAACjI,QAAQ,CAAC8F,YAAAA,CAAAA,CAAAA;QACd,IAAI,CAACtX,KAAK,CAACuX,SAAAA,CAAAA,CAAAA;AACb,KAAA;AAKA,CACAzM,GAAIxI,CAAAA,KAAK,EAAEqH,MAAM,EAAE;QACjB,OAAOmB,GAAAA,CAAI,IAAI,EAAExI,KAAOqH,EAAAA,MAAAA,CAAAA,CAAAA;AAC1B,KAAA;AAKAuJ,CAAAA,GAAAA,CAAI5Q,KAAK,EAAE;QACT,OAAO2U,UAAAA,CAAS,IAAI,EAAE3U,KAAAA,CAAAA,CAAAA;AACxB,KAAA;AAKA,CACAkP,QAASlP,CAAAA,KAAK,EAAEqH,MAAM,EAAE;QACtB,OAAOmB,GAAAA,CAAIkM,aAAa1U,KAAOqH,EAAAA,MAAAA,CAAAA,CAAAA;AACjC,KAAA;IAEA+P,QAASpX,CAAAA,KAAK,EAAEqH,MAAM,EAAE;QACtB,OAAOmB,GAAAA,CAAIiM,WAAWzU,KAAOqH,EAAAA,MAAAA,CAAAA,CAAAA;AAC/B,KAAA;AAmBAkN,CAAAA,KAAAA,CAAMvU,KAAK,EAAEsP,IAAI,EAAE+H,WAAW,EAAEC,UAAU,EAAE;QAC1C,MAAMC,WAAAA,GAAc5C,UAAS,CAAA,IAAI,EAAE3U,KAAAA,CAAAA,CAAAA;QACnC,MAAMwX,iBAAAA,GAAoB7C,UAAS,CAAA,IAAI,EAAE0C,WAAAA,CAAAA,CAAAA;AACzC,QAAA,MAAMI,cAAc,GAAMnI,GAAAA,IAAAA,CAAAA;QAE1BjT,MAAOqb,CAAAA,gBAAgB,CAACH,WAAa,EAAA;AAEnC,YAAA,CAACE,cAAc;gBACbxb,KAAOsb,EAAAA,WAAW,CAACjI,IAAK,CAAA;AACxBqI,gBAAAA,QAAAA,EAAU,IAAI;AAChB,aAAA;AAEA,YAAA,CAACrI,OAAO;AACNzH,gBAAAA,UAAAA,EAAY,IAAI;gBAChB+I,GAAM,CAAA,GAAA;oBACJ,MAAMgH,KAAAA,GAAQ,IAAI,CAACH,WAAY,CAAA,CAAA;oBAC/B,MAAM5Y,MAAAA,GAAS2Y,iBAAiB,CAACF,UAAW,CAAA,CAAA;AAC5C,oBAAA,IAAI5a,SAASkb,KAAQ,CAAA,EAAA;AACnB,wBAAA,OAAOvb,MAAOqP,CAAAA,MAAM,CAAC,IAAI7M,MAAQ+Y,EAAAA,KAAAA,CAAAA,CAAAA;qBAClC;AACD,oBAAA,OAAO5a,eAAe4a,KAAO/Y,EAAAA,MAAAA,CAAAA,CAAAA;AAC/B,iBAAA;AACA2J,gBAAAA,GAAAA,CAAAA,CAAIvM,KAAK,EAAE;oBACT,IAAI,CAACwb,YAAY,GAAGxb,KAAAA,CAAAA;AACtB,iBAAA;AACF,aAAA;AACF,SAAA,CAAA,CAAA;AACF,KAAA;AAEAyB,IAAAA,KAAAA,CAAMma,QAAQ,EAAE;AACdA,QAAAA,QAAAA,CAAS/P,OAAO,CAAC,CAACpK,KAAAA,GAAUA,MAAM,IAAI,CAAA,CAAA,CAAA;AACxC,KAAA;AACF,CAAC;AAGD,eAAe,gBAAgB,IAAIoX,QAAS,CAAA;AAC1CzF,IAAAA,WAAAA,EAAa,CAACC,IAAAA,GAAS,CAACA,IAAAA,CAAKkF,UAAU,CAAC,IAAA,CAAA;IACxCpF,UAAY,EAAA,CAACE,OAASA,IAAS,KAAA,QAAA;IAC/B0G,KAAO,EAAA;QACL7G,SAAW,EAAA,aAAA;AACb,KAAA;IACAmH,WAAa,EAAA;AACXjH,QAAAA,WAAAA,EAAa,KAAK;AAClBD,QAAAA,UAAAA,EAAY,KAAK;AACnB,KAAA;AACF,CAAG,EAAA;AAACR,IAAAA,uBAAAA;AAAyBmB,IAAAA,oBAAAA;AAAsBoC,IAAAA,kBAAAA;CAAmB,CAAE;;AC5JxE;;;;;AAKC,IACM,SAAS2F,YAAanC,CAAAA,IAAc,EAAE;IAC3C,IAAI,CAACA,QAAQ3Z,aAAc2Z,CAAAA,IAAAA,CAAK9T,IAAI,CAAK7F,IAAAA,aAAAA,CAAc2Z,IAAKC,CAAAA,MAAM,CAAG,EAAA;AACnE,QAAA,OAAO,IAAI,CAAA;KACZ;AAED,IAAA,OAAO,CAACD,IAAKE,CAAAA,KAAK,GAAGF,IAAAA,CAAKE,KAAK,GAAG,GAAM,GAAA,EAAE,KACvCF,IAAAA,CAAKI,MAAM,GAAGJ,IAAKI,CAAAA,MAAM,GAAG,GAAA,GAAM,EAAC,CACpCJ,GAAAA,IAAAA,CAAK9T,IAAI,GAAG,KACZ8T,GAAAA,IAAAA,CAAKC,MAAM,CAAA;AACf,CAAC;AAED;;AAEC,IACM,SAASmC,YACd7B,CAAAA,GAA6B,EAC7B8B,IAA4B,EAC5BC,EAAY,EACZC,OAAe,EACfC,MAAc,EACd;IACA,IAAIC,SAAAA,GAAYJ,IAAI,CAACG,MAAO,CAAA,CAAA;AAC5B,IAAA,IAAI,CAACC,SAAW,EAAA;QACdA,SAAYJ,GAAAA,IAAI,CAACG,MAAO,CAAA,GAAGjC,IAAImC,WAAW,CAACF,QAAQ9E,KAAK,CAAA;AACxD4E,QAAAA,EAAAA,CAAGlX,IAAI,CAACoX,MAAAA,CAAAA,CAAAA;KACT;AACD,IAAA,IAAIC,YAAYF,OAAS,EAAA;QACvBA,OAAUE,GAAAA,SAAAA,CAAAA;KACX;IACD,OAAOF,OAAAA,CAAAA;AACT,CAAC;AAKD;;AAEC;AAEM,SAASI,aACdpC,GAA6B,EAC7BP,IAAY,EACZ4C,aAAqB,EACrBC,KAAiF,EACjF;AACAA,IAAAA,KAAAA,GAAQA,SAAS,EAAC,CAAA;AAClB,IAAA,IAAIR,OAAOQ,KAAMR,CAAAA,IAAI,GAAGQ,KAAMR,CAAAA,IAAI,IAAI,EAAC,CAAA;AACvC,IAAA,IAAIC,KAAKO,KAAMC,CAAAA,cAAc,GAAGD,KAAMC,CAAAA,cAAc,IAAI,EAAE,CAAA;IAE1D,IAAID,KAAAA,CAAM7C,IAAI,KAAKA,IAAM,EAAA;QACvBqC,IAAOQ,GAAAA,KAAAA,CAAMR,IAAI,GAAG,EAAC,CAAA;QACrBC,EAAKO,GAAAA,KAAAA,CAAMC,cAAc,GAAG,EAAE,CAAA;AAC9BD,QAAAA,KAAAA,CAAM7C,IAAI,GAAGA,IAAAA,CAAAA;KACd;AAEDO,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;AAERxC,IAAAA,GAAAA,CAAIP,IAAI,GAAGA,IAAAA,CAAAA;AACX,IAAA,IAAIuC,OAAU,GAAA,CAAA,CAAA;IACd,MAAM7Z,IAAAA,GAAOka,cAActa,MAAM,CAAA;IACjC,IAAIH,CAAAA,EAAW6a,CAAWC,EAAAA,IAAAA,EAAcC,KAAwBC,EAAAA,WAAAA,CAAAA;AAChE,IAAA,IAAKhb,CAAI,GAAA,CAAA,EAAGA,CAAIO,GAAAA,IAAAA,EAAMP,CAAK,EAAA,CAAA;QACzB+a,KAAQN,GAAAA,aAAa,CAACza,CAAE,CAAA,CAAA;;AAGxB,QAAA,IAAI+a,UAAU3Y,SAAa2Y,IAAAA,KAAAA,KAAU,IAAI,IAAI,CAAC3c,QAAQ2c,KAAQ,CAAA,EAAA;AAC5DX,YAAAA,OAAAA,GAAUH,YAAa7B,CAAAA,GAAAA,EAAK8B,IAAMC,EAAAA,EAAAA,EAAIC,OAASW,EAAAA,KAAAA,CAAAA,CAAAA;SAC1C,MAAA,IAAI3c,QAAQ2c,KAAQ,CAAA,EAAA;;;YAGzB,IAAKF,CAAAA,GAAI,GAAGC,IAAOC,GAAAA,KAAAA,CAAM5a,MAAM,EAAE0a,CAAAA,GAAIC,MAAMD,CAAK,EAAA,CAAA;gBAC9CG,WAAcD,GAAAA,KAAK,CAACF,CAAE,CAAA,CAAA;;AAEtB,gBAAA,IAAIG,gBAAgB5Y,SAAa4Y,IAAAA,WAAAA,KAAgB,IAAI,IAAI,CAAC5c,QAAQ4c,WAAc,CAAA,EAAA;AAC9EZ,oBAAAA,OAAAA,GAAUH,YAAa7B,CAAAA,GAAAA,EAAK8B,IAAMC,EAAAA,EAAAA,EAAIC,OAASY,EAAAA,WAAAA,CAAAA,CAAAA;iBAChD;AACH,aAAA;SACD;AACH,KAAA;AAEA5C,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;IAEX,MAAMC,KAAAA,GAAQf,EAAGha,CAAAA,MAAM,GAAG,CAAA,CAAA;IAC1B,IAAI+a,KAAAA,GAAQT,aAActa,CAAAA,MAAM,EAAE;AAChC,QAAA,IAAKH,CAAI,GAAA,CAAA,EAAGA,CAAIkb,GAAAA,KAAAA,EAAOlb,CAAK,EAAA,CAAA;AAC1B,YAAA,OAAOka,IAAI,CAACC,EAAE,CAACna,EAAE,CAAC,CAAA;AACpB,SAAA;QACAma,EAAG5P,CAAAA,MAAM,CAAC,CAAG2Q,EAAAA,KAAAA,CAAAA,CAAAA;KACd;IACD,OAAOd,OAAAA,CAAAA;AACT,CAAC;AAED;;;;;;;IAQO,SAASe,WAAY3H,CAAAA,KAAY,EAAE4H,KAAa,EAAE7F,KAAa,EAAE;IACtE,MAAMgC,gBAAAA,GAAmB/D,MAAM6H,uBAAuB,CAAA;IACtD,MAAMC,SAAAA,GAAY/F,UAAU,CAAIlR,GAAAA,IAAAA,CAAKqC,GAAG,CAAC6O,KAAAA,GAAQ,CAAG,EAAA,GAAA,CAAA,GAAO,CAAC,CAAA;IAC5D,OAAOlR,IAAAA,CAAKiB,KAAK,CAAE8V,CAAAA,KAAQE,GAAAA,SAAQ,IAAK/D,gBAAAA,CAAAA,GAAoBA,gBAAmB+D,GAAAA,SAAAA,CAAAA;AACjF,CAAC;AAED;;AAEC,IACM,SAASC,WAAAA,CAAYC,MAA0B,EAAEpD,GAA8B,EAAE;IACtF,IAAI,CAACA,GAAO,IAAA,CAACoD,MAAQ,EAAA;AACnB,QAAA,OAAA;KACD;IAEDpD,GAAMA,GAAAA,GAAAA,IAAOoD,MAAOC,CAAAA,UAAU,CAAC,IAAA,CAAA,CAAA;AAE/BrD,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;;;AAGRxC,IAAAA,GAAAA,CAAIsD,cAAc,EAAA,CAAA;IAClBtD,GAAIuD,CAAAA,SAAS,CAAC,CAAG,EAAA,CAAA,EAAGH,OAAOjG,KAAK,EAAEiG,OAAOI,MAAM,CAAA,CAAA;AAC/CxD,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACb,CAAC;AASM,SAASY,UACdzD,GAA6B,EAC7B7W,OAAyB,EACzBkB,CAAS,EACTE,CAAS,EACT;;AAEAmZ,IAAAA,eAAAA,CAAgB1D,GAAK7W,EAAAA,OAAAA,EAASkB,CAAGE,EAAAA,CAAAA,EAAG,IAAI,CAAA,CAAA;AAC1C,CAAC;AAED;AACO,SAASmZ,eACd1D,CAAAA,GAA6B,EAC7B7W,OAAyB,EACzBkB,CAAS,EACTE,CAAS,EACToZ,CAAS,EACT;AACA,IAAA,IAAIzd,MAAc0d,OAAiBC,EAAAA,OAAAA,EAAiBlY,IAAcmY,EAAAA,YAAAA,EAAsB3G,OAAe4G,QAAkBC,EAAAA,QAAAA,CAAAA;IACzH,MAAMrE,KAAAA,GAAQxW,QAAQ8a,UAAU,CAAA;IAChC,MAAMC,QAAAA,GAAW/a,QAAQ+a,QAAQ,CAAA;IACjC,MAAMC,MAAAA,GAAShb,QAAQgb,MAAM,CAAA;AAC7B,IAAA,IAAIC,GAAM,GAACF,CAAAA,QAAAA,IAAY,CAAA,IAAK5X,WAAAA,CAAAA;IAE5B,IAAIqT,KAAAA,IAAS,OAAOA,KAAAA,KAAU,QAAU,EAAA;AACtCzZ,QAAAA,IAAAA,GAAOyZ,MAAMtZ,QAAQ,EAAA,CAAA;QACrB,IAAIH,IAAAA,KAAS,2BAA+BA,IAAAA,IAAAA,KAAS,4BAA8B,EAAA;AACjF8Z,YAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;YACRxC,GAAIqE,CAAAA,SAAS,CAACha,CAAGE,EAAAA,CAAAA,CAAAA,CAAAA;AACjByV,YAAAA,GAAAA,CAAIsE,MAAM,CAACF,GAAAA,CAAAA,CAAAA;AACXpE,YAAAA,GAAAA,CAAIuE,SAAS,CAAC5E,KAAAA,EAAO,CAACA,KAAAA,CAAMxC,KAAK,GAAG,CAAA,EAAG,CAACwC,KAAAA,CAAM6D,MAAM,GAAG,CAAA,EAAG7D,MAAMxC,KAAK,EAAEwC,MAAM6D,MAAM,CAAA,CAAA;AACnFxD,YAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACX,YAAA,OAAA;SACD;KACF;IAED,IAAI9U,KAAAA,CAAMoW,MAAWA,CAAAA,IAAAA,MAAAA,IAAU,CAAG,EAAA;AAChC,QAAA,OAAA;KACD;AAEDnE,IAAAA,GAAAA,CAAIwE,SAAS,EAAA,CAAA;IAEb,OAAQ7E,KAAAA;;AAEN,QAAA;AACE,YAAA,IAAIgE,CAAG,EAAA;gBACL3D,GAAIyE,CAAAA,OAAO,CAACpa,CAAGE,EAAAA,CAAAA,EAAGoZ,IAAI,CAAGQ,EAAAA,MAAAA,EAAQ,GAAG,CAAGjY,EAAAA,GAAAA,CAAAA,CAAAA;aAClC,MAAA;AACL8T,gBAAAA,GAAAA,CAAI0E,GAAG,CAACra,CAAGE,EAAAA,CAAAA,EAAG4Z,QAAQ,CAAGjY,EAAAA,GAAAA,CAAAA,CAAAA;aAC1B;AACD8T,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,UAAA;YACHxH,KAAQwG,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIQ,MAAM,CAAA;AAC1BnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI4B,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOjH,CAAAA,GAAAA,KAAAA,EAAO5S,CAAI0B,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;YAC1DC,GAAO3X,IAAAA,aAAAA,CAAAA;AACPuT,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI4B,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOjH,CAAAA,GAAAA,KAAAA,EAAO5S,CAAI0B,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;YAC1DC,GAAO3X,IAAAA,aAAAA,CAAAA;AACPuT,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI4B,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOjH,CAAAA,GAAAA,KAAAA,EAAO5S,CAAI0B,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;AAC1DnE,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,aAAA;;;;;;;;AAQHb,YAAAA,YAAAA,GAAeK,MAAS,GAAA,KAAA,CAAA;AACxBxY,YAAAA,IAAAA,GAAOwY,MAASL,GAAAA,YAAAA,CAAAA;AAChBF,YAAAA,OAAAA,GAAU3X,IAAK4K,CAAAA,GAAG,CAACuN,GAAAA,GAAM5X,UAAcb,CAAAA,GAAAA,IAAAA,CAAAA;YACvCoY,QAAW9X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAM5X,GAAAA,UAAAA,CAAAA,IAAemX,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIG,YAAenY,GAAAA,IAAI,CAAD,CAAA;AACvEkY,YAAAA,OAAAA,GAAU5X,IAAK2J,CAAAA,GAAG,CAACwO,GAAAA,GAAM5X,UAAcb,CAAAA,GAAAA,IAAAA,CAAAA;YACvCqY,QAAW/X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAM5X,GAAAA,UAAAA,CAAAA,IAAemX,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIG,YAAenY,GAAAA,IAAI,CAAD,CAAA;YACvEqU,GAAI0E,CAAAA,GAAG,CAACra,CAAI0Z,GAAAA,QAAAA,EAAUxZ,IAAIsZ,OAASC,EAAAA,YAAAA,EAAcM,GAAMpY,GAAAA,EAAAA,EAAIoY,GAAM7X,GAAAA,OAAAA,CAAAA,CAAAA;YACjEyT,GAAI0E,CAAAA,GAAG,CAACra,CAAI2Z,GAAAA,QAAAA,EAAUzZ,IAAIqZ,OAASE,EAAAA,YAAAA,EAAcM,MAAM7X,OAAS6X,EAAAA,GAAAA,CAAAA,CAAAA;YAChEpE,GAAI0E,CAAAA,GAAG,CAACra,CAAI0Z,GAAAA,QAAAA,EAAUxZ,IAAIsZ,OAASC,EAAAA,YAAAA,EAAcM,KAAKA,GAAM7X,GAAAA,OAAAA,CAAAA,CAAAA;YAC5DyT,GAAI0E,CAAAA,GAAG,CAACra,CAAI2Z,GAAAA,QAAAA,EAAUzZ,IAAIqZ,OAASE,EAAAA,YAAAA,EAAcM,GAAM7X,GAAAA,OAAAA,EAAS6X,GAAMpY,GAAAA,EAAAA,CAAAA,CAAAA;AACtEgU,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,MAAA;AACH,YAAA,IAAI,CAACT,QAAU,EAAA;gBACbvY,IAAOM,GAAAA,IAAAA,CAAK6Y,OAAO,GAAGX,MAAAA,CAAAA;gBACtBhH,KAAQwG,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIhY,IAAI,CAAA;gBACxBqU,GAAI+E,CAAAA,IAAI,CAAC1a,CAAI8S,GAAAA,KAAAA,EAAO5S,IAAIoB,IAAM,EAAA,CAAA,GAAIwR,OAAO,CAAIxR,GAAAA,IAAAA,CAAAA,CAAAA;gBAC7C,MAAM;aACP;YACDyY,GAAO5X,IAAAA,UAAAA,CAAAA;AACT,4BACA,KAAK,SAAA;YACHuX,QAAW9X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAU3X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAU5X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAW/X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,UAAA;YACHP,GAAO5X,IAAAA,UAAAA,CAAAA;AACT,4BACA,KAAK,OAAA;YACHuX,QAAW9X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAU3X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAU5X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAW/X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;YAC7B,MAAM;QACR,KAAK,MAAA;YACHG,QAAW9X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAU3X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAU5X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAW/X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;YAC7BQ,GAAO5X,IAAAA,UAAAA,CAAAA;YACPuX,QAAW9X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAU3X,GAAAA,IAAAA,CAAK4K,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAU5X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAW/X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI0Z,GAAAA,QAAAA,EAAUxZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI2Z,GAAAA,QAAAA,EAAUzZ,CAAIqZ,GAAAA,OAAAA,CAAAA,CAAAA;YAC7B,MAAM;QACR,KAAK,MAAA;AACHA,YAAAA,OAAAA,GAAUD,IAAIA,CAAI,GAAA,CAAA,GAAI1X,KAAK4K,GAAG,CAACuN,OAAOD,MAAM,CAAA;YAC5CN,OAAU5X,GAAAA,IAAAA,CAAK2J,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;AAC1BnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACva,CAAIuZ,GAAAA,OAAAA,EAASrZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;AAC5B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAIuZ,GAAAA,OAAAA,EAASrZ,CAAIsZ,GAAAA,OAAAA,CAAAA,CAAAA;YAC5B,MAAM;QACR,KAAK,MAAA;YACH7D,GAAI4E,CAAAA,MAAM,CAACva,CAAGE,EAAAA,CAAAA,CAAAA,CAAAA;AACdyV,YAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAAA,GAAI4B,KAAK4K,GAAG,CAACuN,QAAQT,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIQ,MAAM,CAAD,EAAI5Z,IAAI0B,IAAK2J,CAAAA,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;YACzE,MAAM;AACR,QAAA,KAAK,KAAK;AACRnE,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;AACV,KAAA;AAEA3E,IAAAA,GAAAA,CAAIgF,IAAI,EAAA,CAAA;IACR,IAAI7b,OAAAA,CAAQ8b,WAAW,GAAG,CAAG,EAAA;AAC3BjF,QAAAA,GAAAA,CAAIkF,MAAM,EAAA,CAAA;KACX;AACH,CAAC;AAED;;;;;;IAOO,SAASC,cACdC,CAAAA,KAAY,EACZC,IAAU,EACVC,MAAe,EACf;IACAA,MAASA,GAAAA,MAAAA,IAAU;AAEnB,IAAA,OAAO,CAACD,IAAAA,IAASD,KAASA,IAAAA,KAAAA,CAAM/a,CAAC,GAAGgb,IAAKzR,CAAAA,IAAI,GAAG0R,MAAAA,IAAUF,KAAM/a,CAAAA,CAAC,GAAGgb,IAAAA,CAAKxR,KAAK,GAAGyR,MACjFF,IAAAA,KAAAA,CAAM7a,CAAC,GAAG8a,IAAKrL,CAAAA,GAAG,GAAGsL,MAAAA,IAAUF,KAAM7a,CAAAA,CAAC,GAAG8a,IAAAA,CAAKpL,MAAM,GAAGqL,MAAAA,CAAAA;AACzD,CAAC;AAEM,SAASC,QAAAA,CAASvF,GAA6B,EAAEqF,IAAU,EAAE;AAClErF,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;AACRxC,IAAAA,GAAAA,CAAIwE,SAAS,EAAA,CAAA;AACbxE,IAAAA,GAAAA,CAAI+E,IAAI,CAACM,IAAAA,CAAKzR,IAAI,EAAEyR,IAAAA,CAAKrL,GAAG,EAAEqL,IAAAA,CAAKxR,KAAK,GAAGwR,KAAKzR,IAAI,EAAEyR,KAAKpL,MAAM,GAAGoL,KAAKrL,GAAG,CAAA,CAAA;AAC5EgG,IAAAA,GAAAA,CAAI1D,IAAI,EAAA,CAAA;AACV,CAAC;AAEM,SAASkJ,UAAWxF,CAAAA,GAA6B,EAAE;AACxDA,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACb,CAAC;AAED;;AAEC,IACM,SAAS4C,cACdzF,CAAAA,GAA6B,EAC7BjW,QAAe,EACfpB,MAAa,EACb+c,IAAc,EACdrF,IAAa,EACb;AACA,IAAA,IAAI,CAACtW,QAAU,EAAA;AACb,QAAA,OAAOiW,IAAI6E,MAAM,CAAClc,OAAO0B,CAAC,EAAE1B,OAAO4B,CAAC,CAAA,CAAA;KACrC;AACD,IAAA,IAAI8V,SAAS,QAAU,EAAA;QACrB,MAAMsF,QAAAA,GAAW,CAAC5b,QAAAA,CAASM,CAAC,GAAG1B,MAAAA,CAAO0B,CAAAA,IAAK,GAAA,CAAA;AAC3C2V,QAAAA,GAAAA,CAAI6E,MAAM,CAACc,QAAU5b,EAAAA,QAAAA,CAASQ,CAAC,CAAA,CAAA;AAC/ByV,QAAAA,GAAAA,CAAI6E,MAAM,CAACc,QAAUhd,EAAAA,MAAAA,CAAO4B,CAAC,CAAA,CAAA;AAC/B,KAAA,MAAO,IAAI8V,IAAAA,KAAS,OAAY,KAAA,CAAC,CAACqF,IAAM,EAAA;AACtC1F,QAAAA,GAAAA,CAAI6E,MAAM,CAAC9a,QAAAA,CAASM,CAAC,EAAE1B,OAAO4B,CAAC,CAAA,CAAA;KAC1B,MAAA;AACLyV,QAAAA,GAAAA,CAAI6E,MAAM,CAAClc,MAAAA,CAAO0B,CAAC,EAAEN,SAASQ,CAAC,CAAA,CAAA;KAChC;AACDyV,IAAAA,GAAAA,CAAI6E,MAAM,CAAClc,MAAAA,CAAO0B,CAAC,EAAE1B,OAAO4B,CAAC,CAAA,CAAA;AAC/B,CAAC;AAED;;IAGO,SAASqb,cAAAA,CACd5F,GAA6B,EAC7BjW,QAAqB,EACrBpB,MAAmB,EACnB+c,IAAc,EACd;AACA,IAAA,IAAI,CAAC3b,QAAU,EAAA;AACb,QAAA,OAAOiW,IAAI6E,MAAM,CAAClc,OAAO0B,CAAC,EAAE1B,OAAO4B,CAAC,CAAA,CAAA;KACrC;AACDyV,IAAAA,GAAAA,CAAI6F,aAAa,CACfH,IAAO3b,GAAAA,QAAAA,CAAS+b,IAAI,GAAG/b,QAAAA,CAASgc,IAAI,EACpCL,OAAO3b,QAASic,CAAAA,IAAI,GAAGjc,QAAAA,CAASkc,IAAI,EACpCP,IAAAA,GAAO/c,MAAOod,CAAAA,IAAI,GAAGpd,MAAAA,CAAOmd,IAAI,EAChCJ,OAAO/c,MAAOsd,CAAAA,IAAI,GAAGtd,MAAAA,CAAOqd,IAAI,EAChCrd,MAAAA,CAAO0B,CAAC,EACR1B,OAAO4B,CAAC,CAAA,CAAA;AACZ,CAAC;AAED,SAAS2b,aAAclG,CAAAA,GAA6B,EAAEmG,IAAoB,EAAE;IAC1E,IAAIA,IAAAA,CAAKC,WAAW,EAAE;QACpBpG,GAAIqE,CAAAA,SAAS,CAAC8B,IAAAA,CAAKC,WAAW,CAAC,EAAE,EAAED,IAAAA,CAAKC,WAAW,CAAC,CAAE,CAAA,CAAA,CAAA;KACvD;AAED,IAAA,IAAI,CAACtgB,aAAAA,CAAcqgB,IAAKjC,CAAAA,QAAQ,CAAG,EAAA;QACjClE,GAAIsE,CAAAA,MAAM,CAAC6B,IAAAA,CAAKjC,QAAQ,CAAA,CAAA;KACzB;IAED,IAAIiC,IAAAA,CAAKjO,KAAK,EAAE;QACd8H,GAAIqG,CAAAA,SAAS,GAAGF,IAAAA,CAAKjO,KAAK,CAAA;KAC3B;IAED,IAAIiO,IAAAA,CAAKG,SAAS,EAAE;QAClBtG,GAAIsG,CAAAA,SAAS,GAAGH,IAAAA,CAAKG,SAAS,CAAA;KAC/B;IAED,IAAIH,IAAAA,CAAKI,YAAY,EAAE;QACrBvG,GAAIuG,CAAAA,YAAY,GAAGJ,IAAAA,CAAKI,YAAY,CAAA;KACrC;AACH,CAAA;AAEA,SAASC,YAAAA,CACPxG,GAA6B,EAC7B3V,CAAS,EACTE,CAAS,EACTkc,IAAY,EACZN,IAAoB,EACpB;AACA,IAAA,IAAIA,IAAKO,CAAAA,aAAa,IAAIP,IAAAA,CAAKQ,SAAS,EAAE;AACxC;;;;;;AAMC,QACD,MAAMC,OAAAA,GAAU5G,GAAImC,CAAAA,WAAW,CAACsE,IAAAA,CAAAA,CAAAA;QAChC,MAAM7S,IAAAA,GAAOvJ,CAAIuc,GAAAA,OAAAA,CAAQC,qBAAqB,CAAA;QAC9C,MAAMhT,KAAAA,GAAQxJ,CAAIuc,GAAAA,OAAAA,CAAQE,sBAAsB,CAAA;QAChD,MAAM9M,GAAAA,GAAMzP,CAAIqc,GAAAA,OAAAA,CAAQG,uBAAuB,CAAA;QAC/C,MAAM9M,MAAAA,GAAS1P,CAAIqc,GAAAA,OAAAA,CAAQI,wBAAwB,CAAA;QACnD,MAAMC,WAAAA,GAAcd,IAAKO,CAAAA,aAAa,GAAI1M,CAAAA,GAAMC,GAAAA,MAAK,IAAK,CAAA,GAAIA,MAAM,CAAA;QAEpE+F,GAAIkH,CAAAA,WAAW,GAAGlH,GAAAA,CAAIqG,SAAS,CAAA;AAC/BrG,QAAAA,GAAAA,CAAIwE,SAAS,EAAA,CAAA;AACbxE,QAAAA,GAAAA,CAAIvD,SAAS,GAAG0J,IAAKgB,CAAAA,eAAe,IAAI,CAAA,CAAA;QACxCnH,GAAI4E,CAAAA,MAAM,CAAChR,IAAMqT,EAAAA,WAAAA,CAAAA,CAAAA;QACjBjH,GAAI6E,CAAAA,MAAM,CAAChR,KAAOoT,EAAAA,WAAAA,CAAAA,CAAAA;AAClBjH,QAAAA,GAAAA,CAAIkF,MAAM,EAAA,CAAA;KACX;AACH,CAAA;AAEA,SAASkC,YAAapH,CAAAA,GAA6B,EAAEmG,IAAqB,EAAE;IAC1E,MAAMkB,QAAAA,GAAWrH,IAAIqG,SAAS,CAAA;IAE9BrG,GAAIqG,CAAAA,SAAS,GAAGF,IAAAA,CAAKjO,KAAK,CAAA;AAC1B8H,IAAAA,GAAAA,CAAIsH,QAAQ,CAACnB,IAAKvS,CAAAA,IAAI,EAAEuS,IAAAA,CAAKnM,GAAG,EAAEmM,IAAKhJ,CAAAA,KAAK,EAAEgJ,IAAAA,CAAK3C,MAAM,CAAA,CAAA;AACzDxD,IAAAA,GAAAA,CAAIqG,SAAS,GAAGgB,QAAAA,CAAAA;AAClB,CAAA;AAEA;;AAEC,IACM,SAASE,UAAAA,CACdvH,GAA6B,EAC7B3C,IAAuB,EACvBhT,CAAS,EACTE,CAAS,EACTkV,IAAoB,EACpB0G,IAAuB,GAAA,EAAE,EACzB;IACA,MAAMqB,KAAAA,GAAQxhB,OAAQqX,CAAAA,IAAAA,CAAAA,GAAQA,IAAO,GAAA;AAACA,QAAAA,IAAAA;AAAK,KAAA,CAAA;AAC3C,IAAA,MAAM6H,SAASiB,IAAKsB,CAAAA,WAAW,GAAG,CAAKtB,IAAAA,IAAAA,CAAKuB,WAAW,KAAK,EAAA,CAAA;AAC5D,IAAA,IAAI9f,CAAW6e,EAAAA,IAAAA,CAAAA;AAEfzG,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;IACRxC,GAAIP,CAAAA,IAAI,GAAGA,IAAAA,CAAKwC,MAAM,CAAA;AACtBiE,IAAAA,aAAAA,CAAclG,GAAKmG,EAAAA,IAAAA,CAAAA,CAAAA;AAEnB,IAAA,IAAKve,IAAI,CAAGA,EAAAA,CAAAA,GAAI4f,MAAMzf,MAAM,EAAE,EAAEH,CAAG,CAAA;QACjC6e,IAAOe,GAAAA,KAAK,CAAC5f,CAAE,CAAA,CAAA;QAEf,IAAIue,IAAAA,CAAKwB,QAAQ,EAAE;YACjBP,YAAapH,CAAAA,GAAAA,EAAKmG,KAAKwB,QAAQ,CAAA,CAAA;SAChC;AAED,QAAA,IAAIzC,MAAQ,EAAA;YACV,IAAIiB,IAAAA,CAAKuB,WAAW,EAAE;gBACpB1H,GAAIkH,CAAAA,WAAW,GAAGf,IAAAA,CAAKuB,WAAW,CAAA;aACnC;AAED,YAAA,IAAI,CAAC5hB,aAAAA,CAAcqgB,IAAKsB,CAAAA,WAAW,CAAG,EAAA;gBACpCzH,GAAIvD,CAAAA,SAAS,GAAG0J,IAAAA,CAAKsB,WAAW,CAAA;aACjC;AAEDzH,YAAAA,GAAAA,CAAI4H,UAAU,CAACnB,IAAAA,EAAMpc,CAAGE,EAAAA,CAAAA,EAAG4b,KAAK0B,QAAQ,CAAA,CAAA;SACzC;AAED7H,QAAAA,GAAAA,CAAI8H,QAAQ,CAACrB,IAAAA,EAAMpc,CAAGE,EAAAA,CAAAA,EAAG4b,KAAK0B,QAAQ,CAAA,CAAA;QACtCrB,YAAaxG,CAAAA,GAAAA,EAAK3V,CAAGE,EAAAA,CAAAA,EAAGkc,IAAMN,EAAAA,IAAAA,CAAAA,CAAAA;QAE9B5b,CAAK7D,IAAAA,MAAAA,CAAO+Y,KAAKG,UAAU,CAAA,CAAA;AAC7B,KAAA;AAEAI,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACb,CAAC;AAED;;;;AAIC,IACM,SAASkF,kBAAAA,CACd/H,GAA6B,EAC7B+E,IAA2C,EAC3C;IACA,MAAM,EAAC1a,CAAC,GAAEE,CAAC,GAAEoZ,CAAC,GAAEqE,CAAC,GAAE7D,MAAM,GAAC,GAAGY,IAAAA,CAAAA;;AAG7B/E,IAAAA,GAAAA,CAAI0E,GAAG,CAACra,CAAAA,GAAI8Z,MAAO8D,CAAAA,OAAO,EAAE1d,CAAI4Z,GAAAA,MAAAA,CAAO8D,OAAO,EAAE9D,OAAO8D,OAAO,EAAE,GAAMjc,GAAAA,EAAAA,EAAIA,IAAI,IAAI,CAAA,CAAA;;AAGlFgU,IAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAAA,EAAGE,CAAIyd,GAAAA,CAAAA,GAAI7D,OAAO+D,UAAU,CAAA,CAAA;;AAGvClI,IAAAA,GAAAA,CAAI0E,GAAG,CAACra,CAAAA,GAAI8Z,MAAO+D,CAAAA,UAAU,EAAE3d,CAAIyd,GAAAA,CAAAA,GAAI7D,MAAO+D,CAAAA,UAAU,EAAE/D,MAAO+D,CAAAA,UAAU,EAAElc,EAAAA,EAAIO,SAAS,IAAI,CAAA,CAAA;;AAG9FyT,IAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAAA,GAAIsZ,IAAIQ,MAAOgE,CAAAA,WAAW,EAAE5d,CAAIyd,GAAAA,CAAAA,CAAAA,CAAAA;;AAG3ChI,IAAAA,GAAAA,CAAI0E,GAAG,CAACra,CAAAA,GAAIsZ,CAAIQ,GAAAA,MAAAA,CAAOgE,WAAW,EAAE5d,CAAAA,GAAIyd,CAAI7D,GAAAA,MAAAA,CAAOgE,WAAW,EAAEhE,MAAAA,CAAOgE,WAAW,EAAE5b,OAAAA,EAAS,GAAG,IAAI,CAAA,CAAA;;AAGpGyT,IAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAAA,GAAIsZ,CAAGpZ,EAAAA,CAAAA,GAAI4Z,OAAOiE,QAAQ,CAAA,CAAA;;AAGrCpI,IAAAA,GAAAA,CAAI0E,GAAG,CAACra,CAAAA,GAAIsZ,CAAIQ,GAAAA,MAAAA,CAAOiE,QAAQ,EAAE7d,CAAAA,GAAI4Z,MAAOiE,CAAAA,QAAQ,EAAEjE,MAAOiE,CAAAA,QAAQ,EAAE,CAAG,EAAA,CAAC7b,SAAS,IAAI,CAAA,CAAA;;AAGxFyT,IAAAA,GAAAA,CAAI6E,MAAM,CAACxa,CAAI8Z,GAAAA,MAAAA,CAAO8D,OAAO,EAAE1d,CAAAA,CAAAA,CAAAA;AACjC;;ACxgBA,MAAM8d,WAAc,GAAA,sCAAA,CAAA;AACpB,MAAMC,UAAa,GAAA,uEAAA,CAAA;AAEnB;;;;;;;;;;AAWC,IACM,SAASC,YAAAA,CAAaxiB,KAAsB,EAAE4F,IAAY,EAAU;AACzE,IAAA,MAAM6c,UAAU,CAAC,KAAKziB,KAAI,EAAG0iB,KAAK,CAACJ,WAAAA,CAAAA,CAAAA;AACnC,IAAA,IAAI,CAACG,OAAWA,IAAAA,OAAO,CAAC,CAAA,CAAE,KAAK,QAAU,EAAA;AACvC,QAAA,OAAO7c,IAAO,GAAA,GAAA,CAAA;KACf;IAED5F,KAAQ,GAAA,CAACyiB,OAAO,CAAC,CAAE,CAAA,CAAA;IAEnB,OAAQA,OAAO,CAAC,CAAE,CAAA;QAChB,KAAK,IAAA;YACH,OAAOziB,KAAAA,CAAAA;QACT,KAAK,GAAA;YACHA,KAAS,IAAA,GAAA,CAAA;YACT,MAAM;AAGV,KAAA;AAEA,IAAA,OAAO4F,IAAO5F,GAAAA,KAAAA,CAAAA;AAChB,CAAC;AAED,MAAM2iB,YAAe,GAAA,CAACte,CAAe,GAAA,CAACA,CAAK,IAAA,CAAA,CAAA;AAQpC,SAASue,iBAAAA,CAAkB5iB,KAAsC,EAAE6iB,KAAwC,EAAE;AAClH,IAAA,MAAMC,MAAM,EAAC,CAAA;AACb,IAAA,MAAMC,WAAWtiB,QAASoiB,CAAAA,KAAAA,CAAAA,CAAAA;AAC1B,IAAA,MAAM9gB,OAAOghB,QAAW3iB,GAAAA,MAAAA,CAAO2B,IAAI,CAAC8gB,SAASA,KAAK,CAAA;IAClD,MAAMG,IAAAA,GAAOviB,QAAST,CAAAA,KAAAA,CAAAA,GAClB+iB,QACEE,GAAAA,CAAAA,OAAQliB,cAAef,CAAAA,KAAK,CAACijB,IAAAA,CAAK,EAAEjjB,KAAK,CAAC6iB,KAAK,CAACI,IAAK,CAAA,CAAC,CACtDA,GAAAA,CAAAA,IAAQjjB,GAAAA,KAAK,CAACijB,IAAAA,CAAK,GACrB,IAAMjjB,KAAK,CAAA;IAEf,KAAK,MAAMijB,QAAQlhB,IAAM,CAAA;AACvB+gB,QAAAA,GAAG,CAACG,IAAAA,CAAK,GAAGN,YAAAA,CAAaK,IAAKC,CAAAA,IAAAA,CAAAA,CAAAA,CAAAA;AAChC,KAAA;IACA,OAAOH,GAAAA,CAAAA;AACT,CAAC;AAED;;;;;;;AAOC,IACM,SAASI,MAAOljB,CAAAA,KAA4B,EAAE;AACnD,IAAA,OAAO4iB,kBAAkB5iB,KAAO,EAAA;QAACiU,GAAK,EAAA,GAAA;QAAKnG,KAAO,EAAA,GAAA;QAAKoG,MAAQ,EAAA,GAAA;QAAKrG,IAAM,EAAA,GAAA;AAAG,KAAA,CAAA,CAAA;AAC/E,CAAC;AAED;;;;;;AAMC,IACM,SAASsV,aAAcnjB,CAAAA,KAA2B,EAAE;AACzD,IAAA,OAAO4iB,kBAAkB5iB,KAAO,EAAA;AAAC,QAAA,SAAA;AAAW,QAAA,UAAA;AAAY,QAAA,YAAA;AAAc,QAAA,aAAA;AAAc,KAAA,CAAA,CAAA;AACtF,CAAC;AAED;;;;;;;AAOC,IACM,SAASojB,SAAUpjB,CAAAA,KAAqB,EAAa;AAC1D,IAAA,MAAMgF,MAAMke,MAAOljB,CAAAA,KAAAA,CAAAA,CAAAA;AAEnBgF,IAAAA,GAAAA,CAAIoS,KAAK,GAAGpS,GAAAA,CAAI6I,IAAI,GAAG7I,IAAI8I,KAAK,CAAA;AAChC9I,IAAAA,GAAAA,CAAIyY,MAAM,GAAGzY,GAAAA,CAAIiP,GAAG,GAAGjP,IAAIkP,MAAM,CAAA;IAEjC,OAAOlP,GAAAA,CAAAA;AACT,CAAC;AAED;;;;;;AAMC,IAEM,SAASqe,MAAAA,CAAOjgB,OAA0B,EAAEkgB,QAA4B,EAAE;AAC/ElgB,IAAAA,OAAAA,GAAUA,WAAW,EAAC,CAAA;IACtBkgB,QAAWA,GAAAA,QAAAA,IAAY1Q,SAAS8G,IAAI,CAAA;AAEpC,IAAA,IAAI9T,OAAO7E,cAAeqC,CAAAA,OAAAA,CAAQwC,IAAI,EAAE0d,SAAS1d,IAAI,CAAA,CAAA;IAErD,IAAI,OAAOA,SAAS,QAAU,EAAA;AAC5BA,QAAAA,IAAAA,GAAO2d,SAAS3d,IAAM,EAAA,EAAA,CAAA,CAAA;KACvB;AACD,IAAA,IAAIgU,QAAQ7Y,cAAeqC,CAAAA,OAAAA,CAAQwW,KAAK,EAAE0J,SAAS1J,KAAK,CAAA,CAAA;IACxD,IAAIA,KAAAA,IAAS,CAAC,CAAC,KAAKA,KAAI,EAAG8I,KAAK,CAACH,UAAa,CAAA,EAAA;QAC5Cre,OAAQC,CAAAA,IAAI,CAAC,iCAAA,GAAoCyV,KAAQ,GAAA,GAAA,CAAA,CAAA;QACzDA,KAAQ3V,GAAAA,SAAAA,CAAAA;KACT;AAED,IAAA,MAAMyV,IAAO,GAAA;AACXC,QAAAA,MAAAA,EAAQ5Y,cAAeqC,CAAAA,OAAAA,CAAQuW,MAAM,EAAE2J,SAAS3J,MAAM,CAAA;AACtDE,QAAAA,UAAAA,EAAY2I,aAAazhB,cAAeqC,CAAAA,OAAAA,CAAQyW,UAAU,EAAEyJ,QAAAA,CAASzJ,UAAU,CAAGjU,EAAAA,IAAAA,CAAAA;AAClFA,QAAAA,IAAAA;AACAgU,QAAAA,KAAAA;AACAE,QAAAA,MAAAA,EAAQ/Y,cAAeqC,CAAAA,OAAAA,CAAQ0W,MAAM,EAAEwJ,SAASxJ,MAAM,CAAA;QACtDoC,MAAQ,EAAA,EAAA;AACV,KAAA,CAAA;IAEAxC,IAAKwC,CAAAA,MAAM,GAAGL,YAAanC,CAAAA,IAAAA,CAAAA,CAAAA;IAC3B,OAAOA,IAAAA,CAAAA;AACT,CAAC;AAED;;;;;;;;;;IAWO,SAAS8J,OAAAA,CAAQC,MAAsB,EAAEpK,OAAgB,EAAE7W,KAAc,EAAEkhB,IAA6B,EAAE;AAC/G,IAAA,IAAIC,YAAY,IAAI,CAAA;AACpB,IAAA,IAAI9hB,GAAWO,IAAcpC,EAAAA,KAAAA,CAAAA;IAE7B,IAAK6B,CAAAA,GAAI,GAAGO,IAAOqhB,GAAAA,MAAAA,CAAOzhB,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;QAC/C7B,KAAQyjB,GAAAA,MAAM,CAAC5hB,CAAE,CAAA,CAAA;AACjB,QAAA,IAAI7B,UAAUiE,SAAW,EAAA;YACvB,SAAS;SACV;AACD,QAAA,IAAIoV,OAAYpV,KAAAA,SAAAA,IAAa,OAAOjE,KAAAA,KAAU,UAAY,EAAA;AACxDA,YAAAA,KAAAA,GAAQA,KAAMqZ,CAAAA,OAAAA,CAAAA,CAAAA;AACdsK,YAAAA,SAAAA,GAAY,KAAK,CAAA;SAClB;QACD,IAAInhB,KAAAA,KAAUyB,SAAahE,IAAAA,OAAAA,CAAQD,KAAQ,CAAA,EAAA;AACzCA,YAAAA,KAAAA,GAAQA,KAAK,CAACwC,KAAQxC,GAAAA,KAAAA,CAAMgC,MAAM,CAAC,CAAA;AACnC2hB,YAAAA,SAAAA,GAAY,KAAK,CAAA;SAClB;AACD,QAAA,IAAI3jB,UAAUiE,SAAW,EAAA;YACvB,IAAIyf,IAAAA,IAAQ,CAACC,SAAW,EAAA;gBACtBD,IAAKC,CAAAA,SAAS,GAAG,KAAK,CAAA;aACvB;YACD,OAAO3jB,KAAAA,CAAAA;SACR;AACH,KAAA;AACF,CAAC;AAED;;;;;IAMO,SAAS4jB,SAAUC,CAAAA,MAAqC,EAAErN,KAAsB,EAAEH,WAAoB,EAAE;AAC7G,IAAA,MAAM,EAAC/N,GAAAA,GAAKC,GAAAA,GAAI,GAAGsb,MAAAA,CAAAA;AACnB,IAAA,MAAMC,SAAS1iB,WAAYoV,CAAAA,KAAAA,EAAO,CAACjO,GAAAA,GAAMD,GAAE,IAAK,CAAA,CAAA,CAAA;IAChD,MAAMyb,QAAAA,GAAW,CAAC/jB,KAAegkB,EAAAA,GAAAA,GAAgB3N,eAAerW,KAAU,KAAA,CAAA,GAAI,CAAIA,GAAAA,KAAAA,GAAQgkB,GAAG,CAAA;IAC7F,OAAO;AACL1b,QAAAA,GAAAA,EAAKyb,QAASzb,CAAAA,GAAAA,EAAK,CAACpC,IAAAA,CAAKa,GAAG,CAAC+c,MAAAA,CAAAA,CAAAA;AAC7Bvb,QAAAA,GAAAA,EAAKwb,SAASxb,GAAKub,EAAAA,MAAAA,CAAAA;AACrB,KAAA,CAAA;AACF,CAAC;AAUM,SAASG,aAAAA,CAAcC,aAAqB,EAAE7K,OAAe,EAAE;AACpE,IAAA,OAAOjZ,OAAOqP,MAAM,CAACrP,MAAOyC,CAAAA,MAAM,CAACqhB,aAAgB7K,CAAAA,EAAAA,OAAAA,CAAAA,CAAAA;AACrD;;AC7LA;;;;;;;;;AASC,IACM,SAAS8K,eAIdC,CAAAA,MAAS,EACTC,QAAW,GAAA;AAAC,IAAA,EAAA;CAAG,EACfC,UAAc,EACdhB,QAA4B,EAC5BiB,YAAY,IAAMH,MAAM,CAAC,CAAA,CAAE,EAC3B;AACA,IAAA,MAAMI,kBAAkBF,UAAcF,IAAAA,MAAAA,CAAAA;IACtC,IAAI,OAAOd,aAAa,WAAa,EAAA;AACnCA,QAAAA,QAAAA,GAAWmB,SAAS,WAAaL,EAAAA,MAAAA,CAAAA,CAAAA;KAClC;AACD,IAAA,MAAM7H,KAA6B,GAAA;QACjC,CAACmI,MAAAA,CAAOC,WAAW,GAAG,QAAA;AACtBC,QAAAA,UAAAA,EAAY,IAAI;QAChBC,OAAST,EAAAA,MAAAA;QACTU,WAAaN,EAAAA,eAAAA;QACbtR,SAAWoQ,EAAAA,QAAAA;QACXyB,UAAYR,EAAAA,SAAAA;QACZpJ,QAAU,EAAA,CAACpX,QAAqBogB,eAAgB,CAAA;AAACpgB,gBAAAA,KAAAA;AAAUqgB,gBAAAA,GAAAA,MAAAA;AAAO,aAAA,EAAEC,UAAUG,eAAiBlB,EAAAA,QAAAA,CAAAA;AACjG,KAAA,CAAA;IACA,OAAO,IAAI0B,MAAMzI,KAAO,EAAA;AACtB;;AAEC,QACD0I,cAAeriB,CAAAA,CAAAA,MAAM,EAAEqgB,IAAY,EAAE;AACnC,YAAA,OAAOrgB,MAAM,CAACqgB,IAAK,CAAA,CAAA;YACnB,OAAOrgB,MAAAA,CAAOsiB,KAAK,CAAA;AACnB,YAAA,OAAOd,MAAM,CAAC,CAAA,CAAE,CAACnB,IAAAA,CAAK;AACtB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AAEA;;AAEC,QACDtO,GAAI/R,CAAAA,CAAAA,MAAM,EAAEqgB,IAAY,EAAE;AACxB,YAAA,OAAOkC,QAAQviB,MAAQqgB,EAAAA,IAAAA,EACrB,IAAMmC,oBAAqBnC,CAAAA,IAAAA,EAAMoB,UAAUD,MAAQxhB,EAAAA,MAAAA,CAAAA,CAAAA,CAAAA;AACvD,SAAA;AAEA;;;AAGC,QACDyiB,wBAAyBziB,CAAAA,CAAAA,MAAM,EAAEqgB,IAAI,EAAE;AACrC,YAAA,OAAOqC,QAAQD,wBAAwB,CAACziB,OAAOiiB,OAAO,CAAC,EAAE,EAAE5B,IAAAA,CAAAA,CAAAA;AAC7D,SAAA;AAEA;;AAEC,QACDsC,cAAiB,CAAA,GAAA;AACf,YAAA,OAAOD,OAAQC,CAAAA,cAAc,CAACnB,MAAM,CAAC,CAAE,CAAA,CAAA,CAAA;AACzC,SAAA;AAEA;;AAEC,QACDte,GAAIlD,CAAAA,CAAAA,MAAM,EAAEqgB,IAAY,EAAE;YACxB,OAAOuC,oBAAAA,CAAqB5iB,MAAQqT,CAAAA,CAAAA,QAAQ,CAACgN,IAAAA,CAAAA,CAAAA;AAC/C,SAAA;AAEA;;QAGAwC,OAAAA,CAAAA,CAAQ7iB,MAAM,EAAE;AACd,YAAA,OAAO4iB,oBAAqB5iB,CAAAA,MAAAA,CAAAA,CAAAA;AAC9B,SAAA;AAEA;;AAEC,QACD2J,KAAI3J,MAAM,EAAEqgB,IAAY,EAAEjjB,KAAK,EAAE;YAC/B,MAAM0lB,OAAAA,GAAU9iB,OAAO+iB,QAAQ,KAAK/iB,MAAO+iB,CAAAA,QAAQ,GAAGpB,SAAU,EAAA,CAAA,CAAA;YAChE3hB,MAAM,CAACqgB,KAAK,GAAGyC,OAAO,CAACzC,IAAK,CAAA,GAAGjjB;YAC/B,OAAO4C,MAAAA,CAAOsiB,KAAK,CAAA;AACnB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AACF,KAAA,CAAA,CAAA;AACF,CAAC;AAED;;;;;;;IAQO,SAASU,cAAAA,CAIdC,KAA0B,EAC1BxM,OAAkB,EAClByM,QAA8B,EAC9BC,kBAAuC,EACvC;AACA,IAAA,MAAMxJ,KAA4B,GAAA;AAChCqI,QAAAA,UAAAA,EAAY,KAAK;QACjBoB,MAAQH,EAAAA,KAAAA;QACRI,QAAU5M,EAAAA,OAAAA;QACV6M,SAAWJ,EAAAA,QAAAA;AACXK,QAAAA,MAAAA,EAAQ,IAAI3Z,GAAAA,EAAAA;AACZuM,QAAAA,YAAAA,EAAcA,aAAa8M,KAAOE,EAAAA,kBAAAA,CAAAA;AAClCK,QAAAA,UAAAA,EAAY,CAACnM,GAAAA,GAAmB2L,cAAeC,CAAAA,KAAAA,EAAO5L,KAAK6L,QAAUC,EAAAA,kBAAAA,CAAAA;QACrE5K,QAAU,EAAA,CAACpX,QAAqB6hB,cAAeC,CAAAA,KAAAA,CAAM1K,QAAQ,CAACpX,KAAAA,CAAAA,EAAQsV,SAASyM,QAAUC,EAAAA,kBAAAA,CAAAA;AAC3F,KAAA,CAAA;IACA,OAAO,IAAIf,MAAMzI,KAAO,EAAA;AACtB;;AAEC,QACD0I,cAAeriB,CAAAA,CAAAA,MAAM,EAAEqgB,IAAI,EAAE;AAC3B,YAAA,OAAOrgB,MAAM,CAACqgB,IAAK,CAAA,CAAA;AACnB,YAAA,OAAO4C,KAAK,CAAC5C,IAAK,CAAA,CAAA;AAClB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AAEA;;AAEC,QACDtO,KAAI/R,MAAM,EAAEqgB,IAAY,EAAEoD,QAAQ,EAAE;AAClC,YAAA,OAAOlB,QAAQviB,MAAQqgB,EAAAA,IAAAA,EACrB,IAAMqD,mBAAAA,CAAoB1jB,QAAQqgB,IAAMoD,EAAAA,QAAAA,CAAAA,CAAAA,CAAAA;AAC5C,SAAA;AAEA;;;AAGC,QACDhB,wBAAyBziB,CAAAA,CAAAA,MAAM,EAAEqgB,IAAI,EAAE;YACrC,OAAOrgB,MAAAA,CAAOmW,YAAY,CAACwN,OAAO,GAC9BjB,OAAQxf,CAAAA,GAAG,CAAC+f,KAAAA,EAAO5C,IAAQ,CAAA,GAAA;AAACrX,gBAAAA,UAAAA,EAAY,IAAI;AAAED,gBAAAA,YAAAA,EAAc,IAAI;AAAA,aAAA,GAAI1H,SAAS,GAC7EqhB,OAAAA,CAAQD,wBAAwB,CAACQ,OAAO5C,IAAK,CAAA,CAAA;AACnD,SAAA;AAEA;;AAEC,QACDsC,cAAiB,CAAA,GAAA;YACf,OAAOD,OAAAA,CAAQC,cAAc,CAACM,KAAAA,CAAAA,CAAAA;AAChC,SAAA;AAEA;;AAEC,QACD/f,GAAIlD,CAAAA,CAAAA,MAAM,EAAEqgB,IAAI,EAAE;YAChB,OAAOqC,OAAAA,CAAQxf,GAAG,CAAC+f,KAAO5C,EAAAA,IAAAA,CAAAA,CAAAA;AAC5B,SAAA;AAEA;;AAEC,QACDwC,OAAU,CAAA,GAAA;YACR,OAAOH,OAAAA,CAAQG,OAAO,CAACI,KAAAA,CAAAA,CAAAA;AACzB,SAAA;AAEA;;AAEC,QACDtZ,KAAI3J,MAAM,EAAEqgB,IAAI,EAAEjjB,KAAK,EAAE;AACvB6lB,YAAAA,KAAK,CAAC5C,IAAAA,CAAK,GAAGjjB,KAAAA,CAAAA;AACd,YAAA,OAAO4C,MAAM,CAACqgB,IAAK,CAAA,CAAA;AACnB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AACF,KAAA,CAAA,CAAA;AACF,CAAC;AAED;;AAEC,IACM,SAASlK,YACd8M,CAAAA,KAAoB,EACpBjT,QAA+B,GAAA;AAAC4T,IAAAA,UAAAA,EAAY,IAAI;AAAEC,IAAAA,SAAAA,EAAW,IAAI;AAAA,CAAC,EACtD;AACZ,IAAA,MAAM,EAACrT,WAAcR,EAAAA,QAAAA,CAAS4T,UAAU,GAAErT,UAAaP,EAAAA,QAAAA,CAAS6T,SAAS,GAAEC,QAAW9T,EAAAA,QAAAA,CAAS2T,OAAO,GAAC,GAAGV,KAAAA,CAAAA;IAC1G,OAAO;QACLU,OAASG,EAAAA,QAAAA;QACTF,UAAYpT,EAAAA,WAAAA;QACZqT,SAAWtT,EAAAA,UAAAA;AACXwT,QAAAA,YAAAA,EAAcnhB,UAAW4N,CAAAA,WAAAA,CAAAA,GAAeA,WAAc,GAAA,IAAMA,WAAW;AACvEwT,QAAAA,WAAAA,EAAaphB,UAAW2N,CAAAA,UAAAA,CAAAA,GAAcA,UAAa,GAAA,IAAMA,UAAU;AACrE,KAAA,CAAA;AACF,CAAC;AAED,MAAM0T,OAAAA,GAAU,CAACC,MAAgBzT,EAAAA,IAAAA,GAAiByT,SAASA,MAAS3hB,GAAAA,WAAAA,CAAYkO,QAAQA,IAAI,CAAA;AAC5F,MAAM0T,mBAAmB,CAAC9D,IAAAA,EAAcjjB,QAAmBS,QAAST,CAAAA,KAAAA,CAAAA,IAAUijB,SAAS,UACpF7iB,KAAAA,MAAOmlB,CAAAA,cAAc,CAACvlB,KAAW,CAAA,KAAA,IAAI,IAAIA,KAAM8Y,CAAAA,WAAW,KAAK1Y,MAAK,CAAA,CAAA;AAEvE,SAAS+kB,QACPviB,MAAiB,EACjBqgB,IAAY,EACZO,OAAsB,EACtB;IACA,IAAIpjB,MAAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAAA,EAAQqgB,IAASA,CAAAA,IAAAA,IAAAA,KAAS,aAAe,EAAA;QAChF,OAAOrgB,MAAM,CAACqgB,IAAK,CAAA,CAAA;KACpB;AAED,IAAA,MAAMjjB,KAAQwjB,GAAAA,OAAAA,EAAAA,CAAAA;;IAEd5gB,MAAM,CAACqgB,KAAK,GAAGjjB,KAAAA,CAAAA;IACf,OAAOA,KAAAA,CAAAA;AACT,CAAA;AAEA,SAASsmB,oBACP1jB,MAAoB,EACpBqgB,IAAY,EACZoD,QAAmB,EACnB;IACA,MAAM,EAACL,MAAM,GAAEC,QAAQ,GAAEC,YAAWnN,YAAAA,EAAcN,WAAW,GAAC,GAAG7V,MAAAA,CAAAA;AACjE,IAAA,IAAI5C,KAAQgmB,GAAAA,MAAM,CAAC/C,IAAAA,CAAK;;AAGxB,IAAA,IAAIzd,UAAWxF,CAAAA,KAAAA,CAAAA,IAAUyY,WAAYkO,CAAAA,YAAY,CAAC1D,IAAO,CAAA,EAAA;QACvDjjB,KAAQgnB,GAAAA,kBAAAA,CAAmB/D,IAAMjjB,EAAAA,KAAAA,EAAO4C,MAAQyjB,EAAAA,QAAAA,CAAAA,CAAAA;KACjD;AACD,IAAA,IAAIpmB,OAAQD,CAAAA,KAAAA,CAAAA,IAAUA,KAAMgC,CAAAA,MAAM,EAAE;AAClChC,QAAAA,KAAAA,GAAQinB,aAAchE,CAAAA,IAAAA,EAAMjjB,KAAO4C,EAAAA,MAAAA,EAAQ6V,YAAYmO,WAAW,CAAA,CAAA;KACnE;IACD,IAAIG,gBAAAA,CAAiB9D,MAAMjjB,KAAQ,CAAA,EAAA;;AAEjCA,QAAAA,KAAAA,GAAQ4lB,eAAe5lB,KAAOimB,EAAAA,QAAAA,EAAUC,aAAaA,SAAS,CAACjD,KAAK,EAAExK,WAAAA,CAAAA,CAAAA;KACvE;IACD,OAAOzY,KAAAA,CAAAA;AACT,CAAA;AAEA,SAASgnB,kBAAAA,CACP/D,IAAY,EACZiE,QAAqD,EACrDtkB,MAAoB,EACpByjB,QAAmB,EACnB;IACA,MAAM,EAACL,SAAQC,QAAAA,GAAUC,SAAS,GAAEC,MAAM,GAAC,GAAGvjB,MAAAA,CAAAA;IAC9C,IAAIujB,MAAAA,CAAOrgB,GAAG,CAACmd,IAAO,CAAA,EAAA;QACpB,MAAM,IAAIkE,KAAM,CAAA,sBAAA,GAAyBjnB,KAAMuM,CAAAA,IAAI,CAAC0Z,MAAAA,CAAAA,CAAQiB,IAAI,CAAC,IAAQ,CAAA,GAAA,IAAA,GAAOnE,IAAM,CAAA,CAAA;KACvF;AACDkD,IAAAA,MAAAA,CAAOnC,GAAG,CAACf,IAAAA,CAAAA,CAAAA;IACX,IAAIjjB,KAAAA,GAAQknB,QAASjB,CAAAA,QAAAA,EAAUC,SAAaG,IAAAA,QAAAA,CAAAA,CAAAA;AAC5CF,IAAAA,MAAAA,CAAOkB,MAAM,CAACpE,IAAAA,CAAAA,CAAAA;IACd,IAAI8D,gBAAAA,CAAiB9D,MAAMjjB,KAAQ,CAAA,EAAA;;AAEjCA,QAAAA,KAAAA,GAAQsnB,iBAAkBtB,CAAAA,MAAAA,CAAOnB,OAAO,EAAEmB,QAAQ/C,IAAMjjB,EAAAA,KAAAA,CAAAA,CAAAA;KACzD;IACD,OAAOA,KAAAA,CAAAA;AACT,CAAA;AAEA,SAASinB,aAAAA,CACPhE,IAAY,EACZjjB,KAAgB,EAChB4C,MAAoB,EACpBgkB,WAAqC,EACrC;IACA,MAAM,EAACZ,MAAM,GAAEC,QAAQ,GAAEC,YAAWnN,YAAAA,EAAcN,WAAW,GAAC,GAAG7V,MAAAA,CAAAA;AAEjE,IAAA,IAAI,OAAOqjB,QAASzjB,CAAAA,KAAK,KAAK,WAAA,IAAeokB,YAAY3D,IAAO,CAAA,EAAA;AAC9D,QAAA,OAAOjjB,KAAK,CAACimB,QAAAA,CAASzjB,KAAK,GAAGxC,KAAAA,CAAMgC,MAAM,CAAC,CAAA;AAC7C,KAAA,MAAO,IAAIvB,QAAAA,CAAST,KAAK,CAAC,EAAE,CAAG,EAAA;;AAE7B,QAAA,MAAMunB,GAAMvnB,GAAAA,KAAAA,CAAAA;QACZ,MAAMokB,MAAAA,GAAS4B,OAAOnB,OAAO,CAAC2C,MAAM,CAACvd,CAAAA,IAAKA,CAAMsd,KAAAA,GAAAA,CAAAA,CAAAA;AAChDvnB,QAAAA,KAAAA,GAAQ,EAAE,CAAA;QACV,KAAK,MAAM6F,QAAQ0hB,GAAK,CAAA;AACtB,YAAA,MAAMriB,QAAWoiB,GAAAA,iBAAAA,CAAkBlD,MAAQ4B,EAAAA,MAAAA,EAAQ/C,IAAMpd,EAAAA,IAAAA,CAAAA,CAAAA;YACzD7F,KAAM8E,CAAAA,IAAI,CAAC8gB,cAAe1gB,CAAAA,QAAAA,EAAU+gB,UAAUC,SAAaA,IAAAA,SAAS,CAACjD,IAAAA,CAAK,EAAExK,WAAAA,CAAAA,CAAAA,CAAAA;AAC9E,SAAA;KACD;IACD,OAAOzY,KAAAA,CAAAA;AACT,CAAA;AAEA,SAASynB,gBACPnE,QAA8F,EAC9FL,IAAuB,EACvBjjB,KAAc,EACd;AACA,IAAA,OAAOwF,UAAW8d,CAAAA,QAAAA,CAAAA,GAAYA,QAASL,CAAAA,IAAAA,EAAMjjB,SAASsjB,QAAQ,CAAA;AAChE,CAAA;AAEA,MAAM5K,QAAW,GAAA,CAACzV,GAAwBykB,EAAAA,MAAAA,GAAsBzkB,QAAQ,IAAI,GAAGykB,MAC3E,GAAA,OAAOzkB,GAAQ,KAAA,QAAA,GAAWgC,gBAAiByiB,CAAAA,MAAAA,EAAQzkB,OAAOgB,SAAS,CAAA;AAEvE,SAAS0jB,SAAAA,CACPpb,GAAmB,EACnBqb,YAAyB,EACzB3kB,GAAsB,EACtB4kB,cAAiC,EACjC7nB,KAAc,EACd;IACA,KAAK,MAAM0nB,UAAUE,YAAc,CAAA;QACjC,MAAM7jB,KAAAA,GAAQ2U,SAASzV,GAAKykB,EAAAA,MAAAA,CAAAA,CAAAA;AAC5B,QAAA,IAAI3jB,KAAO,EAAA;AACTwI,YAAAA,GAAAA,CAAIyX,GAAG,CAACjgB,KAAAA,CAAAA,CAAAA;AACR,YAAA,MAAMuf,QAAWmE,GAAAA,eAAAA,CAAgB1jB,KAAMmP,CAAAA,SAAS,EAAEjQ,GAAKjD,EAAAA,KAAAA,CAAAA,CAAAA;AACvD,YAAA,IAAI,OAAOsjB,QAAa,KAAA,WAAA,IAAeA,QAAargB,KAAAA,GAAAA,IAAOqgB,aAAauE,cAAgB,EAAA;;;gBAGtF,OAAOvE,QAAAA,CAAAA;aACR;SACI,MAAA,IAAIvf,UAAU,KAAK,IAAI,OAAO8jB,cAAmB,KAAA,WAAA,IAAe5kB,QAAQ4kB,cAAgB,EAAA;;;AAG7F,YAAA,OAAO,IAAI,CAAA;SACZ;AACH,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAEA,SAASP,iBAAAA,CACPM,YAAyB,EACzB1iB,QAAuB,EACvB+d,IAAuB,EACvBjjB,KAAc,EACd;IACA,MAAMskB,UAAAA,GAAapf,SAAS4f,WAAW,CAAA;AACvC,IAAA,MAAMxB,QAAWmE,GAAAA,eAAAA,CAAgBviB,QAASgO,CAAAA,SAAS,EAAE+P,IAAMjjB,EAAAA,KAAAA,CAAAA,CAAAA;AAC3D,IAAA,MAAM8nB,SAAY,GAAA;AAAIF,QAAAA,GAAAA,YAAAA;AAAiBtD,QAAAA,GAAAA,UAAAA;AAAW,KAAA,CAAA;AAClD,IAAA,MAAM/X,MAAM,IAAIC,GAAAA,EAAAA,CAAAA;AAChBD,IAAAA,GAAAA,CAAIyX,GAAG,CAAChkB,KAAAA,CAAAA,CAAAA;AACR,IAAA,IAAIiD,MAAM8kB,gBAAiBxb,CAAAA,GAAAA,EAAKub,SAAW7E,EAAAA,IAAAA,EAAMK,YAAYL,IAAMjjB,EAAAA,KAAAA,CAAAA,CAAAA;IACnE,IAAIiD,GAAAA,KAAQ,IAAI,EAAE;AAChB,QAAA,OAAO,KAAK,CAAA;KACb;AACD,IAAA,IAAI,OAAOqgB,QAAAA,KAAa,WAAeA,IAAAA,QAAAA,KAAaL,IAAM,EAAA;AACxDhgB,QAAAA,GAAAA,GAAM8kB,gBAAiBxb,CAAAA,GAAAA,EAAKub,SAAWxE,EAAAA,QAAAA,EAAUrgB,GAAKjD,EAAAA,KAAAA,CAAAA,CAAAA;QACtD,IAAIiD,GAAAA,KAAQ,IAAI,EAAE;AAChB,YAAA,OAAO,KAAK,CAAA;SACb;KACF;AACD,IAAA,OAAOkhB,eAAgBjkB,CAAAA,KAAAA,CAAMuM,IAAI,CAACF,GAAM,CAAA,EAAA;AAAC,QAAA,EAAA;AAAG,KAAA,EAAE+X,UAAYhB,EAAAA,QAAAA,EACxD,IAAM0E,YAAAA,CAAa9iB,UAAU+d,IAAgBjjB,EAAAA,KAAAA,CAAAA,CAAAA,CAAAA;AACjD,CAAA;AAEA,SAAS+nB,gBAAAA,CACPxb,GAAmB,EACnBub,SAAsB,EACtB7kB,GAAsB,EACtBqgB,QAA2B,EAC3Bzd,IAAa,EACb;AACA,IAAA,MAAO5C,GAAK,CAAA;AACVA,QAAAA,GAAAA,GAAM0kB,SAAUpb,CAAAA,GAAAA,EAAKub,SAAW7kB,EAAAA,GAAAA,EAAKqgB,QAAUzd,EAAAA,IAAAA,CAAAA,CAAAA;AACjD,KAAA;IACA,OAAO5C,GAAAA,CAAAA;AACT,CAAA;AAEA,SAAS+kB,aACP9iB,QAAuB,EACvB+d,IAAY,EACZjjB,KAAc,EACd;IACA,MAAM0nB,MAAAA,GAASxiB,SAAS6f,UAAU,EAAA,CAAA;AAClC,IAAA,IAAI,EAAE9B,IAAQyE,IAAAA,MAAK,CAAI,EAAA;QACrBA,MAAM,CAACzE,IAAK,CAAA,GAAG,EAAC,CAAA;KACjB;IACD,MAAMrgB,MAAAA,GAAS8kB,MAAM,CAACzE,IAAK,CAAA,CAAA;IAC3B,IAAIhjB,OAAAA,CAAQ2C,MAAWnC,CAAAA,IAAAA,QAAAA,CAAST,KAAQ,CAAA,EAAA;;QAEtC,OAAOA,KAAAA,CAAAA;KACR;AACD,IAAA,OAAO4C,UAAU,EAAC,CAAA;AACpB,CAAA;AAEA,SAASwiB,oBAAAA,CACPnC,IAAY,EACZoB,QAAkB,EAClBD,MAAmB,EACnByB,KAAoB,EACpB;IACA,IAAI7lB,KAAAA,CAAAA;IACJ,KAAK,MAAM8mB,UAAUzC,QAAU,CAAA;QAC7BrkB,KAAQykB,GAAAA,QAAAA,CAASoC,OAAQC,CAAAA,MAAAA,EAAQ7D,IAAOmB,CAAAA,EAAAA,MAAAA,CAAAA,CAAAA;QACxC,IAAI,OAAOpkB,UAAU,WAAa,EAAA;YAChC,OAAO+mB,gBAAAA,CAAiB9D,MAAMjjB,KAC1BsnB,CAAAA,GAAAA,iBAAAA,CAAkBlD,QAAQyB,KAAO5C,EAAAA,IAAAA,EAAMjjB,SACvCA,KAAK,CAAA;SACV;AACH,KAAA;AACF,CAAA;AAEA,SAASykB,QAASxhB,CAAAA,GAAW,EAAEmhB,MAAmB,EAAE;IAClD,KAAK,MAAMrgB,SAASqgB,MAAQ,CAAA;AAC1B,QAAA,IAAI,CAACrgB,KAAO,EAAA;YACV,SAAS;SACV;QACD,MAAM/D,KAAAA,GAAQ+D,KAAK,CAACd,GAAI,CAAA,CAAA;QACxB,IAAI,OAAOjD,UAAU,WAAa,EAAA;YAChC,OAAOA,KAAAA,CAAAA;SACR;AACH,KAAA;AACF,CAAA;AAEA,SAASwlB,oBAAAA,CAAqB5iB,MAAqB,EAAE;IACnD,IAAIb,IAAAA,GAAOa,OAAOsiB,KAAK,CAAA;AACvB,IAAA,IAAI,CAACnjB,IAAM,EAAA;AACTA,QAAAA,IAAAA,GAAOa,MAAOsiB,CAAAA,KAAK,GAAG+C,wBAAAA,CAAyBrlB,OAAOiiB,OAAO,CAAA,CAAA;KAC9D;IACD,OAAO9iB,IAAAA,CAAAA;AACT,CAAA;AAEA,SAASkmB,wBAAAA,CAAyB7D,MAAmB,EAAE;AACrD,IAAA,MAAM7X,MAAM,IAAIC,GAAAA,EAAAA,CAAAA;IAChB,KAAK,MAAMzI,SAASqgB,MAAQ,CAAA;AAC1B,QAAA,KAAK,MAAMnhB,GAAAA,IAAO7C,MAAO2B,CAAAA,IAAI,CAACgC,KAAOyjB,CAAAA,CAAAA,MAAM,CAACzkB,CAAAA,CAAK,GAAA,CAACA,CAAEwV,CAAAA,UAAU,CAAC,GAAO,CAAA,CAAA,CAAA;AACpEhM,YAAAA,GAAAA,CAAIyX,GAAG,CAAC/gB,GAAAA,CAAAA,CAAAA;AACV,SAAA;AACF,KAAA;IACA,OAAO/C,KAAAA,CAAMuM,IAAI,CAACF,GAAAA,CAAAA,CAAAA;AACpB,CAAA;AAEO,SAAS2b,4BACdha,IAAmC,EACnC6N,IAAiB,EACjBjS,KAAa,EACbwE,KAAa,EACb;IACA,MAAM,EAACE,MAAM,GAAC,GAAGN,IAAAA,CAAAA;AACjB,IAAA,MAAM,EAACjL,GAAM,EAAA,GAAA,GAAI,GAAG,IAAI,CAACklB,QAAQ,CAAA;IACjC,MAAMC,MAAAA,GAAS,IAAIloB,KAAoBoO,CAAAA,KAAAA,CAAAA,CAAAA;IACvC,IAAIzM,CAAAA,EAAWO,MAAcI,KAAeqD,EAAAA,IAAAA,CAAAA;IAE5C,IAAKhE,CAAAA,GAAI,GAAGO,IAAOkM,GAAAA,KAAK,EAAEzM,CAAIO,GAAAA,IAAAA,EAAM,EAAEP,CAAG,CAAA;AACvCW,QAAAA,KAAAA,GAAQX,CAAIiI,GAAAA,KAAAA,CAAAA;QACZjE,IAAOkW,GAAAA,IAAI,CAACvZ,KAAM,CAAA,CAAA;QAClB4lB,MAAM,CAACvmB,EAAE,GAAG;AACVwmB,YAAAA,CAAAA,EAAG7Z,MAAO8Z,CAAAA,KAAK,CAACrjB,gBAAAA,CAAiBY,MAAM5C,GAAMT,CAAAA,EAAAA,KAAAA,CAAAA;AAC/C,SAAA,CAAA;AACF,KAAA;IACA,OAAO4lB,MAAAA,CAAAA;AACT;;AClcA,MAAMG,OAAAA,GAAU5nB,MAAO4nB,CAAAA,OAAO,IAAI,KAAA,CAAA;AAGlC,MAAMC,WAAW,CAACra,MAAAA,EAAuBtM,CAAmCA,GAAAA,CAAAA,GAAIsM,OAAOnM,MAAM,IAAI,CAACmM,MAAM,CAACtM,CAAE,CAAA,CAAC4mB,IAAI,IAAIta,MAAM,CAACtM,CAAE,CAAA,CAAA;AAC7H,MAAM6mB,eAAe,CAACtO,SAAAA,GAAyBA,SAAc,KAAA,GAAA,GAAM,MAAM,GAAG,CAAA;AAErE,SAASuO,YACdC,UAAuB,EACvBC,WAAwB,EACxBC,UAAuB,EACvBnZ,CAAS,EAIP;;;;AAMF,IAAA,MAAM3L,QAAW4kB,GAAAA,UAAAA,CAAWH,IAAI,GAAGI,cAAcD,UAAU,CAAA;AAC3D,IAAA,MAAMllB,OAAUmlB,GAAAA,WAAAA,CAAAA;AAChB,IAAA,MAAME,IAAOD,GAAAA,UAAAA,CAAWL,IAAI,GAAGI,cAAcC,UAAU,CAAA;IACvD,MAAME,GAAAA,GAAMxf,sBAAsB9F,OAASM,EAAAA,QAAAA,CAAAA,CAAAA;IAC3C,MAAMilB,GAAAA,GAAMzf,sBAAsBuf,IAAMrlB,EAAAA,OAAAA,CAAAA,CAAAA;AAExC,IAAA,IAAIwlB,GAAMF,GAAAA,GAAAA,IAAOA,GAAAA,GAAMC,GAAE,CAAA,CAAA;AACzB,IAAA,IAAIE,GAAMF,GAAAA,GAAAA,IAAOD,GAAAA,GAAMC,GAAE,CAAA,CAAA;;IAGzBC,GAAMlhB,GAAAA,KAAAA,CAAMkhB,GAAO,CAAA,GAAA,CAAA,GAAIA,GAAG,CAAA;IAC1BC,GAAMnhB,GAAAA,KAAAA,CAAMmhB,GAAO,CAAA,GAAA,CAAA,GAAIA,GAAG,CAAA;IAE1B,MAAMC,EAAAA,GAAKzZ,CAAIuZ,GAAAA,GAAAA,CAAAA;AACf,IAAA,MAAMG,KAAK1Z,CAAIwZ,GAAAA,GAAAA,CAAAA;IAEf,OAAO;QACLnlB,QAAU,EAAA;YACRM,CAAGZ,EAAAA,OAAAA,CAAQY,CAAC,GAAG8kB,EAAML,IAAAA,KAAKzkB,CAAC,GAAGN,QAASM,CAAAA,CAAC,CAADA;YACvCE,CAAGd,EAAAA,OAAAA,CAAQc,CAAC,GAAG4kB,EAAML,IAAAA,KAAKvkB,CAAC,GAAGR,QAASQ,CAAAA,CAAC,CAADA;AACzC,SAAA;QACAukB,IAAM,EAAA;YACJzkB,CAAGZ,EAAAA,OAAAA,CAAQY,CAAC,GAAG+kB,EAAMN,IAAAA,KAAKzkB,CAAC,GAAGN,QAASM,CAAAA,CAAC,CAADA;YACvCE,CAAGd,EAAAA,OAAAA,CAAQc,CAAC,GAAG6kB,EAAMN,IAAAA,KAAKvkB,CAAC,GAAGR,QAASQ,CAAAA,CAAC,CAADA;AACzC,SAAA;AACF,KAAA,CAAA;AACF,CAAC;AAED;;AAEC,IACD,SAAS8kB,cAAenb,CAAAA,MAAqB,EAAEob,MAAgB,EAAEC,EAAY,EAAE;IAC7E,MAAMC,SAAAA,GAAYtb,OAAOnM,MAAM,CAAA;IAE/B,IAAI0nB,MAAAA,EAAgBC,KAAeC,EAAAA,IAAAA,EAAcC,gBAA0BC,EAAAA,YAAAA,CAAAA;IAC3E,IAAIC,UAAAA,GAAavB,SAASra,MAAQ,EAAA,CAAA,CAAA,CAAA;AAClC,IAAA,IAAK,IAAItM,CAAI,GAAA,CAAA,EAAGA,IAAI4nB,SAAY,GAAA,CAAA,EAAG,EAAE5nB,CAAG,CAAA;QACtCioB,YAAeC,GAAAA,UAAAA,CAAAA;QACfA,UAAavB,GAAAA,QAAAA,CAASra,QAAQtM,CAAI,GAAA,CAAA,CAAA,CAAA;QAClC,IAAI,CAACioB,YAAgB,IAAA,CAACC,UAAY,EAAA;YAChC,SAAS;SACV;AAED,QAAA,IAAIljB,aAAa0iB,MAAM,CAAC1nB,CAAE,CAAA,EAAE,GAAG0mB,OAAU,CAAA,EAAA;AACvCiB,YAAAA,EAAE,CAAC3nB,CAAE,CAAA,GAAG2nB,EAAE,CAAC3nB,CAAAA,GAAI,EAAE,GAAG,CAAA,CAAA;YACpB,SAAS;SACV;AAED6nB,QAAAA,MAAAA,GAASF,EAAE,CAAC3nB,CAAAA,CAAE,GAAG0nB,MAAM,CAAC1nB,CAAE,CAAA,CAAA;AAC1B8nB,QAAAA,KAAAA,GAAQH,EAAE,CAAC3nB,CAAAA,GAAI,EAAE,GAAG0nB,MAAM,CAAC1nB,CAAE,CAAA,CAAA;QAC7BgoB,gBAAmB3jB,GAAAA,IAAAA,CAAKmB,GAAG,CAACqiB,MAAAA,EAAQ,KAAKxjB,IAAKmB,CAAAA,GAAG,CAACsiB,KAAO,EAAA,CAAA,CAAA,CAAA;AACzD,QAAA,IAAIE,oBAAoB,CAAG,EAAA;YACzB,SAAS;SACV;QAEDD,IAAO,GAAA,CAAA,GAAI1jB,IAAKyB,CAAAA,IAAI,CAACkiB,gBAAAA,CAAAA,CAAAA;AACrBL,QAAAA,EAAE,CAAC3nB,CAAE,CAAA,GAAG6nB,SAASE,IAAOL,GAAAA,MAAM,CAAC1nB,CAAE,CAAA,CAAA;QACjC2nB,EAAE,CAAC3nB,IAAI,CAAE,CAAA,GAAG8nB,QAAQC,IAAOL,GAAAA,MAAM,CAAC1nB,CAAE,CAAA,CAAA;AACtC,KAAA;AACF,CAAA;AAEA,SAASmoB,gBAAgB7b,MAAqB,EAAEqb,EAAY,EAAEpP,SAAAA,GAAuB,GAAG,EAAE;AACxF,IAAA,MAAM6P,YAAYvB,YAAatO,CAAAA,SAAAA,CAAAA,CAAAA;IAC/B,MAAMqP,SAAAA,GAAYtb,OAAOnM,MAAM,CAAA;AAC/B,IAAA,IAAIuT,OAAe2U,WAAkCJ,EAAAA,YAAAA,CAAAA;IACrD,IAAIC,UAAAA,GAAavB,SAASra,MAAQ,EAAA,CAAA,CAAA,CAAA;AAElC,IAAA,IAAK,IAAItM,CAAI,GAAA,CAAA,EAAGA,CAAI4nB,GAAAA,SAAAA,EAAW,EAAE5nB,CAAG,CAAA;QAClCqoB,WAAcJ,GAAAA,YAAAA,CAAAA;QACdA,YAAeC,GAAAA,UAAAA,CAAAA;QACfA,UAAavB,GAAAA,QAAAA,CAASra,QAAQtM,CAAI,GAAA,CAAA,CAAA,CAAA;AAClC,QAAA,IAAI,CAACioB,YAAc,EAAA;YACjB,SAAS;SACV;QAED,MAAMK,MAAAA,GAASL,YAAY,CAAC1P,SAAU,CAAA,CAAA;QACtC,MAAMgQ,MAAAA,GAASN,YAAY,CAACG,SAAU,CAAA,CAAA;AACtC,QAAA,IAAIC,WAAa,EAAA;AACf3U,YAAAA,KAAAA,GAAQ,CAAC4U,MAAAA,GAASD,WAAW,CAAC9P,SAAAA,CAAU,IAAI,CAAA,CAAA;YAC5C0P,YAAY,CAAC,CAAC,GAAG,EAAE1P,UAAU,CAAC,CAAC,GAAG+P,MAAS5U,GAAAA,KAAAA,CAAAA;AAC3CuU,YAAAA,YAAY,CAAC,CAAC,GAAG,EAAEG,SAAU,CAAA,CAAC,CAAC,GAAGG,MAAS7U,GAAAA,KAAAA,GAAQiU,EAAE,CAAC3nB,CAAE,CAAA,CAAA;SACzD;AACD,QAAA,IAAIkoB,UAAY,EAAA;AACdxU,YAAAA,KAAAA,GAAQ,CAACwU,UAAU,CAAC3P,SAAU,CAAA,GAAG+P,MAAK,IAAK,CAAA,CAAA;YAC3CL,YAAY,CAAC,CAAC,GAAG,EAAE1P,UAAU,CAAC,CAAC,GAAG+P,MAAS5U,GAAAA,KAAAA,CAAAA;AAC3CuU,YAAAA,YAAY,CAAC,CAAC,GAAG,EAAEG,SAAU,CAAA,CAAC,CAAC,GAAGG,MAAS7U,GAAAA,KAAAA,GAAQiU,EAAE,CAAC3nB,CAAE,CAAA,CAAA;SACzD;AACH,KAAA;AACF,CAAA;AAEA;;;;;AAKC,IACM,SAASwoB,mBAAAA,CAAoBlc,MAAqB,EAAEiM,SAAAA,GAAuB,GAAG,EAAE;AACrF,IAAA,MAAM6P,YAAYvB,YAAatO,CAAAA,SAAAA,CAAAA,CAAAA;IAC/B,MAAMqP,SAAAA,GAAYtb,OAAOnM,MAAM,CAAA;AAC/B,IAAA,MAAMunB,MAAmBrpB,GAAAA,KAAAA,CAAMupB,SAAWxK,CAAAA,CAAAA,IAAI,CAAC,CAAA,CAAA,CAAA;AAC/C,IAAA,MAAMuK,KAAetpB,KAAMupB,CAAAA,SAAAA,CAAAA,CAAAA;;AAG3B,IAAA,IAAI5nB,GAAGqoB,WAAkCJ,EAAAA,YAAAA,CAAAA;IACzC,IAAIC,UAAAA,GAAavB,SAASra,MAAQ,EAAA,CAAA,CAAA,CAAA;AAElC,IAAA,IAAKtM,CAAI,GAAA,CAAA,EAAGA,CAAI4nB,GAAAA,SAAAA,EAAW,EAAE5nB,CAAG,CAAA;QAC9BqoB,WAAcJ,GAAAA,YAAAA,CAAAA;QACdA,YAAeC,GAAAA,UAAAA,CAAAA;QACfA,UAAavB,GAAAA,QAAAA,CAASra,QAAQtM,CAAI,GAAA,CAAA,CAAA,CAAA;AAClC,QAAA,IAAI,CAACioB,YAAc,EAAA;YACjB,SAAS;SACV;AAED,QAAA,IAAIC,UAAY,EAAA;AACd,YAAA,MAAMO,aAAaP,UAAU,CAAC3P,UAAU,GAAG0P,YAAY,CAAC1P,SAAU,CAAA,CAAA;;AAGlEmP,YAAAA,MAAM,CAAC1nB,CAAE,CAAA,GAAGyoB,UAAe,KAAA,CAAA,GAAI,CAACP,UAAU,CAACE,SAAAA,CAAU,GAAGH,YAAY,CAACG,UAAU,IAAIK,aAAa,CAAC,CAAA;SAClG;AACDd,QAAAA,EAAE,CAAC3nB,CAAE,CAAA,GAAG,CAACqoB,WAAcX,GAAAA,MAAM,CAAC1nB,CAAE,CAAA,GAC5B,CAACkoB,UAAAA,GAAaR,MAAM,CAAC1nB,CAAAA,GAAI,EAAE,GACxB+E,KAAK2iB,MAAM,CAAC1nB,CAAI,GAAA,CAAA,CAAE,MAAM+E,IAAK2iB,CAAAA,MAAM,CAAC1nB,CAAE,CAAA,CAAA,GAAK,IAC1C,CAAC0nB,MAAM,CAAC1nB,CAAAA,GAAI,EAAE,GAAG0nB,MAAM,CAAC1nB,CAAE,CAAD,IAAK,CAAC,CAAA;AACzC,KAAA;AAEAynB,IAAAA,cAAAA,CAAenb,QAAQob,MAAQC,EAAAA,EAAAA,CAAAA,CAAAA;AAE/BQ,IAAAA,eAAAA,CAAgB7b,QAAQqb,EAAIpP,EAAAA,SAAAA,CAAAA,CAAAA;AAC9B,CAAC;AAED,SAASmQ,gBAAgBC,EAAU,EAAEliB,GAAW,EAAEC,GAAW,EAAE;AAC7D,IAAA,OAAOrC,KAAKqC,GAAG,CAACrC,KAAKoC,GAAG,CAACkiB,IAAIjiB,GAAMD,CAAAA,EAAAA,GAAAA,CAAAA,CAAAA;AACrC,CAAA;AAEA,SAASmiB,eAAgBtc,CAAAA,MAAqB,EAAEmR,IAAe,EAAE;IAC/D,IAAIzd,CAAAA,EAAGO,IAAMid,EAAAA,KAAAA,EAAOqL,MAAQC,EAAAA,UAAAA,CAAAA;AAC5B,IAAA,IAAIC,UAAaxL,GAAAA,cAAAA,CAAejR,MAAM,CAAC,EAAE,EAAEmR,IAAAA,CAAAA,CAAAA;IAC3C,IAAKzd,CAAAA,GAAI,GAAGO,IAAO+L,GAAAA,MAAAA,CAAOnM,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;QAC/C8oB,UAAaD,GAAAA,MAAAA,CAAAA;QACbA,MAASE,GAAAA,UAAAA,CAAAA;QACTA,UAAa/oB,GAAAA,CAAAA,GAAIO,OAAO,CAAKgd,IAAAA,cAAAA,CAAejR,MAAM,CAACtM,CAAAA,GAAI,EAAE,EAAEyd,IAAAA,CAAAA,CAAAA;AAC3D,QAAA,IAAI,CAACoL,MAAQ,EAAA;YACX,SAAS;SACV;QACDrL,KAAQlR,GAAAA,MAAM,CAACtM,CAAE,CAAA,CAAA;AACjB,QAAA,IAAI8oB,UAAY,EAAA;YACdtL,KAAMU,CAAAA,IAAI,GAAGwK,eAAAA,CAAgBlL,KAAMU,CAAAA,IAAI,EAAET,IAAKzR,CAAAA,IAAI,EAAEyR,IAAAA,CAAKxR,KAAK,CAAA,CAAA;YAC9DuR,KAAMY,CAAAA,IAAI,GAAGsK,eAAAA,CAAgBlL,KAAMY,CAAAA,IAAI,EAAEX,IAAKrL,CAAAA,GAAG,EAAEqL,IAAAA,CAAKpL,MAAM,CAAA,CAAA;SAC/D;AACD,QAAA,IAAI0W,UAAY,EAAA;YACdvL,KAAMW,CAAAA,IAAI,GAAGuK,eAAAA,CAAgBlL,KAAMW,CAAAA,IAAI,EAAEV,IAAKzR,CAAAA,IAAI,EAAEyR,IAAAA,CAAKxR,KAAK,CAAA,CAAA;YAC9DuR,KAAMa,CAAAA,IAAI,GAAGqK,eAAAA,CAAgBlL,KAAMa,CAAAA,IAAI,EAAEZ,IAAKrL,CAAAA,GAAG,EAAEqL,IAAAA,CAAKpL,MAAM,CAAA,CAAA;SAC/D;AACH,KAAA;AACF,CAAA;AAEA;;AAEC,IACM,SAAS2W,0BACd1c,CAAAA,MAAqB,EACrB/K,OAAO,EACPkc,IAAe,EACfvM,IAAa,EACbqH,SAAoB,EACpB;IACA,IAAIvY,CAAAA,EAAWO,MAAcid,KAAoByL,EAAAA,aAAAA,CAAAA;;IAGjD,IAAI1nB,OAAAA,CAAQ2nB,QAAQ,EAAE;AACpB5c,QAAAA,MAAAA,GAASA,OAAOqZ,MAAM,CAAC,CAACgD,EAAO,GAAA,CAACA,GAAG/B,IAAI,CAAA,CAAA;KACxC;IAED,IAAIrlB,OAAAA,CAAQ4nB,sBAAsB,KAAK,UAAY,EAAA;AACjDX,QAAAA,mBAAAA,CAAoBlc,MAAQiM,EAAAA,SAAAA,CAAAA,CAAAA;KACvB,MAAA;QACL,IAAI6Q,IAAAA,GAAOlY,IAAO5E,GAAAA,MAAM,CAACA,MAAAA,CAAOnM,MAAM,GAAG,CAAE,CAAA,GAAGmM,MAAM,CAAC,CAAE,CAAA,CAAA;QACvD,IAAKtM,CAAAA,GAAI,GAAGO,IAAO+L,GAAAA,MAAAA,CAAOnM,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;YAC/Cwd,KAAQlR,GAAAA,MAAM,CAACtM,CAAE,CAAA,CAAA;YACjBipB,aAAgBnC,GAAAA,WAAAA,CACdsC,MACA5L,KACAlR,EAAAA,MAAM,CAACjI,IAAKoC,CAAAA,GAAG,CAACzG,CAAI,GAAA,CAAA,EAAGO,QAAQ2Q,IAAAA,GAAO,IAAI,CAAA,KAAM3Q,IAAK,CAAA,EACrDgB,QAAQ8nB,OAAO,CAAA,CAAA;AAEjB7L,YAAAA,KAAAA,CAAMU,IAAI,GAAG+K,aAAc9mB,CAAAA,QAAQ,CAACM,CAAC,CAAA;AACrC+a,YAAAA,KAAAA,CAAMY,IAAI,GAAG6K,aAAc9mB,CAAAA,QAAQ,CAACQ,CAAC,CAAA;AACrC6a,YAAAA,KAAAA,CAAMW,IAAI,GAAG8K,aAAc/B,CAAAA,IAAI,CAACzkB,CAAC,CAAA;AACjC+a,YAAAA,KAAAA,CAAMa,IAAI,GAAG4K,aAAc/B,CAAAA,IAAI,CAACvkB,CAAC,CAAA;YACjCymB,IAAO5L,GAAAA,KAAAA,CAAAA;AACT,SAAA;KACD;IAED,IAAIjc,OAAAA,CAAQqnB,eAAe,EAAE;AAC3BA,QAAAA,eAAAA,CAAgBtc,MAAQmR,EAAAA,IAAAA,CAAAA,CAAAA;KACzB;AACH;;ACzNA;;;;;;;;IAWO,SAAS6L,eAA2B,GAAA;AACzC,IAAA,OAAO,OAAOpe,MAAAA,KAAW,WAAe,IAAA,OAAOqe,QAAa,KAAA,WAAA,CAAA;AAC9D,CAAC;AAED;;AAEC,IACM,SAASC,cAAeC,CAAAA,OAA0B,EAAqB;IAC5E,IAAI5D,MAAAA,GAAS4D,QAAQC,UAAU,CAAA;AAC/B,IAAA,IAAI7D,MAAUA,IAAAA,MAAAA,CAAOpnB,QAAQ,EAAA,KAAO,qBAAuB,EAAA;QACzDonB,MAAS,GAACA,OAAsB8D,IAAI,CAAA;KACrC;IACD,OAAO9D,MAAAA,CAAAA;AACT,CAAC;AAED;;;AAGC,IAED,SAAS+D,aAAcC,CAAAA,UAA2B,EAAE/S,IAAiB,EAAEgT,cAAsB,EAAE;IAC7F,IAAIC,aAAAA,CAAAA;IACJ,IAAI,OAAOF,eAAe,QAAU,EAAA;AAClCE,QAAAA,aAAAA,GAAgBrI,SAASmI,UAAY,EAAA,EAAA,CAAA,CAAA;AAErC,QAAA,IAAIA,UAAWxoB,CAAAA,OAAO,CAAC,GAAA,CAAA,KAAS,CAAC,CAAG,EAAA;;AAElC0oB,YAAAA,aAAAA,GAAgB,aAAiB,GAAA,GAAA,GAAOjT,IAAK4S,CAAAA,UAAU,CAACI,cAAe,CAAA,CAAA;SACxE;KACI,MAAA;QACLC,aAAgBF,GAAAA,UAAAA,CAAAA;KACjB;IAED,OAAOE,aAAAA,CAAAA;AACT,CAAA;AAEA,MAAMC,gBAAAA,GAAmB,CAACC,OAAAA,GACxBA,OAAQC,CAAAA,aAAa,CAACC,WAAW,CAACH,gBAAgB,CAACC,OAAAA,EAAS,IAAI,CAAA,CAAA;AAE3D,SAASG,QAAAA,CAASC,EAAe,EAAE7jB,QAAgB,EAAU;IAClE,OAAOwjB,gBAAAA,CAAiBK,EAAIC,CAAAA,CAAAA,gBAAgB,CAAC9jB,QAAAA,CAAAA,CAAAA;AAC/C,CAAC;AAED,MAAM+jB,SAAY,GAAA;AAAC,IAAA,KAAA;AAAO,IAAA,OAAA;AAAS,IAAA,QAAA;AAAU,IAAA,MAAA;AAAO,CAAA,CAAA;AACpD,SAASC,mBAAmBC,MAA2B,EAAE1S,KAAa,EAAE2S,MAAe,EAAa;AAClG,IAAA,MAAM7kB,SAAS,EAAC,CAAA;IAChB6kB,MAASA,GAAAA,MAAAA,GAAS,GAAMA,GAAAA,MAAAA,GAAS,EAAE,CAAA;AACnC,IAAA,IAAK,IAAI1qB,CAAAA,GAAI,CAAGA,EAAAA,CAAAA,GAAI,GAAGA,CAAK,EAAA,CAAA;QAC1B,MAAM2qB,GAAAA,GAAMJ,SAAS,CAACvqB,CAAE,CAAA,CAAA;QACxB6F,MAAM,CAAC8kB,GAAI,CAAA,GAAGrrB,UAAWmrB,CAAAA,MAAM,CAAC1S,KAAQ,GAAA,GAAA,GAAM4S,GAAMD,GAAAA,MAAAA,CAAO,CAAK,IAAA,CAAA,CAAA;AAClE,KAAA;AACA7kB,IAAAA,MAAAA,CAAO0P,KAAK,GAAG1P,MAAAA,CAAOmG,IAAI,GAAGnG,OAAOoG,KAAK,CAAA;AACzCpG,IAAAA,MAAAA,CAAO+V,MAAM,GAAG/V,MAAAA,CAAOuM,GAAG,GAAGvM,OAAOwM,MAAM,CAAA;IAC1C,OAAOxM,MAAAA,CAAAA;AACT,CAAA;AAEA,MAAM+kB,eAAe,CAACnoB,CAAAA,EAAWE,GAAW5B,MAC1C,GAAC0B,CAAAA,CAAI,GAAA,CAAA,IAAKE,IAAI,CAAA,MAAO,CAAC5B,MAAAA,IAAU,CAAC,MAACA,CAAuB8pB,UAAU,CAAD,CAAA;AAEpE;;;;AAIC,IACD,SAASC,iBAAAA,CACP3mB,CAAkC,EAClCqX,MAAyB,EAKvB;IACF,MAAMuP,OAAAA,GAAU,CAAC5mB,CAAiB4mB,OAAO,CAAA;IACzC,MAAMlqB,MAAAA,GAAUkqB,WAAWA,OAAQ5qB,CAAAA,MAAM,GAAG4qB,OAAO,CAAC,CAAE,CAAA,GAAG5mB,CAAC,CAAA;AAC1D,IAAA,MAAM,EAAC6mB,OAAAA,GAASC,OAAAA,GAAQ,GAAGpqB,MAAAA,CAAAA;AAC3B,IAAA,IAAIqqB,MAAM,KAAK,CAAA;AACf,IAAA,IAAIzoB,CAAGE,EAAAA,CAAAA,CAAAA;AACP,IAAA,IAAIioB,YAAaI,CAAAA,OAAAA,EAASC,OAAS9mB,EAAAA,CAAAA,CAAEpD,MAAM,CAAG,EAAA;QAC5C0B,CAAIuoB,GAAAA,OAAAA,CAAAA;QACJroB,CAAIsoB,GAAAA,OAAAA,CAAAA;KACC,MAAA;QACL,MAAM9N,IAAAA,GAAO3B,OAAO2P,qBAAqB,EAAA,CAAA;AACzC1oB,QAAAA,CAAAA,GAAI5B,MAAOuqB,CAAAA,OAAO,GAAGjO,IAAAA,CAAKnR,IAAI,CAAA;AAC9BrJ,QAAAA,CAAAA,GAAI9B,MAAOwqB,CAAAA,OAAO,GAAGlO,IAAAA,CAAK/K,GAAG,CAAA;AAC7B8Y,QAAAA,GAAAA,GAAM,IAAI,CAAA;KACX;IACD,OAAO;AAACzoB,QAAAA,CAAAA;AAAGE,QAAAA,CAAAA;AAAGuoB,QAAAA,GAAAA;AAAG,KAAA,CAAA;AACnB,CAAA;AAEA;;;;;AAKC,IAEM,SAASI,mBAAAA,CACdC,KAAmD,EACnD/X,KAAY,EACc;AAC1B,IAAA,IAAI,YAAY+X,KAAO,EAAA;QACrB,OAAOA,KAAAA,CAAAA;KACR;AAED,IAAA,MAAM,EAAC/P,MAAAA,GAAQH,uBAAAA,GAAwB,GAAG7H,KAAAA,CAAAA;AAC1C,IAAA,MAAMuE,QAAQiS,gBAAiBxO,CAAAA,MAAAA,CAAAA,CAAAA;IAC/B,MAAMgQ,SAAAA,GAAYzT,KAAM0T,CAAAA,SAAS,KAAK,YAAA,CAAA;IACtC,MAAMC,QAAAA,GAAWlB,mBAAmBzS,KAAO,EAAA,SAAA,CAAA,CAAA;IAC3C,MAAM4T,OAAAA,GAAUnB,kBAAmBzS,CAAAA,KAAAA,EAAO,QAAU,EAAA,OAAA,CAAA,CAAA;IACpD,MAAM,EAACtV,IAAGE,CAAAA,GAAGuoB,GAAG,GAAC,GAAGJ,iBAAAA,CAAkBS,KAAO/P,EAAAA,MAAAA,CAAAA,CAAAA;IAC7C,MAAMQ,OAAAA,GAAU0P,SAAS1f,IAAI,IAAIkf,GAAOS,IAAAA,OAAAA,CAAQ3f,IAAI,CAAD,CAAA;IACnD,MAAMiQ,OAAAA,GAAUyP,SAAStZ,GAAG,IAAI8Y,GAAOS,IAAAA,OAAAA,CAAQvZ,GAAG,CAAD,CAAA;AAEjD,IAAA,IAAI,EAACmD,KAAAA,GAAOqG,MAAAA,GAAO,GAAGpI,KAAAA,CAAAA;AACtB,IAAA,IAAIgY,SAAW,EAAA;AACbjW,QAAAA,KAAAA,IAASmW,QAASnW,CAAAA,KAAK,GAAGoW,OAAAA,CAAQpW,KAAK,CAAA;AACvCqG,QAAAA,MAAAA,IAAU8P,QAAS9P,CAAAA,MAAM,GAAG+P,OAAAA,CAAQ/P,MAAM,CAAA;KAC3C;IACD,OAAO;QACLnZ,CAAG4B,EAAAA,IAAAA,CAAKiB,KAAK,CAAC,CAAC7C,CAAIuZ,GAAAA,OAAM,IAAKzG,KAAAA,GAAQiG,MAAOjG,CAAAA,KAAK,GAAG8F,uBAAAA,CAAAA;QACrD1Y,CAAG0B,EAAAA,IAAAA,CAAKiB,KAAK,CAAC,CAAC3C,CAAIsZ,GAAAA,OAAM,IAAKL,MAAAA,GAASJ,MAAOI,CAAAA,MAAM,GAAGP,uBAAAA,CAAAA;AACzD,KAAA,CAAA;AACF,CAAC;AAED,SAASuQ,iBAAiBpQ,MAAyB,EAAEjG,KAAa,EAAEqG,MAAc,EAAkB;AAClG,IAAA,IAAIqE,QAAkB4L,EAAAA,SAAAA,CAAAA;IAEtB,IAAItW,KAAAA,KAAUnT,SAAawZ,IAAAA,MAAAA,KAAWxZ,SAAW,EAAA;QAC/C,MAAM0pB,SAAAA,GAAYtQ,UAAUgO,cAAehO,CAAAA,MAAAA,CAAAA,CAAAA;AAC3C,QAAA,IAAI,CAACsQ,SAAW,EAAA;AACdvW,YAAAA,KAAAA,GAAQiG,OAAOuQ,WAAW,CAAA;AAC1BnQ,YAAAA,MAAAA,GAASJ,OAAOwQ,YAAY,CAAA;SACvB,MAAA;AACL,YAAA,MAAM7O,IAAO2O,GAAAA,SAAAA,CAAUX,qBAAqB,EAAA,CAAA;AAC5C,YAAA,MAAMc,iBAAiBjC,gBAAiB8B,CAAAA,SAAAA,CAAAA,CAAAA;YACxC,MAAMI,eAAAA,GAAkB1B,kBAAmByB,CAAAA,cAAAA,EAAgB,QAAU,EAAA,OAAA,CAAA,CAAA;YACrE,MAAME,gBAAAA,GAAmB3B,mBAAmByB,cAAgB,EAAA,SAAA,CAAA,CAAA;AAC5D1W,YAAAA,KAAAA,GAAQ4H,KAAK5H,KAAK,GAAG4W,iBAAiB5W,KAAK,GAAG2W,gBAAgB3W,KAAK,CAAA;AACnEqG,YAAAA,MAAAA,GAASuB,KAAKvB,MAAM,GAAGuQ,iBAAiBvQ,MAAM,GAAGsQ,gBAAgBtQ,MAAM,CAAA;AACvEqE,YAAAA,QAAAA,GAAW2J,aAAcqC,CAAAA,cAAAA,CAAehM,QAAQ,EAAE6L,SAAW,EAAA,aAAA,CAAA,CAAA;AAC7DD,YAAAA,SAAAA,GAAYjC,aAAcqC,CAAAA,cAAAA,CAAeJ,SAAS,EAAEC,SAAW,EAAA,cAAA,CAAA,CAAA;SAChE;KACF;IACD,OAAO;AACLvW,QAAAA,KAAAA;AACAqG,QAAAA,MAAAA;AACAqE,QAAAA,QAAAA,EAAUA,QAAYzb,IAAAA,QAAAA;AACtBqnB,QAAAA,SAAAA,EAAWA,SAAarnB,IAAAA,QAAAA;AAC1B,KAAA,CAAA;AACF,CAAA;AAEA,MAAM4nB,SAAS,CAAC5pB,CAAAA,GAAc6B,KAAKiB,KAAK,CAAC9C,IAAI,EAAM,CAAA,GAAA,EAAA,CAAA;AAEnD;AACO,SAAS6pB,eACd7Q,MAAyB,EACzB8Q,OAAgB,EAChBC,QAAiB,EACjBC,WAAoB,EACe;AACnC,IAAA,MAAMzU,QAAQiS,gBAAiBxO,CAAAA,MAAAA,CAAAA,CAAAA;IAC/B,MAAMiR,OAAAA,GAAUjC,mBAAmBzS,KAAO,EAAA,QAAA,CAAA,CAAA;AAC1C,IAAA,MAAMkI,WAAW2J,aAAc7R,CAAAA,KAAAA,CAAMkI,QAAQ,EAAEzE,QAAQ,aAAkBhX,CAAAA,IAAAA,QAAAA,CAAAA;AACzE,IAAA,MAAMqnB,YAAYjC,aAAc7R,CAAAA,KAAAA,CAAM8T,SAAS,EAAErQ,QAAQ,cAAmBhX,CAAAA,IAAAA,QAAAA,CAAAA;IAC5E,MAAMkoB,aAAAA,GAAgBd,gBAAiBpQ,CAAAA,MAAAA,EAAQ8Q,OAASC,EAAAA,QAAAA,CAAAA,CAAAA;AACxD,IAAA,IAAI,EAAChX,KAAAA,GAAOqG,MAAAA,GAAO,GAAG8Q,aAAAA,CAAAA;IAEtB,IAAI3U,KAAAA,CAAM0T,SAAS,KAAK,aAAe,EAAA;QACrC,MAAME,OAAAA,GAAUnB,kBAAmBzS,CAAAA,KAAAA,EAAO,QAAU,EAAA,OAAA,CAAA,CAAA;QACpD,MAAM2T,QAAAA,GAAWlB,mBAAmBzS,KAAO,EAAA,SAAA,CAAA,CAAA;AAC3CxC,QAAAA,KAAAA,IAASmW,QAASnW,CAAAA,KAAK,GAAGoW,OAAAA,CAAQpW,KAAK,CAAA;AACvCqG,QAAAA,MAAAA,IAAU8P,QAAS9P,CAAAA,MAAM,GAAG+P,OAAAA,CAAQ/P,MAAM,CAAA;KAC3C;AACDrG,IAAAA,KAAAA,GAAQlR,KAAKqC,GAAG,CAAC,CAAG6O,EAAAA,KAAAA,GAAQkX,QAAQlX,KAAK,CAAA,CAAA;IACzCqG,MAASvX,GAAAA,IAAAA,CAAKqC,GAAG,CAAC,CAAA,EAAG8lB,cAAcjX,KAAQiX,GAAAA,WAAAA,GAAc5Q,MAAS6Q,GAAAA,OAAAA,CAAQ7Q,MAAM,CAAA,CAAA;AAChFrG,IAAAA,KAAAA,GAAQ6W,OAAO/nB,IAAKoC,CAAAA,GAAG,CAAC8O,KAAO0K,EAAAA,QAAAA,EAAUyM,cAAczM,QAAQ,CAAA,CAAA,CAAA;AAC/DrE,IAAAA,MAAAA,GAASwQ,OAAO/nB,IAAKoC,CAAAA,GAAG,CAACmV,MAAQiQ,EAAAA,SAAAA,EAAWa,cAAcb,SAAS,CAAA,CAAA,CAAA;IACnE,IAAItW,KAAAA,IAAS,CAACqG,MAAQ,EAAA;;;AAGpBA,QAAAA,MAAAA,GAASwQ,OAAO7W,KAAQ,GAAA,CAAA,CAAA,CAAA;KACzB;IAED,MAAMoX,cAAAA,GAAiBL,OAAYlqB,KAAAA,SAAAA,IAAamqB,QAAanqB,KAAAA,SAAAA,CAAAA;IAE7D,IAAIuqB,cAAAA,IAAkBH,eAAeE,aAAc9Q,CAAAA,MAAM,IAAIA,MAAS8Q,GAAAA,aAAAA,CAAc9Q,MAAM,EAAE;AAC1FA,QAAAA,MAAAA,GAAS8Q,cAAc9Q,MAAM,CAAA;AAC7BrG,QAAAA,KAAAA,GAAQ6W,MAAO/nB,CAAAA,IAAAA,CAAKoB,KAAK,CAACmW,MAAS4Q,GAAAA,WAAAA,CAAAA,CAAAA,CAAAA;KACpC;IAED,OAAO;AAACjX,QAAAA,KAAAA;AAAOqG,QAAAA,MAAAA;AAAM,KAAA,CAAA;AACvB,CAAC;AAED;;;;;IAMO,SAASgR,WACdpZ,CAAAA,KAAY,EACZqZ,UAAkB,EAClBC,UAAoB,EACJ;AAChB,IAAA,MAAMC,aAAaF,UAAc,IAAA,CAAA,CAAA;AACjC,IAAA,MAAMG,eAAe3oB,IAAKoB,CAAAA,KAAK,CAAC+N,KAAAA,CAAMoI,MAAM,GAAGmR,UAAAA,CAAAA,CAAAA;AAC/C,IAAA,MAAME,cAAc5oB,IAAKoB,CAAAA,KAAK,CAAC+N,KAAAA,CAAM+B,KAAK,GAAGwX,UAAAA,CAAAA,CAAAA;AAE7CvZ,IAAAA,KAAAA,CAAMoI,MAAM,GAAGvX,IAAAA,CAAKoB,KAAK,CAAC+N,MAAMoI,MAAM,CAAA,CAAA;AACtCpI,IAAAA,KAAAA,CAAM+B,KAAK,GAAGlR,IAAAA,CAAKoB,KAAK,CAAC+N,MAAM+B,KAAK,CAAA,CAAA;IAEpC,MAAMiG,MAAAA,GAAShI,MAAMgI,MAAM,CAAA;;;;AAK3B,IAAA,IAAIA,OAAOzD,KAAK,KAAK+U,UAAAA,IAAe,CAACtR,MAAOzD,CAAAA,KAAK,CAAC6D,MAAM,IAAI,CAACJ,MAAAA,CAAOzD,KAAK,CAACxC,KAAK,CAAI,EAAA;QACjFiG,MAAOzD,CAAAA,KAAK,CAAC6D,MAAM,GAAG,CAAC,EAAEpI,KAAMoI,CAAAA,MAAM,CAAC,EAAE,CAAC,CAAA;QACzCJ,MAAOzD,CAAAA,KAAK,CAACxC,KAAK,GAAG,CAAC,EAAE/B,KAAM+B,CAAAA,KAAK,CAAC,EAAE,CAAC,CAAA;KACxC;IAED,IAAI/B,KAAAA,CAAM6H,uBAAuB,KAAK0R,UAC/BvR,IAAAA,MAAAA,CAAOI,MAAM,KAAKoR,YAClBxR,IAAAA,MAAAA,CAAOjG,KAAK,KAAK0X,WAAa,EAAA;AACnCzZ,QAAAA,KAAAA,CAAM6H,uBAAuB,GAAG0R,UAAAA,CAAAA;AAChCvR,QAAAA,MAAAA,CAAOI,MAAM,GAAGoR,YAAAA,CAAAA;AAChBxR,QAAAA,MAAAA,CAAOjG,KAAK,GAAG0X,WAAAA,CAAAA;QACfzZ,KAAM4E,CAAAA,GAAG,CAAC8U,YAAY,CAACH,YAAY,CAAG,EAAA,CAAA,EAAGA,YAAY,CAAG,EAAA,CAAA,CAAA,CAAA;AACxD,QAAA,OAAO,IAAI,CAAA;KACZ;AACD,IAAA,OAAO,KAAK,CAAA;AACd,CAAC;AAED;;;;IAKaI,MAAAA,4BAAAA,GAAgC,WAAW;AACtD,IAAA,IAAIC,mBAAmB,KAAK,CAAA;IAC5B,IAAI;AACF,QAAA,MAAM7rB,OAAU,GAAA;AACd,YAAA,IAAI8rB,OAAU,CAAA,GAAA;AACZD,gBAAAA,gBAAAA,GAAmB,IAAI,CAAA;AACvB,gBAAA,OAAO,KAAK,CAAA;AACd,aAAA;AACF,SAAA,CAAA;AAEA,QAAA,IAAI9D,eAAmB,EAAA,EAAA;AACrBpe,YAAAA,MAAAA,CAAOoiB,gBAAgB,CAAC,MAAQ,EAAA,IAAI,EAAE/rB,OAAAA,CAAAA,CAAAA;AACtC2J,YAAAA,MAAAA,CAAOqiB,mBAAmB,CAAC,MAAQ,EAAA,IAAI,EAAEhsB,OAAAA,CAAAA,CAAAA;SAC1C;AACH,KAAA,CAAE,OAAO4C,CAAG,EAAA;;AAEZ,KAAA;IACA,OAAOipB,gBAAAA,CAAAA;AACT,CAAK,GAAA;AAEL;;;;;;;;AAQC,IAEM,SAASI,YAAAA,CACdvD,OAAoB,EACpBzjB,QAA4B,EACR;IACpB,MAAMrI,KAAAA,GAAQisB,SAASH,OAASzjB,EAAAA,QAAAA,CAAAA,CAAAA;AAChC,IAAA,MAAMoa,OAAUziB,GAAAA,KAAAA,IAASA,KAAM0iB,CAAAA,KAAK,CAAC,mBAAA,CAAA,CAAA;AACrC,IAAA,OAAOD,UAAU,CAACA,OAAO,CAAC,CAAA,CAAE,GAAGxe,SAAS,CAAA;AAC1C;;ACjSA;;IAGO,SAASqrB,YAAAA,CAAaC,EAAS,EAAEC,EAAS,EAAE7f,CAAS,EAAE2K,IAAK,EAAE;IACnE,OAAO;QACLhW,CAAGirB,EAAAA,EAAAA,CAAGjrB,CAAC,GAAGqL,CAAK6f,IAAAA,GAAGlrB,CAAC,GAAGirB,EAAGjrB,CAAAA,CAAC,CAADA;QACzBE,CAAG+qB,EAAAA,EAAAA,CAAG/qB,CAAC,GAAGmL,CAAK6f,IAAAA,GAAGhrB,CAAC,GAAG+qB,EAAG/qB,CAAAA,CAAC,CAADA;AAC3B,KAAA,CAAA;AACF,CAAC;AAED;;IAGO,SAASirB,qBAAAA,CACdF,EAAS,EACTC,EAAS,EACT7f,CAAS,EAAE2K,IAAkC,EAC7C;IACA,OAAO;QACLhW,CAAGirB,EAAAA,EAAAA,CAAGjrB,CAAC,GAAGqL,CAAK6f,IAAAA,GAAGlrB,CAAC,GAAGirB,EAAGjrB,CAAAA,CAAC,CAADA;QACzBE,CAAG8V,EAAAA,IAAAA,KAAS,QAAW3K,GAAAA,CAAAA,GAAI,GAAM4f,GAAAA,EAAAA,CAAG/qB,CAAC,GAAGgrB,EAAGhrB,CAAAA,CAAC,GACxC8V,IAAAA,KAAS,OAAU3K,GAAAA,CAAAA,GAAI,IAAI4f,EAAG/qB,CAAAA,CAAC,GAAGgrB,EAAAA,CAAGhrB,CAAC,GACpCmL,CAAI,GAAA,CAAA,GAAI6f,EAAGhrB,CAAAA,CAAC,GAAG+qB,EAAAA,CAAG/qB,CAAC;AAC3B,KAAA,CAAA;AACF,CAAC;AAED;;IAGO,SAASkrB,oBAAAA,CAAqBH,EAAe,EAAEC,EAAe,EAAE7f,CAAS,EAAE2K,IAAK,EAAE;AACvF,IAAA,MAAMqV,GAAM,GAAA;AAACrrB,QAAAA,CAAAA,EAAGirB,GAAGvP,IAAI;AAAExb,QAAAA,CAAAA,EAAG+qB,GAAGrP,IAAI;AAAA,KAAA,CAAA;AACnC,IAAA,MAAM0P,GAAM,GAAA;AAACtrB,QAAAA,CAAAA,EAAGkrB,GAAGzP,IAAI;AAAEvb,QAAAA,CAAAA,EAAGgrB,GAAGvP,IAAI;AAAA,KAAA,CAAA;IACnC,MAAMva,CAAAA,GAAI4pB,YAAaC,CAAAA,EAAAA,EAAII,GAAKhgB,EAAAA,CAAAA,CAAAA,CAAAA;IAChC,MAAMhK,CAAAA,GAAI2pB,YAAaK,CAAAA,GAAAA,EAAKC,GAAKjgB,EAAAA,CAAAA,CAAAA,CAAAA;IACjC,MAAMkgB,CAAAA,GAAIP,YAAaM,CAAAA,GAAAA,EAAKJ,EAAI7f,EAAAA,CAAAA,CAAAA,CAAAA;IAChC,MAAMqC,CAAAA,GAAIsd,YAAa5pB,CAAAA,CAAAA,EAAGC,CAAGgK,EAAAA,CAAAA,CAAAA,CAAAA;IAC7B,MAAM3J,CAAAA,GAAIspB,YAAa3pB,CAAAA,CAAAA,EAAGkqB,CAAGlgB,EAAAA,CAAAA,CAAAA,CAAAA;IAC7B,OAAO2f,YAAAA,CAAatd,GAAGhM,CAAG2J,EAAAA,CAAAA,CAAAA,CAAAA;AAC5B;;AChCA,MAAMmgB,qBAAwB,GAAA,SAASC,KAAa,EAAE3Y,KAAa,EAAc;IAC/E,OAAO;AACL9S,QAAAA,CAAAA,CAAAA,CAAEA,CAAC,EAAE;YACH,OAAOyrB,KAAAA,GAAQA,QAAQ3Y,KAAQ9S,GAAAA,CAAAA,CAAAA;AACjC,SAAA;AACA0rB,QAAAA,QAAAA,CAAAA,CAASpS,CAAC,EAAE;YACVxG,KAAQwG,GAAAA,CAAAA,CAAAA;AACV,SAAA;AACA2C,QAAAA,SAAAA,CAAAA,CAAU7S,KAAK,EAAE;AACf,YAAA,IAAIA,UAAU,QAAU,EAAA;gBACtB,OAAOA,KAAAA,CAAAA;aACR;YACD,OAAOA,KAAAA,KAAU,OAAU,GAAA,MAAA,GAAS,OAAO,CAAA;AAC7C,SAAA;QACAuiB,KAAM3rB,CAAAA,CAAAA,CAAC,EAAEtE,KAAK,EAAE;AACd,YAAA,OAAOsE,CAAItE,GAAAA,KAAAA,CAAAA;AACb,SAAA;QACAkwB,UAAW5rB,CAAAA,CAAAA,CAAC,EAAE6rB,SAAS,EAAE;AACvB,YAAA,OAAO7rB,CAAI6rB,GAAAA,SAAAA,CAAAA;AACb,SAAA;AACF,KAAA,CAAA;AACF,CAAA,CAAA;AAEA,MAAMC,wBAAwB,WAAuB;IACnD,OAAO;AACL9rB,QAAAA,CAAAA,CAAAA,CAAEA,CAAC,EAAE;YACH,OAAOA,CAAAA,CAAAA;AACT,SAAA;QACA0rB,QAASpS,CAAAA,CAAAA,CAAC,EAAE,EACZ;AACA2C,QAAAA,SAAAA,CAAAA,CAAU7S,KAAK,EAAE;YACf,OAAOA,KAAAA,CAAAA;AACT,SAAA;QACAuiB,KAAM3rB,CAAAA,CAAAA,CAAC,EAAEtE,KAAK,EAAE;AACd,YAAA,OAAOsE,CAAItE,GAAAA,KAAAA,CAAAA;AACb,SAAA;QACAkwB,UAAW5rB,CAAAA,CAAAA,CAAC,EAAE+rB,UAAU,EAAE;YACxB,OAAO/rB,CAAAA,CAAAA;AACT,SAAA;AACF,KAAA,CAAA;AACF,CAAA,CAAA;AAEO,SAASgsB,aAAcviB,CAAAA,GAAY,EAAEgiB,KAAa,EAAE3Y,KAAa,EAAE;AACxE,IAAA,OAAOrJ,GAAM+hB,GAAAA,qBAAAA,CAAsBC,KAAO3Y,EAAAA,KAAAA,CAAAA,GAASgZ,qBAAuB,EAAA,CAAA;AAC5E,CAAC;AAEM,SAASG,qBAAAA,CAAsBtW,GAA6B,EAAEuW,SAAwB,EAAE;AAC7F,IAAA,IAAI5W,KAA4B6W,EAAAA,QAAAA,CAAAA;IAChC,IAAID,SAAAA,KAAc,KAASA,IAAAA,SAAAA,KAAc,KAAO,EAAA;QAC9C5W,KAAQK,GAAAA,GAAAA,CAAIoD,MAAM,CAACzD,KAAK,CAAA;QACxB6W,QAAW,GAAA;AACT7W,YAAAA,KAAAA,CAAMuS,gBAAgB,CAAC,WAAA,CAAA;AACvBvS,YAAAA,KAAAA,CAAM8W,mBAAmB,CAAC,WAAA,CAAA;AAC3B,SAAA,CAAA;QAED9W,KAAM+W,CAAAA,WAAW,CAAC,WAAA,EAAaH,SAAW,EAAA,WAAA,CAAA,CAAA;AACzCvW,QAAAA,GAAAA,CAAiD2W,iBAAiB,GAAGH,QAAAA,CAAAA;KACvE;AACH,CAAC;AAEM,SAASI,oBAAAA,CAAqB5W,GAA6B,EAAEwW,QAA2B,EAAE;AAC/F,IAAA,IAAIA,aAAaxsB,SAAW,EAAA;QAC1B,OAAQgW,IAAiD2W,iBAAiB,CAAA;AAC1E3W,QAAAA,GAAAA,CAAIoD,MAAM,CAACzD,KAAK,CAAC+W,WAAW,CAAC,WAAaF,EAAAA,QAAQ,CAAC,CAAA,CAAE,EAAEA,QAAQ,CAAC,CAAE,CAAA,CAAA,CAAA;KACnE;AACH;;AC/DA,SAASK,UAAWzoB,CAAAA,QAAQ,EAAE;AAC5B,IAAA,IAAIA,aAAa,OAAS,EAAA;QACxB,OAAO;YACL0oB,OAASlnB,EAAAA,aAAAA;YACTmnB,OAASrnB,EAAAA,UAAAA;YACTsnB,SAAWrnB,EAAAA,eAAAA;AACb,SAAA,CAAA;KACD;IACD,OAAO;QACLmnB,OAASvmB,EAAAA,UAAAA;QACTwmB,OAAS,EAAA,CAACtrB,CAAGC,EAAAA,CAAAA,GAAMD,CAAIC,GAAAA,CAAAA;AACvBsrB,QAAAA,SAAAA,EAAW3sB,CAAAA,CAAKA,GAAAA,CAAAA;AAClB,KAAA,CAAA;AACF,CAAA;AAEA,SAAS4sB,gBAAiB,CAAA,EAACpnB,KAAK,GAAEC,GAAG,GAAEuE,KAAK,GAAEyE,IAAI,GAAE6G,KAAK,GAAC,EAAE;IAC1D,OAAO;AACL9P,QAAAA,KAAAA,EAAOA,KAAQwE,GAAAA,KAAAA;AACfvE,QAAAA,GAAAA,EAAKA,GAAMuE,GAAAA,KAAAA;AACXyE,QAAAA,IAAAA,EAAMA,QAAQ,CAAChJ,MAAMD,KAAQ,GAAA,CAAA,IAAKwE,KAAU,KAAA,CAAA;AAC5CsL,QAAAA,KAAAA;AACF,KAAA,CAAA;AACF,CAAA;AAEA,SAASuX,WAAWC,OAAO,EAAEjjB,MAAM,EAAEmI,MAAM,EAAE;IAC3C,MAAM,EAACjO,WAAUyB,KAAAA,EAAOunB,aAAYtnB,GAAAA,EAAKunB,QAAQ,GAAC,GAAGhb,MAAAA,CAAAA;AACrD,IAAA,MAAM,EAACya,OAAO,GAAEE,SAAS,GAAC,GAAGH,UAAWzoB,CAAAA,QAAAA,CAAAA,CAAAA;IACxC,MAAMiG,KAAAA,GAAQH,OAAOnM,MAAM,CAAA;AAE3B,IAAA,IAAI,EAAC8H,KAAK,GAAEC,MAAKgJ,IAAAA,GAAK,GAAGqe,OAAAA,CAAAA;AACzB,IAAA,IAAIvvB,CAAGO,EAAAA,IAAAA,CAAAA;AAEP,IAAA,IAAI2Q,IAAM,EAAA;QACRjJ,KAASwE,IAAAA,KAAAA,CAAAA;QACTvE,GAAOuE,IAAAA,KAAAA,CAAAA;QACP,IAAKzM,CAAAA,GAAI,GAAGO,IAAOkM,GAAAA,KAAK,EAAEzM,CAAIO,GAAAA,IAAAA,EAAM,EAAEP,CAAG,CAAA;YACvC,IAAI,CAACkvB,OAAQE,CAAAA,SAAAA,CAAU9iB,MAAM,CAACrE,KAAQwE,GAAAA,KAAAA,CAAM,CAACjG,QAAAA,CAAS,CAAGgpB,EAAAA,UAAAA,EAAYC,QAAW,CAAA,EAAA;gBAC9E,MAAM;aACP;AACDxnB,YAAAA,KAAAA,EAAAA,CAAAA;AACAC,YAAAA,GAAAA,EAAAA,CAAAA;AACF,SAAA;QACAD,KAASwE,IAAAA,KAAAA,CAAAA;QACTvE,GAAOuE,IAAAA,KAAAA,CAAAA;KACR;AAED,IAAA,IAAIvE,MAAMD,KAAO,EAAA;QACfC,GAAOuE,IAAAA,KAAAA,CAAAA;KACR;IACD,OAAO;AAACxE,QAAAA,KAAAA;AAAOC,QAAAA,GAAAA;AAAKgJ,QAAAA,IAAAA;AAAM6G,QAAAA,KAAAA,EAAOwX,QAAQxX,KAAK;AAAA,KAAA,CAAA;AAChD,CAAA;AAgBA,CAAO,SAAS2X,aAAcH,CAAAA,OAAO,EAAEjjB,MAAM,EAAEmI,MAAM,EAAE;AACrD,IAAA,IAAI,CAACA,MAAQ,EAAA;QACX,OAAO;AAAC8a,YAAAA,OAAAA;AAAQ,SAAA,CAAA;KACjB;IAED,MAAM,EAAC/oB,WAAUyB,KAAAA,EAAOunB,aAAYtnB,GAAAA,EAAKunB,QAAQ,GAAC,GAAGhb,MAAAA,CAAAA;IACrD,MAAMhI,KAAAA,GAAQH,OAAOnM,MAAM,CAAA;IAC3B,MAAM,EAACgvB,UAASD,OAAAA,GAASE,SAAS,GAAC,GAAGH,UAAWzoB,CAAAA,QAAAA,CAAAA,CAAAA;AACjD,IAAA,MAAM,EAACyB,KAAAA,GAAOC,GAAAA,GAAKgJ,IAAAA,GAAM6G,KAAAA,GAAM,GAAGuX,UAAWC,CAAAA,OAAAA,EAASjjB,MAAQmI,EAAAA,MAAAA,CAAAA,CAAAA;AAE9D,IAAA,MAAM5O,SAAS,EAAE,CAAA;AACjB,IAAA,IAAI8pB,SAAS,KAAK,CAAA;AAClB,IAAA,IAAIC,WAAW,IAAI,CAAA;AACnB,IAAA,IAAIzxB,OAAOqf,KAAOqS,EAAAA,SAAAA,CAAAA;IAElB,MAAMC,aAAAA,GAAgB,IAAMZ,OAAQM,CAAAA,UAAAA,EAAYK,WAAW1xB,KAAUgxB,CAAAA,IAAAA,OAAAA,CAAQK,YAAYK,SAAe,CAAA,KAAA,CAAA,CAAA;IACxG,MAAME,WAAAA,GAAc,IAAMZ,OAAQM,CAAAA,QAAAA,EAAUtxB,WAAW,CAAK+wB,IAAAA,OAAAA,CAAQO,UAAUI,SAAW1xB,EAAAA,KAAAA,CAAAA,CAAAA;IACzF,MAAM6xB,WAAAA,GAAc,IAAML,MAAUG,IAAAA,aAAAA,EAAAA,CAAAA;IACpC,MAAMG,UAAAA,GAAa,IAAM,CAACN,MAAUI,IAAAA,WAAAA,EAAAA,CAAAA;IAEpC,IAAK,IAAI/vB,IAAIiI,KAAOmhB,EAAAA,IAAAA,GAAOnhB,OAAOjI,CAAKkI,IAAAA,GAAAA,EAAK,EAAElI,CAAG,CAAA;QAC/Cwd,KAAQlR,GAAAA,MAAM,CAACtM,CAAAA,GAAIyM,KAAM,CAAA,CAAA;QAEzB,IAAI+Q,KAAAA,CAAMoJ,IAAI,EAAE;YACd,SAAS;SACV;QAEDzoB,KAAQixB,GAAAA,SAAAA,CAAU5R,KAAK,CAAChX,QAAS,CAAA,CAAA,CAAA;AAEjC,QAAA,IAAIrI,UAAU0xB,SAAW,EAAA;YACvB,SAAS;SACV;QAEDF,MAAST,GAAAA,OAAAA,CAAQ/wB,OAAOqxB,UAAYC,EAAAA,QAAAA,CAAAA,CAAAA;QAEpC,IAAIG,QAAAA,KAAa,IAAI,IAAII,WAAe,EAAA,EAAA;AACtCJ,YAAAA,QAAAA,GAAWT,OAAQhxB,CAAAA,KAAAA,EAAOqxB,UAAgB,CAAA,KAAA,CAAA,GAAIxvB,IAAIopB,IAAI,CAAA;SACvD;QAED,IAAIwG,QAAAA,KAAa,IAAI,IAAIK,UAAc,EAAA,EAAA;YACrCpqB,MAAO5C,CAAAA,IAAI,CAACosB,gBAAiB,CAAA;gBAACpnB,KAAO2nB,EAAAA,QAAAA;gBAAU1nB,GAAKlI,EAAAA,CAAAA;AAAGkR,gBAAAA,IAAAA;AAAMzE,gBAAAA,KAAAA;AAAOsL,gBAAAA,KAAAA;AAAK,aAAA,CAAA,CAAA,CAAA;AACzE6X,YAAAA,QAAAA,GAAW,IAAI,CAAA;SAChB;QACDxG,IAAOppB,GAAAA,CAAAA,CAAAA;QACP6vB,SAAY1xB,GAAAA,KAAAA,CAAAA;AACd,KAAA;IAEA,IAAIyxB,QAAAA,KAAa,IAAI,EAAE;QACrB/pB,MAAO5C,CAAAA,IAAI,CAACosB,gBAAiB,CAAA;YAACpnB,KAAO2nB,EAAAA,QAAAA;AAAU1nB,YAAAA,GAAAA;AAAKgJ,YAAAA,IAAAA;AAAMzE,YAAAA,KAAAA;AAAOsL,YAAAA,KAAAA;AAAK,SAAA,CAAA,CAAA,CAAA;KACvE;IAED,OAAOlS,MAAAA,CAAAA;AACT,CAAC;AAWA,CACM,SAASqqB,cAAAA,CAAerR,IAAI,EAAEpK,MAAM,EAAE;AAC3C,IAAA,MAAM5O,SAAS,EAAE,CAAA;IACjB,MAAMsqB,QAAAA,GAAWtR,KAAKsR,QAAQ,CAAA;AAE9B,IAAA,IAAK,IAAInwB,CAAI,GAAA,CAAA,EAAGA,IAAImwB,QAAShwB,CAAAA,MAAM,EAAEH,CAAK,EAAA,CAAA;QACxC,MAAMowB,GAAAA,GAAMV,cAAcS,QAAQ,CAACnwB,EAAE,EAAE6e,IAAAA,CAAKvS,MAAM,EAAEmI,MAAAA,CAAAA,CAAAA;QACpD,IAAI2b,GAAAA,CAAIjwB,MAAM,EAAE;AACd0F,YAAAA,MAAAA,CAAO5C,IAAI,CAAImtB,GAAAA,GAAAA,CAAAA,CAAAA;SAChB;AACH,KAAA;IACA,OAAOvqB,MAAAA,CAAAA;AACT,CAAC;AAKD,CAAA,SAASwqB,gBAAgB/jB,MAAM,EAAEG,KAAK,EAAEyE,IAAI,EAAEgY,QAAQ,EAAE;AACtD,IAAA,IAAIjhB,KAAQ,GAAA,CAAA,CAAA;AACZ,IAAA,IAAIC,MAAMuE,KAAQ,GAAA,CAAA,CAAA;IAElB,IAAIyE,IAAAA,IAAQ,CAACgY,QAAU,EAAA;QAErB,MAAOjhB,KAAAA,GAAQwE,SAAS,CAACH,MAAM,CAACrE,KAAM,CAAA,CAAC2e,IAAI,CAAE;AAC3C3e,YAAAA,KAAAA,EAAAA,CAAAA;AACF,SAAA;KACD;AAGD,IAAA,MAAOA,QAAQwE,KAASH,IAAAA,MAAM,CAACrE,KAAM,CAAA,CAAC2e,IAAI,CAAE;AAC1C3e,QAAAA,KAAAA,EAAAA,CAAAA;AACF,KAAA;IAGAA,KAASwE,IAAAA,KAAAA,CAAAA;AAET,IAAA,IAAIyE,IAAM,EAAA;QAERhJ,GAAOD,IAAAA,KAAAA,CAAAA;KACR;IAED,MAAOC,GAAAA,GAAMD,SAASqE,MAAM,CAACpE,MAAMuE,KAAM,CAAA,CAACma,IAAI,CAAE;AAC9C1e,QAAAA,GAAAA,EAAAA,CAAAA;AACF,KAAA;IAGAA,GAAOuE,IAAAA,KAAAA,CAAAA;IAEP,OAAO;AAACxE,QAAAA,KAAAA;AAAOC,QAAAA,GAAAA;AAAG,KAAA,CAAA;AACpB,CAAA;AASA,CAAA,SAASooB,cAAchkB,MAAM,EAAErE,KAAK,EAAEvB,GAAG,EAAEwK,IAAI,EAAE;IAC/C,MAAMzE,KAAAA,GAAQH,OAAOnM,MAAM,CAAA;AAC3B,IAAA,MAAM0F,SAAS,EAAE,CAAA;AACjB,IAAA,IAAIsD,IAAOlB,GAAAA,KAAAA,CAAAA;IACX,IAAImhB,IAAAA,GAAO9c,MAAM,CAACrE,KAAM,CAAA,CAAA;IACxB,IAAIC,GAAAA,CAAAA;AAEJ,IAAA,IAAKA,MAAMD,KAAQ,GAAA,CAAA,EAAGC,GAAOxB,IAAAA,GAAAA,EAAK,EAAEwB,GAAK,CAAA;AACvC,QAAA,MAAMqoB,GAAMjkB,GAAAA,MAAM,CAACpE,GAAAA,GAAMuE,KAAM,CAAA,CAAA;AAC/B,QAAA,IAAI8jB,GAAI3J,CAAAA,IAAI,IAAI2J,GAAAA,CAAIC,IAAI,EAAE;YACxB,IAAI,CAACpH,IAAKxC,CAAAA,IAAI,EAAE;AACd1V,gBAAAA,IAAAA,GAAO,KAAK,CAAA;AACZrL,gBAAAA,MAAAA,CAAO5C,IAAI,CAAC;AAACgF,oBAAAA,KAAAA,EAAOA,KAAQwE,GAAAA,KAAAA;AAAOvE,oBAAAA,GAAAA,EAAK,CAACA,GAAM,GAAA,CAAA,IAAKuE,KAAAA;AAAOyE,oBAAAA,IAAAA;AAAI,iBAAA,CAAA,CAAA;AAE/DjJ,gBAAAA,KAAAA,GAAQkB,IAAOonB,GAAAA,GAAAA,CAAIC,IAAI,GAAGtoB,MAAM,IAAI,CAAA;aACrC;SACI,MAAA;YACLiB,IAAOjB,GAAAA,GAAAA,CAAAA;YACP,IAAIkhB,IAAAA,CAAKxC,IAAI,EAAE;gBACb3e,KAAQC,GAAAA,GAAAA,CAAAA;aACT;SACF;QACDkhB,IAAOmH,GAAAA,GAAAA,CAAAA;AACT,KAAA;IAEA,IAAIpnB,IAAAA,KAAS,IAAI,EAAE;AACjBtD,QAAAA,MAAAA,CAAO5C,IAAI,CAAC;AAACgF,YAAAA,KAAAA,EAAOA,KAAQwE,GAAAA,KAAAA;AAAOvE,YAAAA,GAAAA,EAAKiB,IAAOsD,GAAAA,KAAAA;AAAOyE,YAAAA,IAAAA;AAAI,SAAA,CAAA,CAAA;KAC3D;IAED,OAAOrL,MAAAA,CAAAA;AACT,CAAA;AASC,CACM,SAAS4qB,gBAAAA,CAAiB5R,IAAI,EAAE6R,cAAc,EAAE;IACrD,MAAMpkB,MAAAA,GAASuS,KAAKvS,MAAM,CAAA;AAC1B,IAAA,MAAM4c,QAAWrK,GAAAA,IAAAA,CAAKtd,OAAO,CAAC2nB,QAAQ,CAAA;IACtC,MAAMzc,KAAAA,GAAQH,OAAOnM,MAAM,CAAA;AAE3B,IAAA,IAAI,CAACsM,KAAO,EAAA;AACV,QAAA,OAAO,EAAE,CAAA;KACV;AAED,IAAA,MAAMyE,IAAO,GAAA,CAAC,CAAC2N,IAAAA,CAAK8R,KAAK,CAAA;IACzB,MAAM,EAAC1oB,QAAOC,GAAAA,GAAI,GAAGmoB,eAAAA,CAAgB/jB,MAAQG,EAAAA,KAAAA,EAAOyE,IAAMgY,EAAAA,QAAAA,CAAAA,CAAAA;IAE1D,IAAIA,QAAAA,KAAa,IAAI,EAAE;AACrB,QAAA,OAAO0H,cAAc/R,IAAM,EAAA;AAAC,YAAA;AAAC5W,gBAAAA,KAAAA;AAAOC,gBAAAA,GAAAA;AAAKgJ,gBAAAA,IAAAA;AAAI,aAAA;AAAE,SAAA,EAAE5E,MAAQokB,EAAAA,cAAAA,CAAAA,CAAAA;KAC1D;AAED,IAAA,MAAMhqB,GAAMwB,GAAAA,GAAAA,GAAMD,KAAQC,GAAAA,GAAAA,GAAMuE,QAAQvE,GAAG,CAAA;IAC3C,MAAM2oB,YAAAA,GAAe,CAAC,CAAChS,IAAAA,CAAKiS,SAAS,IAAI7oB,KAAAA,KAAU,CAAKC,IAAAA,GAAAA,KAAQuE,KAAQ,GAAA,CAAA,CAAA;AACxE,IAAA,OAAOmkB,cAAc/R,IAAMyR,EAAAA,aAAAA,CAAchkB,QAAQrE,KAAOvB,EAAAA,GAAAA,EAAKmqB,eAAevkB,MAAQokB,EAAAA,cAAAA,CAAAA,CAAAA;AACtF,CAAC;AAQD,CAAA,SAASE,cAAc/R,IAAI,EAAEsR,QAAQ,EAAE7jB,MAAM,EAAEokB,cAAc,EAAE;AAC7D,IAAA,IAAI,CAACA,cAAkB,IAAA,CAACA,eAAenM,UAAU,IAAI,CAACjY,MAAQ,EAAA;QAC5D,OAAO6jB,QAAAA,CAAAA;KACR;IACD,OAAOY,eAAAA,CAAgBlS,IAAMsR,EAAAA,QAAAA,EAAU7jB,MAAQokB,EAAAA,cAAAA,CAAAA,CAAAA;AACjD,CAAA;AASA,CAAA,SAASK,gBAAgBlS,IAAI,EAAEsR,QAAQ,EAAE7jB,MAAM,EAAEokB,cAAc,EAAE;AAC/D,IAAA,MAAMM,YAAenS,GAAAA,IAAAA,CAAKoS,MAAM,CAACxV,UAAU,EAAA,CAAA;IAC3C,MAAMyV,SAAAA,GAAYC,SAAUtS,CAAAA,IAAAA,CAAKtd,OAAO,CAAA,CAAA;IACxC,MAAM,EAAC6vB,aAAe1wB,EAAAA,YAAAA,GAAca,OAAAA,EAAS,EAAC2nB,QAAQ,GAAC,GAAC,GAAGrK,IAAAA,CAAAA;IAC3D,MAAMpS,KAAAA,GAAQH,OAAOnM,MAAM,CAAA;AAC3B,IAAA,MAAM0F,SAAS,EAAE,CAAA;AACjB,IAAA,IAAIwrB,SAAYH,GAAAA,SAAAA,CAAAA;AAChB,IAAA,IAAIjpB,KAAQkoB,GAAAA,QAAQ,CAAC,CAAA,CAAE,CAACloB,KAAK,CAAA;AAC7B,IAAA,IAAIjI,CAAIiI,GAAAA,KAAAA,CAAAA;IAER,SAASqpB,QAAAA,CAASlpB,CAAC,EAAEjE,CAAC,EAAEotB,CAAC,EAAEC,EAAE,EAAE;AAC7B,QAAA,MAAMC,GAAMvI,GAAAA,QAAAA,GAAW,CAAC,CAAA,GAAI,CAAC,CAAA;AAC7B,QAAA,IAAI9gB,MAAMjE,CAAG,EAAA;AACX,YAAA,OAAA;SACD;QAEDiE,CAAKqE,IAAAA,KAAAA,CAAAA;AACL,QAAA,MAAOH,MAAM,CAAClE,CAAAA,GAAIqE,KAAM,CAAA,CAACma,IAAI,CAAE;YAC7Bxe,CAAKqpB,IAAAA,GAAAA,CAAAA;AACP,SAAA;AACA,QAAA,MAAOnlB,MAAM,CAACnI,CAAAA,GAAIsI,KAAM,CAAA,CAACma,IAAI,CAAE;YAC7BziB,CAAKstB,IAAAA,GAAAA,CAAAA;AACP,SAAA;QACA,IAAIrpB,CAAAA,GAAIqE,KAAUtI,KAAAA,CAAAA,GAAIsI,KAAO,EAAA;AAC3B5G,YAAAA,MAAAA,CAAO5C,IAAI,CAAC;AAACgF,gBAAAA,KAAAA,EAAOG,CAAIqE,GAAAA,KAAAA;AAAOvE,gBAAAA,GAAAA,EAAK/D,CAAIsI,GAAAA,KAAAA;gBAAOyE,IAAMqgB,EAAAA,CAAAA;gBAAGxZ,KAAOyZ,EAAAA,EAAAA;AAAE,aAAA,CAAA,CAAA;YACjEH,SAAYG,GAAAA,EAAAA,CAAAA;AACZvpB,YAAAA,KAAAA,GAAQ9D,CAAIsI,GAAAA,KAAAA,CAAAA;SACb;AACH,KAAA;IAEA,KAAK,MAAM8iB,WAAWY,QAAU,CAAA;QAC9BloB,KAAQihB,GAAAA,QAAAA,GAAWjhB,KAAQsnB,GAAAA,OAAAA,CAAQtnB,KAAK,CAAA;AACxC,QAAA,IAAImhB,IAAO9c,GAAAA,MAAM,CAACrE,KAAAA,GAAQwE,KAAM,CAAA,CAAA;QAChC,IAAIsL,KAAAA,CAAAA;AACJ,QAAA,IAAK/X,IAAIiI,KAAQ,GAAA,CAAA,EAAGjI,KAAKuvB,OAAQrnB,CAAAA,GAAG,EAAElI,CAAK,EAAA,CAAA;AACzC,YAAA,MAAM2oB,EAAKrc,GAAAA,MAAM,CAACtM,CAAAA,GAAIyM,KAAM,CAAA,CAAA;AAC5BsL,YAAAA,KAAAA,GAAQoZ,SAAUT,CAAAA,cAAAA,CAAenM,UAAU,CAACnC,cAAc4O,YAAc,EAAA;gBACtE1yB,IAAM,EAAA,SAAA;gBACNozB,EAAItI,EAAAA,IAAAA;gBACJsE,EAAI/E,EAAAA,EAAAA;AACJgJ,gBAAAA,WAAAA,EAAa,CAAC3xB,CAAI,GAAA,CAAA,IAAKyM,KAAAA;AACvBmlB,gBAAAA,WAAAA,EAAa5xB,CAAIyM,GAAAA,KAAAA;AACjB/L,gBAAAA,YAAAA;AACF,aAAA,CAAA,CAAA,CAAA,CAAA;YACA,IAAImxB,YAAAA,CAAa9Z,OAAOsZ,SAAY,CAAA,EAAA;AAClCC,gBAAAA,QAAAA,CAASrpB,KAAOjI,EAAAA,CAAAA,GAAI,CAAGuvB,EAAAA,OAAAA,CAAQre,IAAI,EAAEmgB,SAAAA,CAAAA,CAAAA;aACtC;YACDjI,IAAOT,GAAAA,EAAAA,CAAAA;YACP0I,SAAYtZ,GAAAA,KAAAA,CAAAA;AACd,SAAA;QACA,IAAI9P,KAAAA,GAAQjI,IAAI,CAAG,EAAA;AACjBsxB,YAAAA,QAAAA,CAASrpB,KAAOjI,EAAAA,CAAAA,GAAI,CAAGuvB,EAAAA,OAAAA,CAAQre,IAAI,EAAEmgB,SAAAA,CAAAA,CAAAA;SACtC;AACH,KAAA;IAEA,OAAOxrB,MAAAA,CAAAA;AACT,CAAA;AAEA,SAASsrB,SAAAA,CAAU5vB,OAAO,EAAE;IAC1B,OAAO;AACL6V,QAAAA,eAAAA,EAAiB7V,QAAQ6V,eAAe;AACxC0a,QAAAA,cAAAA,EAAgBvwB,QAAQuwB,cAAc;AACtCC,QAAAA,UAAAA,EAAYxwB,QAAQwwB,UAAU;AAC9BC,QAAAA,gBAAAA,EAAkBzwB,QAAQywB,gBAAgB;AAC1CC,QAAAA,eAAAA,EAAiB1wB,QAAQ0wB,eAAe;AACxC5U,QAAAA,WAAAA,EAAa9b,QAAQ8b,WAAW;AAChChG,QAAAA,WAAAA,EAAa9V,QAAQ8V,WAAW;AAClC,KAAA,CAAA;AACF,CAAA;AAEA,SAASwa,YAAa9Z,CAAAA,KAAK,EAAEsZ,SAAS,EAAE;AACtC,IAAA,IAAI,CAACA,SAAW,EAAA;AACd,QAAA,OAAO,KAAK,CAAA;KACb;AACD,IAAA,MAAM3W,QAAQ,EAAE,CAAA;AAChB,IAAA,MAAMwX,QAAW,GAAA,SAAS9wB,GAAG,EAAEjD,KAAK,EAAE;QACpC,IAAI,CAACkS,oBAAoBlS,KAAQ,CAAA,EAAA;YAC/B,OAAOA,KAAAA,CAAAA;SACR;AACD,QAAA,IAAI,CAACuc,KAAAA,CAAMtG,QAAQ,CAACjW,KAAQ,CAAA,EAAA;AAC1Buc,YAAAA,KAAAA,CAAMzX,IAAI,CAAC9E,KAAAA,CAAAA,CAAAA;SACZ;QACD,OAAOuc,KAAAA,CAAMrZ,OAAO,CAAClD,KAAAA,CAAAA,CAAAA;AACvB,KAAA,CAAA;IACA,OAAOwU,IAAAA,CAAKC,SAAS,CAACmF,KAAAA,EAAOma,cAAcvf,IAAKC,CAAAA,SAAS,CAACye,SAAWa,EAAAA,QAAAA,CAAAA,CAAAA;AACvE;;;;"}