var Sr=Object.defineProperty;var an=Object.getOwnPropertyDescriptor;var fn=Object.getOwnPropertyNames;var ln=Object.prototype.hasOwnProperty;var pn=(e,t)=>{for(var r in t)Sr(e,r,{get:t[r],enumerable:!0})},un=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of fn(t))!ln.call(e,n)&&n!==r&&Sr(e,n,{get:()=>t[n],enumerable:!(o=an(t,n))||o.enumerable});return e};var mn=e=>un(Sr({},"__esModule",{value:!0}),e);var Oi={};pn(Oi,{a98:()=>ii,average:()=>a0,averageAngle:()=>H,averageNumber:()=>Dr,blend:()=>v0,blerp:()=>jt,clampChroma:()=>D0,clampGamut:()=>kr,clampRgb:()=>q0,colorsNamed:()=>Pt,convertA98ToXyz65:()=>tt,convertCubehelixToRgb:()=>Zt,convertDlchToLab65:()=>ye,convertHsiToRgb:()=>nt,convertHslToRgb:()=>ft,convertHsvToRgb:()=>ze,convertHwbToRgb:()=>ut,convertJabToJch:()=>st,convertJabToRgb:()=>De,convertJabToXyz65:()=>Ae,convertJchToJab:()=>dt,convertLab65ToDlch:()=>Te,convertLab65ToRgb:()=>re,convertLab65ToXyz65:()=>Ne,convertLabToLch:()=>I,convertLabToRgb:()=>Ee,convertLabToXyz50:()=>se,convertLchToLab:()=>A,convertLchuvToLuv:()=>ht,convertLrgbToOklab:()=>Ye,convertLrgbToRgb:()=>j,convertLuvToLchuv:()=>ct,convertLuvToXyz50:()=>_e,convertOkhslToOklab:()=>Be,convertOkhsvToOklab:()=>Fe,convertOklabToLrgb:()=>U,convertOklabToOkhsl:()=>Ge,convertOklabToOkhsv:()=>Ze,convertOklabToRgb:()=>ae,convertP3ToXyz65:()=>gt,convertProphotoToXyz50:()=>yt,convertRec2020ToXyz65:()=>zt,convertRgbToCubehelix:()=>Bt,convertRgbToHsi:()=>at,convertRgbToHsl:()=>it,convertRgbToHsv:()=>ke,convertRgbToHwb:()=>mt,convertRgbToJab:()=>qe,convertRgbToLab:()=>je,convertRgbToLab65:()=>oe,convertRgbToLrgb:()=>J,convertRgbToOklab:()=>ne,convertRgbToXyb:()=>ir,convertRgbToXyz50:()=>W,convertRgbToXyz65:()=>E,convertRgbToYiq:()=>mr,convertXybToRgb:()=>lr,convertXyz50ToLab:()=>de,convertXyz50ToLuv:()=>we,convertXyz50ToProphoto:()=>Mt,convertXyz50ToRgb:()=>F,convertXyz50ToXyz65:()=>ur,convertXyz65ToA98:()=>rt,convertXyz65ToJab:()=>Ie,convertXyz65ToLab65:()=>$e,convertXyz65ToP3:()=>vt,convertXyz65ToRec2020:()=>Tt,convertXyz65ToRgb:()=>Y,convertXyz65ToXyz50:()=>pr,convertYiqToRgb:()=>sr,converter:()=>g,cubehelix:()=>li,differenceCie76:()=>Vo,differenceCie94:()=>e0,differenceCiede2000:()=>t0,differenceCmc:()=>r0,differenceEuclidean:()=>pe,differenceHueChroma:()=>te,differenceHueNaive:()=>Ft,differenceHueSaturation:()=>ee,differenceHyab:()=>o0,differenceKotsarenkoRamos:()=>n0,displayable:()=>zr,dlab:()=>pi,dlch:()=>ui,easingGamma:()=>Tr,easingInOutSine:()=>rn,easingMidpoint:()=>xr,easingSmootherstep:()=>tn,easingSmoothstep:()=>V0,easingSmoothstepInverse:()=>en,filterBrightness:()=>j0,filterContrast:()=>Y0,filterDeficiencyDeuter:()=>K0,filterDeficiencyProt:()=>U0,filterDeficiencyTrit:()=>Q0,filterGrayscale:()=>Z0,filterHueRotate:()=>W0,filterInvert:()=>F0,filterSaturate:()=>B0,filterSepia:()=>G0,fixupAlpha:()=>M,fixupHueDecreasing:()=>Zo,fixupHueIncreasing:()=>Bo,fixupHueLonger:()=>Go,fixupHueShorter:()=>w,formatCss:()=>c0,formatHex:()=>h0,formatHex8:()=>b0,formatHsl:()=>g0,formatRgb:()=>x0,getMode:()=>L,hsi:()=>mi,hsl:()=>si,hsv:()=>di,hwb:()=>ci,inGamut:()=>wt,interpolate:()=>L0,interpolateWith:()=>So,interpolateWithPremultipliedAlpha:()=>R0,interpolatorLinear:()=>u,interpolatorPiecewise:()=>Yt,interpolatorSplineBasis:()=>Mr,interpolatorSplineBasisClosed:()=>yr,interpolatorSplineMonotone:()=>X0,interpolatorSplineMonotone2:()=>P0,interpolatorSplineMonotoneClosed:()=>N0,interpolatorSplineNatural:()=>H0,interpolatorSplineNaturalClosed:()=>S0,jab:()=>hi,jch:()=>bi,lab:()=>xi,lab65:()=>gi,lch:()=>vi,lch65:()=>Mi,lchuv:()=>yi,lerp:()=>V,lrgb:()=>Ti,luv:()=>zi,mapAlphaDivide:()=>br,mapAlphaMultiply:()=>hr,mapTransferGamma:()=>y0,mapTransferLinear:()=>Rt,mapper:()=>ce,modeA98:()=>Ar,modeCubehelix:()=>Jr,modeDlab:()=>Gr,modeDlch:()=>Br,modeHsi:()=>Zr,modeHsl:()=>lt,modeHsv:()=>pt,modeHwb:()=>Fr,modeJab:()=>Vr,modeJch:()=>eo,modeLab:()=>Le,modeLab65:()=>oo,modeLch:()=>Re,modeLch65:()=>no,modeLchuv:()=>ao,modeLrgb:()=>fo,modeLuv:()=>io,modeOkhsl:()=>po,modeOkhsv:()=>uo,modeOklab:()=>mo,modeOklch:()=>so,modeP3:()=>co,modeProphoto:()=>xo,modeRec2020:()=>Mo,modeRgb:()=>G,modeXyb:()=>zo,modeXyz50:()=>ko,modeXyz65:()=>Lo,modeYiq:()=>Ro,nearest:()=>E0,okhsl:()=>ki,okhsv:()=>Li,oklab:()=>Ri,oklch:()=>wi,p3:()=>_i,parse:()=>Ot,parseHex:()=>$t,parseHsl:()=>Vt,parseHslLegacy:()=>Qt,parseHwb:()=>er,parseLab:()=>tr,parseLch:()=>rr,parseNamed:()=>Nt,parseOklab:()=>nr,parseOklch:()=>ar,parseRgb:()=>Jt,parseRgbLegacy:()=>Ct,parseTransparent:()=>Et,prophoto:()=>Hi,random:()=>M0,rec2020:()=>Si,removeParser:()=>Jo,rgb:()=>Xi,round:()=>dr,samples:()=>$0,serializeHex:()=>cr,serializeHex8:()=>wo,serializeHsl:()=>Ho,serializeRgb:()=>_o,toGamut:()=>J0,trilerp:()=>Yo,unlerp:()=>jo,useMode:()=>T,useParser:()=>Xr,wcagContrast:()=>on,wcagLuminance:()=>Rr,xyb:()=>Pi,xyz50:()=>Ni,xyz65:()=>$i,yiq:()=>Ci});module.exports=mn(Oi);var sn=(e,t)=>{if(typeof e=="number"){if(t===3)return{mode:"rgb",r:(e>>8&15|e>>4&240)/255,g:(e>>4&15|e&240)/255,b:(e&15|e<<4&240)/255};if(t===4)return{mode:"rgb",r:(e>>12&15|e>>8&240)/255,g:(e>>8&15|e>>4&240)/255,b:(e>>4&15|e&240)/255,alpha:(e&15|e<<4&240)/255};if(t===6)return{mode:"rgb",r:(e>>16&255)/255,g:(e>>8&255)/255,b:(e&255)/255};if(t===8)return{mode:"rgb",r:(e>>24&255)/255,g:(e>>16&255)/255,b:(e>>8&255)/255,alpha:(e&255)/255}}},Xt=sn;var dn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},Pt=dn;var cn=e=>Xt(Pt[e.toLowerCase()],6),Nt=cn;var hn=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,bn=e=>{let t;return(t=e.match(hn))?Xt(parseInt(t[1],16),t[1].length):void 0},$t=bn;var O="([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)",Gi=`(?:${O}|none)`,ge=`${O}%`,Bi=`(?:${O}%|none)`,Ve=`(?:${O}%|${O})`,xn=`(?:${O}%|${O}|none)`,qo=`(?:${O}(deg|grad|rad|turn)|${O})`,Zi=`(?:${O}(deg|grad|rad|turn)|${O}|none)`,le="\\s*,\\s*";var Fi=new RegExp("^"+xn+"$");var gn=new RegExp(`^rgba?\\(\\s*${O}${le}${O}${le}${O}\\s*(?:,\\s*${Ve}\\s*)?\\)$`),vn=new RegExp(`^rgba?\\(\\s*${ge}${le}${ge}${le}${ge}\\s*(?:,\\s*${Ve}\\s*)?\\)$`),Mn=e=>{let t={mode:"rgb"},r;if(r=e.match(gn))r[1]!==void 0&&(t.r=r[1]/255),r[2]!==void 0&&(t.g=r[2]/255),r[3]!==void 0&&(t.b=r[3]/255);else if(r=e.match(vn))r[1]!==void 0&&(t.r=r[1]/100),r[2]!==void 0&&(t.g=r[2]/100),r[3]!==void 0&&(t.b=r[3]/100);else return;return r[4]!==void 0?t.alpha=r[4]/100:r[5]!==void 0&&(t.alpha=+r[5]),t},Ct=Mn;var yn=(e,t)=>e===void 0?void 0:typeof e!="object"?Ot(e):e.mode!==void 0?e:t?{...e,mode:t}:void 0,P=yn;var Tn=(e="rgb")=>t=>(t=P(t,e))!==void 0?t.mode===e?t:K[t.mode][e]?K[t.mode][e](t):e==="rgb"?K[t.mode].rgb(t):K.rgb[e](K[t.mode].rgb(t)):void 0,g=Tn;var K={},Do={},ve=[],It={},zn=e=>e,T=e=>(K[e.mode]={...K[e.mode],...e.toMode},Object.keys(e.fromMode||{}).forEach(t=>{K[t]||(K[t]={}),K[t][e.mode]=e.fromMode[t]}),e.ranges||(e.ranges={}),e.difference||(e.difference={}),e.channels.forEach(t=>{if(e.ranges[t]===void 0&&(e.ranges[t]=[0,1]),!e.interpolate[t])throw new Error(`Missing interpolator for: ${t}`);typeof e.interpolate[t]=="function"&&(e.interpolate[t]={use:e.interpolate[t]}),e.interpolate[t].fixup||(e.interpolate[t].fixup=zn)}),Do[e.mode]=e,(e.parse||[]).forEach(t=>{Xr(t,e.mode)}),g(e.mode)),L=e=>Do[e],Xr=(e,t)=>{if(typeof e=="string"){if(!t)throw new Error("'mode' required when 'parser' is a string");It[e]=t}else typeof e=="function"&&ve.indexOf(e)<0&&ve.push(e)},Jo=e=>{if(typeof e=="string")delete It[e];else if(typeof e=="function"){let t=ve.indexOf(e);t>0&&ve.splice(t,1)}};var Pr=/[^\x00-\x7F]|[a-zA-Z_]/,kn=/[^\x00-\x7F]|[-\w]/,p={Function:"function",Ident:"ident",Number:"number",Percentage:"percentage",ParenClose:")",None:"none",Hue:"hue",Alpha:"alpha"},x=0;function At(e){let t=e[x],r=e[x+1];return t==="-"||t==="+"?/\d/.test(r)||r==="."&&/\d/.test(e[x+2]):t==="."?/\d/.test(r):/\d/.test(t)}function Nr(e){if(x>=e.length)return!1;let t=e[x];if(Pr.test(t))return!0;if(t==="-"){if(e.length-x<2)return!1;let r=e[x+1];return!!(r==="-"||Pr.test(r))}return!1}var Ln={deg:1,rad:180/Math.PI,grad:9/10,turn:360};function et(e){let t="";if((e[x]==="-"||e[x]==="+")&&(t+=e[x++]),t+=qt(e),e[x]==="."&&/\d/.test(e[x+1])&&(t+=e[x++]+qt(e)),(e[x]==="e"||e[x]==="E")&&((e[x+1]==="-"||e[x+1]==="+")&&/\d/.test(e[x+2])?t+=e[x++]+e[x++]+qt(e):/\d/.test(e[x+1])&&(t+=e[x++]+qt(e))),Nr(e)){let r=Dt(e);return r==="deg"||r==="rad"||r==="turn"||r==="grad"?{type:p.Hue,value:t*Ln[r]}:void 0}return e[x]==="%"?(x++,{type:p.Percentage,value:+t}):{type:p.Number,value:+t}}function qt(e){let t="";for(;/\d/.test(e[x]);)t+=e[x++];return t}function Dt(e){let t="";for(;x<e.length&&kn.test(e[x]);)t+=e[x++];return t}function Rn(e){let t=Dt(e);return e[x]==="("?(x++,{type:p.Function,value:t}):t==="none"?{type:p.None,value:void 0}:{type:p.Ident,value:t}}function wn(e=""){let t=e.trim(),r=[],o;for(x=0;x<t.length;){if(o=t[x++],o===`
`||o==="	"||o===" "){for(;x<t.length&&(t[x]===`
`||t[x]==="	"||t[x]===" ");)x++;continue}if(o===",")return;if(o===")"){r.push({type:p.ParenClose});continue}if(o==="+"){if(x--,At(t)){r.push(et(t));continue}return}if(o==="-"){if(x--,At(t)){r.push(et(t));continue}if(Nr(t)){r.push({type:p.Ident,value:Dt(t)});continue}return}if(o==="."){if(x--,At(t)){r.push(et(t));continue}return}if(o==="/"){for(;x<t.length&&(t[x]===`
`||t[x]==="	"||t[x]===" ");)x++;let n;if(At(t)&&(n=et(t),n.type!==p.Hue)){r.push({type:p.Alpha,value:n});continue}if(Nr(t)&&Dt(t)==="none"){r.push({type:p.Alpha,value:{type:p.None,value:void 0}});continue}return}if(/\d/.test(o)){x--,r.push(et(t));continue}if(Pr.test(o)){x--,r.push(Rn(t));continue}return}return r}function _n(e){e._i=0;let t=e[e._i++];if(!t||t.type!==p.Function||t.value!=="color"||(t=e[e._i++],t.type!==p.Ident))return;let r=It[t.value];if(!r)return;let o={mode:r},n=Eo(e,!1);if(!n)return;let a=L(r).channels;for(let f=0,i;f<a.length;f++)i=n[f],i.type!==p.None&&(o[a[f]]=i.type===p.Number?i.value:i.value/100);return o}function Eo(e,t){let r=[],o;for(;e._i<e.length;){if(o=e[e._i++],o.type===p.None||o.type===p.Number||o.type===p.Alpha||o.type===p.Percentage||t&&o.type===p.Hue){r.push(o);continue}if(o.type===p.ParenClose){if(e._i<e.length)return;continue}return}if(!(r.length<3||r.length>4)){if(r.length===4){if(r[3].type!==p.Alpha)return;r[3]=r[3].value}return r.length===3&&r.push({type:p.None,value:void 0}),r.every(n=>n.type!==p.Alpha)?r:void 0}}function Hn(e,t){e._i=0;let r=e[e._i++];if(!r||r.type!==p.Function)return;let o=Eo(e,t);if(o)return o.unshift(r.value),o}var Sn=e=>{if(typeof e!="string")return;let t=wn(e),r=t?Hn(t,!0):void 0,o,n=0,a=ve.length;for(;n<a;)if((o=ve[n++](e,r))!==void 0)return o;return t?_n(t):void 0},Ot=Sn;function Xn(e,t){if(!t||t[0]!=="rgb"&&t[0]!=="rgba")return;let r={mode:"rgb"},[,o,n,a,f]=t;if(!(o.type===p.Hue||n.type===p.Hue||a.type===p.Hue))return o.type!==p.None&&(r.r=o.type===p.Number?o.value/255:o.value/100),n.type!==p.None&&(r.g=n.type===p.Number?n.value/255:n.value/100),a.type!==p.None&&(r.b=a.type===p.Number?a.value/255:a.value/100),f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var Jt=Xn;var Pn=e=>e==="transparent"?{mode:"rgb",r:0,g:0,b:0,alpha:0}:void 0,Et=Pn;var V=(e,t,r)=>e+r*(t-e),jo=(e,t,r)=>(r-e)/(t-e),jt=(e,t,r,o,n,a)=>V(V(e,t,n),V(r,o,n),a),Yo=(e,t,r,o,n,a,f,i,l,m,s)=>V(jt(e,t,r,o,l,m),jt(n,a,f,i,l,m),s);var Nn=e=>{let t=[];for(let r=0;r<e.length-1;r++){let o=e[r],n=e[r+1];o===void 0&&n===void 0?t.push(void 0):o!==void 0&&n!==void 0?t.push([o,n]):t.push(o!==void 0?[o,o]:[n,n])}return t},Yt=e=>t=>{let r=Nn(t);return o=>{let n=o*r.length,a=o>=1?r.length-1:Math.max(Math.floor(n),0),f=r[a];return f===void 0?void 0:e(f[0],f[1],n-a)}};var u=Yt(V);var M=e=>{let t=!1,r=e.map(o=>o!==void 0?(t=!0,o):1);return t?r:e};var $n={mode:"rgb",channels:["r","g","b","alpha"],parse:[Jt,$t,Ct,Nt,Et,"srgb"],serialize:"srgb",interpolate:{r:u,g:u,b:u,alpha:{use:u,fixup:M}},gamut:!0},G=$n;var $r=e=>Math.pow(Math.abs(e),2.19921875)*Math.sign(e),Cn=e=>{let t=$r(e.r),r=$r(e.g),o=$r(e.b),n={mode:"xyz65",x:.5766690429101305*t+.1855582379065463*r+.1882286462349947*o,y:.297344975250536*t+.6273635662554661*r+.0752914584939979*o,z:.0270313613864123*t+.0706888525358272*r+.9913375368376386*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},tt=Cn;var Cr=e=>Math.pow(Math.abs(e),.4547069271758437)*Math.sign(e),On=({x:e,y:t,z:r,alpha:o})=>{let n={mode:"a98",r:Cr(e*2.0415879038107465-t*.5650069742788597-.3447313507783297*r),g:Cr(e*-.9692436362808798+t*1.8759675015077206+.0415550574071756*r),b:Cr(e*.0134442806320312-t*.1183623922310184+1.0151749943912058*r)};return o!==void 0&&(n.alpha=o),n},rt=On;var Or=e=>{let t=Math.abs(e);return t<=.04045?e/12.92:(Math.sign(e)||1)*Math.pow((t+.055)/1.055,2.4)},In=({r:e,g:t,b:r,alpha:o})=>{let n={mode:"lrgb",r:Or(e),g:Or(t),b:Or(r)};return o!==void 0&&(n.alpha=o),n},J=In;var An=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a={mode:"xyz65",x:.4123907992659593*t+.357584339383878*r+.1804807884018343*o,y:.2126390058715102*t+.715168678767756*r+.0721923153607337*o,z:.0193308187155918*t+.119194779794626*r+.9505321522496607*o};return n!==void 0&&(a.alpha=n),a},E=An;var Ir=e=>{let t=Math.abs(e);return t>.0031308?(Math.sign(e)||1)*(1.055*Math.pow(t,.4166666666666667)-.055):e*12.92},qn=({r:e,g:t,b:r,alpha:o},n="rgb")=>{let a={mode:n,r:Ir(e),g:Ir(t),b:Ir(r)};return o!==void 0&&(a.alpha=o),a},j=qn;var Dn=({x:e,y:t,z:r,alpha:o})=>{let n=j({r:e*3.2409699419045226-t*1.537383177570094-.4986107602930034*r,g:e*-.9692436362808796+t*1.8759675015077204+.0415550574071756*r,b:e*.0556300796969936-t*.2039769588889765+1.0569715142428784*r});return o!==void 0&&(n.alpha=o),n},Y=Dn;var Jn={...G,mode:"a98",parse:["a98-rgb"],serialize:"a98-rgb",fromMode:{rgb:e=>rt(E(e)),xyz65:rt},toMode:{rgb:e=>Y(tt(e)),xyz65:tt}},Ar=Jn;var En=e=>(e=e%360)<0?e+360:e,k=En;var Gt=(e,t)=>e.map((r,o,n)=>{if(r===void 0)return r;let a=k(r);return o===0||e[o-1]===void 0?a:t(a-k(n[o-1]))}).reduce((r,o)=>!r.length||o===void 0||r[r.length-1]===void 0?(r.push(o),r):(r.push(o+r[r.length-1]),r),[]),w=e=>Gt(e,t=>Math.abs(t)<=180?t:t-360*Math.sign(t)),Go=e=>Gt(e,t=>Math.abs(t)>=180||t===0?t:t-360*Math.sign(t)),Bo=e=>Gt(e,t=>t>=0?t:t+360),Zo=e=>Gt(e,t=>t<=0?t:t-360);var _=[-.14861,1.78277,-.29227,-.90649,1.97294,0],Fo=Math.PI/180,Wo=180/Math.PI;var Uo=_[3]*_[4],Ko=_[1]*_[4],Qo=_[1]*_[2]-_[0]*_[3],jn=({r:e,g:t,b:r,alpha:o})=>{let n=(Qo*r+e*Uo-t*Ko)/(Qo+Uo-Ko),a=r-n,f=(_[4]*(t-n)-_[2]*a)/_[3],i={mode:"cubehelix",l:n,s:n===0||n===1?void 0:Math.sqrt(a*a+f*f)/(_[4]*n*(1-n))};return i.s&&(i.h=Math.atan2(f,a)*Wo-120),o!==void 0&&(i.alpha=o),i},Bt=jn;var Yn=({h:e,s:t,l:r,alpha:o})=>{let n={mode:"rgb"};e=(e===void 0?0:e+120)*Fo;let a=t===void 0?0:t*r*(1-r),f=Math.cos(e),i=Math.sin(e);return n.r=r+a*(_[0]*f+_[1]*i),n.g=r+a*(_[2]*f+_[3]*i),n.b=r+a*(_[4]*f+_[5]*i),o!==void 0&&(n.alpha=o),n},Zt=Yn;var ee=(e,t)=>{if(e.h===void 0||t.h===void 0||!e.s||!t.s)return 0;let r=k(e.h),o=k(t.h),n=Math.sin((o-r+360)/2*Math.PI/180);return 2*Math.sqrt(e.s*t.s)*n},Ft=(e,t)=>{if(e.h===void 0||t.h===void 0)return 0;let r=k(e.h),o=k(t.h);return Math.abs(o-r)>180?r-(o-360*Math.sign(o-r)):o-r},te=(e,t)=>{if(e.h===void 0||t.h===void 0||!e.c||!t.c)return 0;let r=k(e.h),o=k(t.h),n=Math.sin((o-r+360)/2*Math.PI/180);return 2*Math.sqrt(e.c*t.c)*n},pe=(e="rgb",t=[1,1,1,0])=>{let r=L(e),o=r.channels,n=r.difference,a=g(e);return(f,i)=>{let l=a(f),m=a(i);return Math.sqrt(o.reduce((s,d,h)=>{let c=n[d]?n[d](l,m):l[d]-m[d];return s+(t[h]||0)*Math.pow(isNaN(c)?0:c,2)},0))}},Vo=()=>pe("lab65"),e0=(e=1,t=.045,r=.015)=>{let o=g("lab65");return(n,a)=>{let f=o(n),i=o(a),l=f.l,m=f.a,s=f.b,d=Math.sqrt(m*m+s*s),h=i.l,c=i.a,b=i.b,y=Math.sqrt(c*c+b*b),v=Math.pow(l-h,2),z=Math.pow(d-y,2),S=Math.pow(m-c,2)+Math.pow(s-b,2)-z;return Math.sqrt(v/Math.pow(e,2)+z/Math.pow(1+t*d,2)+S/Math.pow(1+r*d,2))}},t0=(e=1,t=1,r=1)=>{let o=g("lab65");return(n,a)=>{let f=o(n),i=o(a),l=f.l,m=f.a,s=f.b,d=Math.sqrt(m*m+s*s),h=i.l,c=i.a,b=i.b,y=Math.sqrt(c*c+b*b),v=(d+y)/2,z=.5*(1-Math.sqrt(Math.pow(v,7)/(Math.pow(v,7)+Math.pow(25,7)))),S=m*(1+z),X=c*(1+z),N=Math.sqrt(S*S+s*s),$=Math.sqrt(X*X+b*b),C=Math.abs(S)+Math.abs(s)===0?0:Math.atan2(s,S);C+=(C<0)*2*Math.PI;let q=Math.abs(X)+Math.abs(b)===0?0:Math.atan2(b,X);q+=(q<0)*2*Math.PI;let he=h-l,ie=$-N,Z=N*$===0?0:q-C;Z-=(Z>Math.PI)*2*Math.PI,Z+=(Z<-Math.PI)*2*Math.PI;let Q=2*Math.sqrt(N*$)*Math.sin(Z/2),be=(l+h)/2,xe=(N+$)/2,D;N*$===0?D=C+q:(D=(C+q)/2,D-=(Math.abs(C-q)>Math.PI)*Math.PI,D+=(D<0)*2*Math.PI);let _t=Math.pow(be-50,2),Ht=1-.17*Math.cos(D-Math.PI/6)+.24*Math.cos(2*D)+.32*Math.cos(3*D+Math.PI/30)-.2*Math.cos(4*D-63*Math.PI/180),Ue=1+.015*_t/Math.sqrt(20+_t),Ke=1+.045*xe,Pe=1+.015*xe*Ht,wr=30*Math.PI/180*Math.exp(-1*Math.pow((180/Math.PI*D-275)/25,2)),St=2*Math.sqrt(Math.pow(xe,7)/(Math.pow(xe,7)+Math.pow(25,7))),Qe=-1*Math.sin(2*wr)*St;return Math.sqrt(Math.pow(he/(e*Ue),2)+Math.pow(ie/(t*Ke),2)+Math.pow(Q/(r*Pe),2)+Qe*ie/(t*Ke)*Q/(r*Pe))}},r0=(e=1,t=1)=>{let r=g("lab65");return(o,n)=>{let a=r(o),f=a.l,i=a.a,l=a.b,m=Math.sqrt(i*i+l*l),s=Math.atan2(l,i);s=s+2*Math.PI*(s<0);let d=r(n),h=d.l,c=d.a,b=d.b,y=Math.sqrt(c*c+b*b),v=Math.pow(f-h,2),z=Math.pow(m-y,2),S=Math.pow(i-c,2)+Math.pow(l-b,2)-z,X=Math.sqrt(Math.pow(m,4)/(Math.pow(m,4)+1900)),N=s>=164/180*Math.PI&&s<=345/180*Math.PI?.56+Math.abs(.2*Math.cos(s+168/180*Math.PI)):.36+Math.abs(.4*Math.cos(s+35/180*Math.PI)),$=f<16?.511:.040975*f/(1+.01765*f),C=.0638*m/(1+.0131*m)+.638,q=C*(X*N+1-X);return Math.sqrt(v/Math.pow(e*$,2)+z/Math.pow(t*C,2)+S/Math.pow(q,2))}},o0=()=>{let e=g("lab65");return(t,r)=>{let o=e(t),n=e(r),a=o.l-n.l,f=o.a-n.a,i=o.b-n.b;return Math.abs(a)+Math.sqrt(f*f+i*i)}},n0=()=>pe("yiq",[.5053,.299,.1957]);var H=e=>{let t=e.reduce((r,o)=>{if(o!==void 0){let n=o*Math.PI/180;r.sin+=Math.sin(n),r.cos+=Math.cos(n)}return r},{sin:0,cos:0});return Math.atan2(t.sin,t.cos)*180/Math.PI},Dr=e=>{let t=e.filter(r=>r!==void 0);return t.length?t.reduce((r,o)=>r+o,0)/t.length:void 0},qr=e=>typeof e=="function";function a0(e,t="rgb",r){let o=L(t),n=e.map(g(t));return o.channels.reduce((a,f)=>{let i=n.map(l=>l[f]).filter(l=>l!==void 0);if(i.length){let l;qr(r)?l=r:r&&qr(r[f])?l=r[f]:o.average&&qr(o.average[f])?l=o.average[f]:l=Dr,a[f]=l(i,f)}return a},{mode:t})}var Gn={mode:"cubehelix",channels:["h","s","l","alpha"],parse:["--cubehelix"],serialize:"--cubehelix",ranges:{h:[0,360],s:[0,4.614],l:[0,1]},fromMode:{rgb:Bt},toMode:{rgb:Zt},interpolate:{h:{use:u,fixup:w},s:u,l:u,alpha:{use:u,fixup:M}},difference:{h:ee},average:{h:H}},Jr=Gn;var Bn=({l:e,a:t,b:r,alpha:o},n="lch")=>{let a=Math.sqrt(t*t+r*r),f={mode:n,l:e,c:a};return a&&(f.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(f.alpha=o),f},I=Bn;var Zn=({l:e,c:t,h:r,alpha:o},n="lab")=>{let a={mode:n,l:e,a:t?t*Math.cos(r/180*Math.PI):0,b:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(a.alpha=o),a},A=Zn;var Wt=Math.pow(29,3)/Math.pow(3,3),Ut=Math.pow(6,3)/Math.pow(29,3);var R={X:.9642956764295677,Y:1,Z:.8251046025104602},ue={X:.3127/.329,Y:1,Z:(1-.3127-.329)/.329},mp=Math.pow(29,3)/Math.pow(3,3),sp=Math.pow(6,3)/Math.pow(29,3);var Er=e=>Math.pow(e,3)>Ut?Math.pow(e,3):(116*e-16)/Wt,Fn=({l:e,a:t,b:r,alpha:o})=>{let n=(e+16)/116,a=t/500+n,f=n-r/200,i={mode:"xyz65",x:Er(a)*ue.X,y:Er(n)*ue.Y,z:Er(f)*ue.Z};return o!==void 0&&(i.alpha=o),i},Ne=Fn;var Wn=e=>Y(Ne(e)),re=Wn;var jr=e=>e>Ut?Math.cbrt(e):(Wt*e+16)/116,Un=({x:e,y:t,z:r,alpha:o})=>{let n=jr(e/ue.X),a=jr(t/ue.Y),f=jr(r/ue.Z),i={mode:"lab65",l:116*a-16,a:500*(n-a),b:200*(a-f)};return o!==void 0&&(i.alpha=o),i},$e=Un;var Kn=e=>{let t=$e(E(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},oe=Kn;var Me=.14444444444444443*Math.PI,Ce=Math.cos(Me),Oe=Math.sin(Me),Kt=100/Math.log(139/100);var Qn=({l:e,c:t,h:r,alpha:o})=>{let n={mode:"lab65",l:(Math.exp(e*1/Kt)-1)/.0039};if(r===void 0)n.a=n.b=0;else{let a=(Math.exp(.0435*t*1*1)-1)/.075,f=a*Math.cos(r/180*Math.PI-Me),i=a*Math.sin(r/180*Math.PI-Me);n.a=f*Ce-i/.83*Oe,n.b=f*Oe+i/.83*Ce}return o!==void 0&&(n.alpha=o),n},ye=Qn;var Vn=({l:e,a:t,b:r,alpha:o})=>{let n=t*Ce+r*Oe,a=.83*(r*Ce-t*Oe),f=Math.sqrt(n*n+a*a),i={mode:"dlch",l:Kt/1*Math.log(1+.0039*e),c:Math.log(1+.075*f)/(.0435*1*1)};return i.c&&(i.h=k((Math.atan2(a,n)+Me)/Math.PI*180)),o!==void 0&&(i.alpha=o),i},Te=Vn;var f0=e=>ye(I(e,"dlch")),i0=e=>A(Te(e),"dlab"),ea={mode:"dlab",parse:["--din99o-lab"],serialize:"--din99o-lab",toMode:{lab65:f0,rgb:e=>re(f0(e))},fromMode:{lab65:i0,rgb:e=>i0(oe(e))},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-40.09,45.501],b:[-40.469,44.344]},interpolate:{l:u,a:u,b:u,alpha:{use:u,fixup:M}}},Gr=ea;var ta={mode:"dlch",parse:["--din99o-lch"],serialize:"--din99o-lch",toMode:{lab65:ye,dlab:e=>A(e,"dlab"),rgb:e=>re(ye(e))},fromMode:{lab65:Te,dlab:e=>I(e,"dlch"),rgb:e=>Te(oe(e))},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,51.484],h:[0,360]},interpolate:{l:u,c:u,h:{use:u,fixup:w},alpha:{use:u,fixup:M}},difference:{h:te},average:{h:H}},Br=ta;function nt({h:e,s:t,i:r,alpha:o}){e=k(e);let n=Math.abs(e/60%2-1),a;switch(Math.floor(e/60)){case 0:a={r:r*(1+t*(3/(2-n)-1)),g:r*(1+t*(3*(1-n)/(2-n)-1)),b:r*(1-t)};break;case 1:a={r:r*(1+t*(3*(1-n)/(2-n)-1)),g:r*(1+t*(3/(2-n)-1)),b:r*(1-t)};break;case 2:a={r:r*(1-t),g:r*(1+t*(3/(2-n)-1)),b:r*(1+t*(3*(1-n)/(2-n)-1))};break;case 3:a={r:r*(1-t),g:r*(1+t*(3*(1-n)/(2-n)-1)),b:r*(1+t*(3/(2-n)-1))};break;case 4:a={r:r*(1+t*(3*(1-n)/(2-n)-1)),g:r*(1-t),b:r*(1+t*(3/(2-n)-1))};break;case 5:a={r:r*(1+t*(3/(2-n)-1)),g:r*(1-t),b:r*(1+t*(3*(1-n)/(2-n)-1))};break;default:a={r:r*(1-t),g:r*(1-t),b:r*(1-t)}}return a.mode="rgb",o!==void 0&&(a.alpha=o),a}function at({r:e,g:t,b:r,alpha:o}){let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsi",s:e+t+r===0?0:1-3*a/(e+t+r),i:(e+t+r)/3};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var ra={mode:"hsi",toMode:{rgb:nt},parse:["--hsi"],serialize:"--hsi",fromMode:{rgb:at},channels:["h","s","i","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:u,fixup:w},s:u,i:u,alpha:{use:u,fixup:M}},difference:{h:ee},average:{h:H}},Zr=ra;function ft({h:e,s:t,l:r,alpha:o}){e=k(e);let n=r+t*(r<.5?r:1-r),a=n-(n-r)*2*Math.abs(e/60%2-1),f;switch(Math.floor(e/60)){case 0:f={r:n,g:a,b:2*r-n};break;case 1:f={r:a,g:n,b:2*r-n};break;case 2:f={r:2*r-n,g:n,b:a};break;case 3:f={r:2*r-n,g:a,b:n};break;case 4:f={r:a,g:2*r-n,b:n};break;case 5:f={r:n,g:2*r-n,b:a};break;default:f={r:2*r-n,g:2*r-n,b:2*r-n}}return f.mode="rgb",o!==void 0&&(f.alpha=o),f}function it({r:e,g:t,b:r,alpha:o}){let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsl",s:n===a?0:(n-a)/(1-Math.abs(n+a-1)),l:.5*(n+a)};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var oa=(e,t)=>{switch(t){case"deg":return+e;case"rad":return e/Math.PI*180;case"grad":return e/10*9;case"turn":return e*360}},l0=oa;var na=new RegExp(`^hsla?\\(\\s*${qo}${le}${ge}${le}${ge}\\s*(?:,\\s*${Ve}\\s*)?\\)$`),aa=e=>{let t=e.match(na);if(!t)return;let r={mode:"hsl"};return t[3]!==void 0?r.h=+t[3]:t[1]!==void 0&&t[2]!==void 0&&(r.h=l0(t[1],t[2])),t[4]!==void 0&&(r.s=Math.min(Math.max(0,t[4]/100),1)),t[5]!==void 0&&(r.l=Math.min(Math.max(0,t[5]/100),1)),t[6]!==void 0?r.alpha=t[6]/100:t[7]!==void 0&&(r.alpha=+t[7]),r},Qt=aa;function fa(e,t){if(!t||t[0]!=="hsl"&&t[0]!=="hsla")return;let r={mode:"hsl"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Percentage)return;r.h=o.value}if(n.type!==p.None){if(n.type===p.Hue)return;r.s=n.type===p.Number?n.value:n.value/100}if(a.type!==p.None){if(a.type===p.Hue)return;r.l=a.type===p.Number?a.value:a.value/100}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var Vt=fa;var ia={mode:"hsl",toMode:{rgb:ft},fromMode:{rgb:it},channels:["h","s","l","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[Vt,Qt],serialize:e=>`hsl(${e.h||0} ${e.s!==void 0?e.s*100+"%":"none"} ${e.l!==void 0?e.l*100+"%":"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:u,fixup:w},s:u,l:u,alpha:{use:u,fixup:M}},difference:{h:ee},average:{h:H}},lt=ia;function ze({h:e,s:t,v:r,alpha:o}){e=k(e);let n=Math.abs(e/60%2-1),a;switch(Math.floor(e/60)){case 0:a={r,g:r*(1-t*n),b:r*(1-t)};break;case 1:a={r:r*(1-t*n),g:r,b:r*(1-t)};break;case 2:a={r:r*(1-t),g:r,b:r*(1-t*n)};break;case 3:a={r:r*(1-t),g:r*(1-t*n),b:r};break;case 4:a={r:r*(1-t*n),g:r*(1-t),b:r};break;case 5:a={r,g:r*(1-t),b:r*(1-t*n)};break;default:a={r:r*(1-t),g:r*(1-t),b:r*(1-t)}}return a.mode="rgb",o!==void 0&&(a.alpha=o),a}function ke({r:e,g:t,b:r,alpha:o}){let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsv",s:n===0?0:1-a/n,v:n};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var la={mode:"hsv",toMode:{rgb:ze},parse:["--hsv"],serialize:"--hsv",fromMode:{rgb:ke},channels:["h","s","v","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:u,fixup:w},s:u,v:u,alpha:{use:u,fixup:M}},difference:{h:ee},average:{h:H}},pt=la;function ut({h:e,w:t,b:r,alpha:o}){if(t+r>1){let n=t+r;t/=n,r/=n}return ze({h:e,s:r===1?1:1-t/(1-r),v:1-r,alpha:o})}function mt(e){let t=ke(e);if(t===void 0)return;let r={mode:"hwb",w:(1-t.s)*t.v,b:1-t.v};return t.h!==void 0&&(r.h=t.h),t.alpha!==void 0&&(r.alpha=t.alpha),r}function pa(e,t){if(!t||t[0]!=="hwb")return;let r={mode:"hwb"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Percentage)return;r.h=o.value}if(n.type!==p.None){if(n.type===p.Hue)return;r.w=n.type===p.Number?n.value:n.value/100}if(a.type!==p.None){if(a.type===p.Hue)return;r.b=a.type===p.Number?a.value:a.value/100}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var er=pa;var ua={mode:"hwb",toMode:{rgb:ut},fromMode:{rgb:mt},channels:["h","w","b","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[er],serialize:e=>`hwb(${e.h||0} ${e.w*100}% ${e.b*100}%${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:u,fixup:w},w:u,b:u,alpha:{use:u,fixup:M}},difference:{h:Ft},average:{h:H}},Fr=ua;var ma=.1593017578125,sa=134.03437499999998,da=.8359375,ca=18.8515625,ha=18.6875,ba=16295499532821565e-27,Wr=e=>{let t=Math.pow(e/1e4,ma);return Math.pow((da+ca*t)/(1+ha*t),sa)||0},Ur=e=>Math.max(e*203,0),xa=({x:e,y:t,z:r,alpha:o})=>{e=Ur(e),t=Ur(t),r=Ur(r);let n=1.15*e-.15*r,a=.66*t+.34*e,f=Wr(.41478972*n+.579999*a+.014648*r),i=Wr(-.20151*n+1.120649*a+.0531008*r),l=Wr(-.0166008*n+.2648*a+.6684799*r),m=(f+i)/2,s={mode:"jab",j:.44*m/(1-.56*m)-ba,a:3.524*f-4.066708*i+.542708*l,b:.199076*f+1.096799*i-1.295875*l};return o!==void 0&&(s.alpha=o),s},Ie=xa;var ga=.1593017578125,va=134.03437499999998,Ma=.8359375,ya=18.8515625,Ta=18.6875,p0=16295499532821565e-27,Kr=e=>{let t=Math.pow(e,1/va);return 1e4*Math.pow((Ma-t)/(Ta*t-ya),1/ga)||0},Qr=e=>e/203,za=({j:e,a:t,b:r,alpha:o})=>{let n=(e+p0)/(.44+.56*(e+p0)),a=Kr(n+.13860504*t+.058047316*r),f=Kr(n-.13860504*t-.058047316*r),i=Kr(n-.096019242*t-.8118919*r),l={mode:"xyz65",x:Qr(1.661373024652174*a-.914523081304348*f+.23136208173913045*i),y:Qr(-.3250758611844533*a+1.571847026732543*f-.21825383453227928*i),z:Qr(-.090982811*a-.31272829*f+1.5227666*i)};return o!==void 0&&(l.alpha=o),l},Ae=za;var ka=e=>{let t=Ie(E(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},qe=ka;var La=e=>Y(Ae(e)),De=La;var Ra={mode:"jab",channels:["j","a","b","alpha"],parse:["--jzazbz"],serialize:"--jzazbz",fromMode:{rgb:qe,xyz65:Ie},toMode:{rgb:De,xyz65:Ae},ranges:{j:[0,.222],a:[-.109,.129],b:[-.185,.134]},interpolate:{j:u,a:u,b:u,alpha:{use:u,fixup:M}}},Vr=Ra;var wa=({j:e,a:t,b:r,alpha:o})=>{let n=Math.sqrt(t*t+r*r),a={mode:"jch",j:e,c:n};return n&&(a.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(a.alpha=o),a},st=wa;var _a=({j:e,c:t,h:r,alpha:o})=>{let n={mode:"jab",j:e,a:t?t*Math.cos(r/180*Math.PI):0,b:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(n.alpha=o),n},dt=_a;var Ha={mode:"jch",parse:["--jzczhz"],serialize:"--jzczhz",toMode:{jab:dt,rgb:e=>De(dt(e))},fromMode:{rgb:e=>st(qe(e)),jab:st},channels:["j","c","h","alpha"],ranges:{j:[0,.221],c:[0,.19],h:[0,360]},interpolate:{h:{use:u,fixup:w},c:u,j:u,alpha:{use:u,fixup:M}},difference:{h:te},average:{h:H}},eo=Ha;var me=Math.pow(29,3)/Math.pow(3,3),Je=Math.pow(6,3)/Math.pow(29,3);var to=e=>Math.pow(e,3)>Je?Math.pow(e,3):(116*e-16)/me,Sa=({l:e,a:t,b:r,alpha:o})=>{let n=(e+16)/116,a=t/500+n,f=n-r/200,i={mode:"xyz50",x:to(a)*R.X,y:to(n)*R.Y,z:to(f)*R.Z};return o!==void 0&&(i.alpha=o),i},se=Sa;var Xa=({x:e,y:t,z:r,alpha:o})=>{let n=j({r:e*3.1341359569958707-t*1.6173863321612538-.4906619460083532*r,g:e*-.978795502912089+t*1.916254567259524+.03344273116131949*r,b:e*.07195537988411677-t*.2289768264158322+1.405386058324125*r});return o!==void 0&&(n.alpha=o),n},F=Xa;var Pa=e=>F(se(e)),Ee=Pa;var Na=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a={mode:"xyz50",x:.436065742824811*t+.3851514688337912*r+.14307845442264197*o,y:.22249319175623702*t+.7168870538238823*r+.06061979053616537*o,z:.013923904500943465*t+.09708128566574634*r+.7140993584005155*o};return n!==void 0&&(a.alpha=n),a},W=Na;var ro=e=>e>Je?Math.cbrt(e):(me*e+16)/116,$a=({x:e,y:t,z:r,alpha:o})=>{let n=ro(e/R.X),a=ro(t/R.Y),f=ro(r/R.Z),i={mode:"lab",l:116*a-16,a:500*(n-a),b:200*(a-f)};return o!==void 0&&(i.alpha=o),i},de=$a;var Ca=e=>{let t=de(W(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},je=Ca;function Oa(e,t){if(!t||t[0]!=="lab")return;let r={mode:"lab"},[,o,n,a,f]=t;if(!(o.type===p.Hue||n.type===p.Hue||a.type===p.Hue))return o.type!==p.None&&(r.l=o.value),n.type!==p.None&&(r.a=n.type===p.Number?n.value:n.value*125/100),a.type!==p.None&&(r.b=a.type===p.Number?a.value:a.value*125/100),f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var tr=Oa;var Ia={mode:"lab",toMode:{xyz50:se,rgb:Ee},fromMode:{xyz50:de,rgb:je},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-100,100],b:[-100,100]},parse:[tr],serialize:e=>`lab(${e.l!==void 0?e.l:"none"} ${e.a!==void 0?e.a:"none"} ${e.b!==void 0?e.b:"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{l:u,a:u,b:u,alpha:{use:u,fixup:M}}},Le=Ia;var Aa={...Le,mode:"lab65",parse:["--lab-d65"],serialize:"--lab-d65",toMode:{xyz65:Ne,rgb:re},fromMode:{xyz65:$e,rgb:oe},ranges:{l:[0,100],a:[-86.182,98.234],b:[-107.86,94.477]}},oo=Aa;function qa(e,t){if(!t||t[0]!=="lch")return;let r={mode:"lch"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Hue)return;r.l=o.value}if(n.type!==p.None&&(r.c=Math.max(0,n.type===p.Number?n.value:n.value*150/100)),a.type!==p.None){if(a.type===p.Percentage)return;r.h=a.value}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var rr=qa;var Da={mode:"lch",toMode:{lab:A,rgb:e=>Ee(A(e))},fromMode:{rgb:e=>I(je(e)),lab:I},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,150],h:[0,360]},parse:[rr],serialize:e=>`lch(${e.l!==void 0?e.l:"none"} ${e.c!==void 0?e.c:"none"} ${e.h||0}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:u,fixup:w},c:u,l:u,alpha:{use:u,fixup:M}},difference:{h:te},average:{h:H}},Re=Da;var Ja={...Re,mode:"lch65",parse:["--lch-d65"],serialize:"--lch-d65",toMode:{lab65:e=>A(e,"lab65"),rgb:e=>re(A(e,"lab65"))},fromMode:{rgb:e=>I(oe(e),"lch65"),lab65:e=>I(e,"lch65")},ranges:{l:[0,100],c:[0,133.807],h:[0,360]}},no=Ja;var Ea=({l:e,u:t,v:r,alpha:o})=>{let n=Math.sqrt(t*t+r*r),a={mode:"lchuv",l:e,c:n};return n&&(a.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(a.alpha=o),a},ct=Ea;var ja=({l:e,c:t,h:r,alpha:o})=>{let n={mode:"luv",l:e,u:t?t*Math.cos(r/180*Math.PI):0,v:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(n.alpha=o),n},ht=ja;var u0=(e,t,r)=>4*e/(e+15*t+3*r),m0=(e,t,r)=>9*t/(e+15*t+3*r),Ya=u0(R.X,R.Y,R.Z),Ga=m0(R.X,R.Y,R.Z),Ba=e=>e<=Je?me*e:116*Math.cbrt(e)-16,Za=({x:e,y:t,z:r,alpha:o})=>{let n=Ba(t/R.Y),a=u0(e,t,r),f=m0(e,t,r);!isFinite(a)||!isFinite(f)?n=a=f=0:(a=13*n*(a-Ya),f=13*n*(f-Ga));let i={mode:"luv",l:n,u:a,v:f};return o!==void 0&&(i.alpha=o),i},we=Za;var Fa=(e,t,r)=>4*e/(e+15*t+3*r),Wa=(e,t,r)=>9*t/(e+15*t+3*r),Ua=Fa(R.X,R.Y,R.Z),Ka=Wa(R.X,R.Y,R.Z),Qa=({l:e,u:t,v:r,alpha:o})=>{let n=t/(13*e)+Ua,a=r/(13*e)+Ka,f=R.Y*(e<=8?e/me:Math.pow((e+16)/116,3)),i=f*(9*n)/(4*a),l=f*(12-3*n-20*a)/(4*a),m={mode:"xyz50",x:i,y:f,z:l};return o!==void 0&&(m.alpha=o),m},_e=Qa;var Va=e=>ct(we(W(e))),ef=e=>F(_e(ht(e))),tf={mode:"lchuv",toMode:{luv:ht,rgb:ef},fromMode:{rgb:Va,luv:ct},channels:["l","c","h","alpha"],parse:["--lchuv"],serialize:"--lchuv",ranges:{l:[0,100],c:[0,176.956],h:[0,360]},interpolate:{h:{use:u,fixup:w},c:u,l:u,alpha:{use:u,fixup:M}},difference:{h:te},average:{h:H}},ao=tf;var rf={...G,mode:"lrgb",toMode:{rgb:j},fromMode:{rgb:J},parse:["srgb-linear"],serialize:"srgb-linear"},fo=rf;var of={mode:"luv",toMode:{xyz50:_e,rgb:e=>F(_e(e))},fromMode:{xyz50:we,rgb:e=>we(W(e))},channels:["l","u","v","alpha"],parse:["--luv"],serialize:"--luv",ranges:{l:[0,100],u:[-84.936,175.042],v:[-125.882,87.243]},interpolate:{l:u,u,v:u,alpha:{use:u,fixup:M}}},io=of;var nf=({r:e,g:t,b:r,alpha:o})=>{let n=Math.cbrt(.41222147079999993*e+.5363325363*t+.0514459929*r),a=Math.cbrt(.2119034981999999*e+.6806995450999999*t+.1073969566*r),f=Math.cbrt(.08830246189999998*e+.2817188376*t+.6299787005000002*r),i={mode:"oklab",l:.2104542553*n+.793617785*a-.0040720468*f,a:1.9779984951*n-2.428592205*a+.4505937099*f,b:.0259040371*n+.7827717662*a-.808675766*f};return o!==void 0&&(i.alpha=o),i},Ye=nf;var af=e=>{let t=Ye(J(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},ne=af;var ff=({l:e,a:t,b:r,alpha:o})=>{let n=Math.pow(e*.9999999984505198+.39633779217376786*t+.2158037580607588*r,3),a=Math.pow(e*1.0000000088817609-.10556134232365635*t-.06385417477170591*r,3),f=Math.pow(e*1.0000000546724108-.08948418209496575*t-1.2914855378640917*r,3),i={mode:"lrgb",r:4.076741661347994*n-3.307711590408193*a+.230969928729428*f,g:-1.2684380040921763*n+2.6097574006633715*a-.3413193963102197*f,b:-.004196086541837188*n-.7034186144594493*a+1.7076147009309444*f};return o!==void 0&&(i.alpha=o),i},U=ff;var lf=e=>j(U(e)),ae=lf;function bt(e){let o=1.170873786407767;return .5*(o*e-.206+Math.sqrt((o*e-.206)*(o*e-.206)+4*.03*o*e))}function He(e){let o=1.170873786407767;return(e*e+.206*e)/(o*(e+.03))}function pf(e,t){let r,o,n,a,f,i,l,m;-1.88170328*e-.80936493*t>1?(r=1.19086277,o=1.76576728,n=.59662641,a=.75515197,f=.56771245,i=4.0767416621,l=-3.3077115913,m=.2309699292):1.81444104*e-1.19445276*t>1?(r=.73956515,o=-.45954404,n=.08285427,a=.1254107,f=.14503204,i=-1.2684380046,l=2.6097574011,m=-.3413193965):(r=1.35733652,o=-.00915799,n=-1.1513021,a=-.50559606,f=.00692167,i=-.0041960863,l=-.7034186147,m=1.707614701);let s=r+o*e+n*t+a*e*e+f*e*t,d=.3963377774*e+.2158037573*t,h=-.1055613458*e-.0638541728*t,c=-.0894841775*e-1.291485548*t;{let b=1+s*d,y=1+s*h,v=1+s*c,z=b*b*b,S=y*y*y,X=v*v*v,N=3*d*b*b,$=3*h*y*y,C=3*c*v*v,q=6*d*d*b,he=6*h*h*y,ie=6*c*c*v,Z=i*z+l*S+m*X,Q=i*N+l*$+m*C,be=i*q+l*he+m*ie;s=s-Z*Q/(Q*Q-.5*Z*be)}return s}function lo(e,t){let r=pf(e,t),o=U({l:1,a:r*e,b:r*t}),n=Math.cbrt(1/Math.max(o.r,o.g,o.b)),a=n*r;return[n,a]}function uf(e,t,r,o,n,a=null){a||(a=lo(e,t));let f;if((r-n)*a[1]-(a[0]-n)*o<=0)f=a[1]*n/(o*a[0]+a[1]*(n-r));else{f=a[1]*(n-1)/(o*(a[0]-1)+a[1]*(n-r));{let i=r-n,l=o,m=.3963377774*e+.2158037573*t,s=-.1055613458*e-.0638541728*t,d=-.0894841775*e-1.291485548*t,h=i+l*m,c=i+l*s,b=i+l*d;{let y=n*(1-f)+f*r,v=f*o,z=y+v*m,S=y+v*s,X=y+v*d,N=z*z*z,$=S*S*S,C=X*X*X,q=3*h*z*z,he=3*c*S*S,ie=3*b*X*X,Z=6*h*h*z,Q=6*c*c*S,be=6*b*b*X,xe=4.0767416621*N-3.3077115913*$+.2309699292*C-1,D=4.0767416621*q-3.3077115913*he+.2309699292*ie,_t=4.0767416621*Z-3.3077115913*Q+.2309699292*be,Ht=D/(D*D-.5*xe*_t),Ue=-xe*Ht,Ke=-1.2684380046*N+2.6097574011*$-.3413193965*C-1,Pe=-1.2684380046*q+2.6097574011*he-.3413193965*ie,wr=-1.2684380046*Z+2.6097574011*Q-.3413193965*be,St=Pe/(Pe*Pe-.5*Ke*wr),Qe=-Ke*St,Io=-.0041960863*N-.7034186147*$+1.707614701*C-1,_r=-.0041960863*q-.7034186147*he+1.707614701*ie,nn=-.0041960863*Z-.7034186147*Q+1.707614701*be,Ao=_r/(_r*_r-.5*Io*nn),Hr=-Io*Ao;Ue=Ht>=0?Ue:1e6,Qe=St>=0?Qe:1e6,Hr=Ao>=0?Hr:1e6,f+=Math.min(Ue,Math.min(Qe,Hr))}}}return f}function xt(e,t,r=null){r||(r=lo(e,t));let o=r[0],n=r[1];return[n/o,n/(1-o)]}function or(e,t,r){let o=lo(t,r),n=uf(t,r,e,1,e,o),a=xt(t,r,o),f=.11516993+1/(7.4477897+4.1590124*r+t*(-2.19557347+1.75198401*r+t*(-2.13704948-10.02301043*r+t*(-4.24894561+5.38770819*r+4.69891013*t)))),i=.11239642+1/(1.6132032-.68124379*r+t*(.40370612+.90148123*r+t*(-.27087943+.6122399*r+t*(.00299215-.45399568*r-.14661872*t)))),l=n/Math.min(e*a[0],(1-e)*a[1]),m=e*f,s=(1-e)*i,d=.9*l*Math.sqrt(Math.sqrt(1/(1/(m*m*m*m)+1/(s*s*s*s))));return m=e*.4,s=(1-e)*.8,[Math.sqrt(1/(1/(m*m)+1/(s*s))),d,n]}function Ge(e){let t={mode:"okhsl",l:bt(e.l)};e.alpha!==void 0&&(t.alpha=e.alpha);let r=Math.sqrt(e.a*e.a+e.b*e.b);if(!r)return t.s=0,t;let[o,n,a]=or(e.l,e.a/r,e.b/r),f;if(r<n){let i=0,l=.8*o,m=1-l/n;f=(r-i)/(l+m*(r-i))*.8}else{let i=n,l=.2*n*n*1.25*1.25/o,m=1-l/(a-n);f=.8+.2*((r-i)/(l+m*(r-i)))}return f&&(t.s=f,t.h=k(Math.atan2(e.b,e.a)*180/Math.PI)),t}function Be(e){let t=He(e.l),r={mode:"oklab",l:t};if(e.alpha!==void 0&&(r.alpha=e.alpha),!e.s||e.l===1)return r.a=r.b=0,r;let o=Math.cos(e.h/180*Math.PI),n=Math.sin(e.h/180*Math.PI),[a,f,i]=or(t,o,n),l,m,s,d;e.s<.8?(l=1.25*e.s,m=0,s=.8*a,d=1-s/f):(l=5*(e.s-.8),m=f,s=.2*f*f*1.25*1.25/a,d=1-s/(i-f));let h=m+l*s/(1-d*l);return r.a=h*o,r.b=h*n,r}var mf={...lt,mode:"okhsl",channels:["h","s","l","alpha"],parse:["--okhsl"],serialize:"--okhsl",fromMode:{oklab:Ge,rgb:e=>Ge(ne(e))},toMode:{oklab:Be,rgb:e=>ae(Be(e))}},po=mf;function Ze(e){let t=Math.sqrt(e.a*e.a+e.b*e.b),r=e.l,o=t?e.a/t:1,n=t?e.b/t:1,[a,f]=xt(o,n),i=.5,l=1-i/a,m=f/(t+r*f),s=m*r,d=m*t,h=He(s),c=d*h/s,b=U({l:h,a:o*c,b:n*c}),y=Math.cbrt(1/Math.max(b.r,b.g,b.b,0));r=r/y,t=t/y*bt(r)/r,r=bt(r);let v={mode:"okhsv",s:t?(i+f)*d/(f*i+f*l*d):0,v:r?r/s:0};return v.s&&(v.h=k(Math.atan2(e.b,e.a)*180/Math.PI)),e.alpha!==void 0&&(v.alpha=e.alpha),v}function Fe(e){let t={mode:"oklab"};e.alpha!==void 0&&(t.alpha=e.alpha);let r=e.h||0,o=Math.cos(r/180*Math.PI),n=Math.sin(r/180*Math.PI),[a,f]=xt(o,n),i=.5,l=1-i/a,m=1-e.s*i/(i+f-f*l*e.s),s=e.s*f*i/(i+f-f*l*e.s),d=He(m),h=s*d/m,c=U({l:d,a:o*h,b:n*h}),b=Math.cbrt(1/Math.max(c.r,c.g,c.b,0)),y=He(e.v*m),v=s*y/m;return t.l=y*b,t.a=v*o*b,t.b=v*n*b,t}var sf={...pt,mode:"okhsv",channels:["h","s","v","alpha"],parse:["--okhsv"],serialize:"--okhsv",fromMode:{oklab:Ze,rgb:e=>Ze(ne(e))},toMode:{oklab:Fe,rgb:e=>ae(Fe(e))}},uo=sf;function df(e,t){if(!t||t[0]!=="oklab")return;let r={mode:"oklab"},[,o,n,a,f]=t;if(!(o.type===p.Hue||n.type===p.Hue||a.type===p.Hue))return o.type!==p.None&&(r.l=o.type===p.Number?o.value:o.value/100),n.type!==p.None&&(r.a=n.type===p.Number?n.value:n.value*.4/100),a.type!==p.None&&(r.b=a.type===p.Number?a.value:a.value*.4/100),f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var nr=df;var cf={...Le,mode:"oklab",toMode:{lrgb:U,rgb:ae},fromMode:{lrgb:Ye,rgb:ne},ranges:{l:[0,1],a:[-.4,.4],b:[-.4,.4]},parse:[nr],serialize:e=>`oklab(${e.l!==void 0?e.l:"none"} ${e.a!==void 0?e.a:"none"} ${e.b!==void 0?e.b:"none"}${e.alpha<1?` / ${e.alpha}`:""})`},mo=cf;function hf(e,t){if(!t||t[0]!=="oklch")return;let r={mode:"oklch"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Hue)return;r.l=o.type===p.Number?o.value:o.value/100}if(n.type!==p.None&&(r.c=Math.max(0,n.type===p.Number?n.value:n.value*.4/100)),a.type!==p.None){if(a.type===p.Percentage)return;r.h=a.value}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var ar=hf;var bf={...Re,mode:"oklch",toMode:{oklab:e=>A(e,"oklab"),rgb:e=>ae(A(e,"oklab"))},fromMode:{rgb:e=>I(ne(e),"oklch"),oklab:e=>I(e,"oklch")},parse:[ar],serialize:e=>`oklch(${e.l!==void 0?e.l:"none"} ${e.c!==void 0?e.c:"none"} ${e.h||0}${e.alpha<1?` / ${e.alpha}`:""})`,ranges:{l:[0,1],c:[0,.4],h:[0,360]}},so=bf;var xf=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a={mode:"xyz65",x:.486570948648216*t+.265667693169093*r+.1982172852343625*o,y:.2289745640697487*t+.6917385218365062*r+.079286914093745*o,z:0*t+.0451133818589026*r+1.043944368900976*o};return n!==void 0&&(a.alpha=n),a},gt=xf;var gf=({x:e,y:t,z:r,alpha:o})=>{let n=j({r:e*2.4934969119414263-t*.9313836179191242-.402710784450717*r,g:e*-.8294889695615749+t*1.7626640603183465+.0236246858419436*r,b:e*.0358458302437845-t*.0761723892680418+.9568845240076871*r},"p3");return o!==void 0&&(n.alpha=o),n},vt=gf;var vf={...G,mode:"p3",parse:["display-p3"],serialize:"display-p3",fromMode:{rgb:e=>vt(E(e)),xyz65:vt},toMode:{rgb:e=>Y(gt(e)),xyz65:gt}},co=vf;var ho=e=>{let t=Math.abs(e);return t>=.001953125?Math.sign(e)*Math.pow(t,.5555555555555556):16*e},Mf=({x:e,y:t,z:r,alpha:o})=>{let n={mode:"prophoto",r:ho(e*1.3457868816471585-t*.2555720873797946-.0511018649755453*r),g:ho(e*-.5446307051249019+t*1.5082477428451466+.0205274474364214*r),b:ho(e*0+t*0+1.2119675456389452*r)};return o!==void 0&&(n.alpha=o),n},Mt=Mf;var bo=e=>{let t=Math.abs(e);return t>=.03125?Math.sign(e)*Math.pow(t,1.8):e/16},yf=e=>{let t=bo(e.r),r=bo(e.g),o=bo(e.b),n={mode:"xyz50",x:.7977666449006423*t+.1351812974005331*r+.0313477341283922*o,y:.2880748288194013*t+.7118352342418731*r+899369387256e-16*o,z:0*t+0*r+.8251046025104602*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},yt=yf;var Tf={...G,mode:"prophoto",parse:["prophoto-rgb"],serialize:"prophoto-rgb",fromMode:{xyz50:Mt,rgb:e=>Mt(W(e))},toMode:{xyz50:yt,rgb:e=>F(yt(e))}},xo=Tf;var s0=1.09929682680944,zf=.018053968510807,go=e=>{let t=Math.abs(e);return t>zf?(Math.sign(e)||1)*(s0*Math.pow(t,.45)-(s0-1)):4.5*e},kf=({x:e,y:t,z:r,alpha:o})=>{let n={mode:"rec2020",r:go(e*1.7166511879712683-t*.3556707837763925-.2533662813736599*r),g:go(e*-.6666843518324893+t*1.6164812366349395+.0157685458139111*r),b:go(e*.0176398574453108-t*.0427706132578085+.9421031212354739*r)};return o!==void 0&&(n.alpha=o),n},Tt=kf;var d0=1.09929682680944,Lf=.018053968510807,vo=e=>{let t=Math.abs(e);return t<Lf*4.5?e/4.5:(Math.sign(e)||1)*Math.pow((t+d0-1)/d0,1/.45)},Rf=e=>{let t=vo(e.r),r=vo(e.g),o=vo(e.b),n={mode:"xyz65",x:.6369580483012911*t+.1446169035862083*r+.1688809751641721*o,y:.262700212011267*t+.6779980715188708*r+.059301716469862*o,z:0*t+.0280726930490874*r+1.0609850577107909*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},zt=Rf;var wf={...G,mode:"rec2020",fromMode:{xyz65:Tt,rgb:e=>Tt(E(e))},toMode:{xyz65:zt,rgb:e=>Y(zt(e))},parse:["rec2020"],serialize:"rec2020"},Mo=wf;var fe=.0037930732552754493,fr=Math.cbrt(fe);var yo=e=>Math.cbrt(e)-fr,_f=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a=yo(.3*t+.622*r+.078*o+fe),f=yo(.23*t+.692*r+.078*o+fe),i=yo(.2434226892454782*t+.2047674442449682*r+.5518098665095535*o+fe),l={mode:"xyb",x:(a-f)/2,y:(a+f)/2,b:i-(a+f)/2};return n!==void 0&&(l.alpha=n),l},ir=_f;var To=e=>Math.pow(e+fr,3),Hf=({x:e,y:t,b:r,alpha:o})=>{let n=To(e+t)-fe,a=To(t-e)-fe,f=To(r+t)-fe,i=j({r:11.031566904639861*n-9.866943908131562*a-.16462299650829934*f,g:-3.2541473810744237*n+4.418770377582723*a-.16462299650829934*f,b:-3.6588512867136815*n+2.7129230459360922*a+1.9459282407775895*f});return o!==void 0&&(i.alpha=o),i},lr=Hf;var Sf={mode:"xyb",channels:["x","y","b","alpha"],parse:["--xyb"],serialize:"--xyb",toMode:{rgb:lr},fromMode:{rgb:ir},ranges:{x:[-.0154,.0281],y:[0,.8453],b:[-.2778,.388]},interpolate:{x:u,y:u,b:u,alpha:{use:u,fixup:M}}},zo=Sf;var Xf={mode:"xyz50",parse:["xyz-d50"],serialize:"xyz-d50",toMode:{rgb:F,lab:de},fromMode:{rgb:W,lab:se},channels:["x","y","z","alpha"],ranges:{x:[0,.964],y:[0,.999],z:[0,.825]},interpolate:{x:u,y:u,z:u,alpha:{use:u,fixup:M}}},ko=Xf;var Pf=e=>{let{x:t,y:r,z:o,alpha:n}=e,a={mode:"xyz50",x:1.0479298208405488*t+.0229467933410191*r-.0501922295431356*o,y:.0296278156881593*t+.990434484573249*r-.0170738250293851*o,z:-.0092430581525912*t+.0150551448965779*r+.7518742899580008*o};return n!==void 0&&(a.alpha=n),a},pr=Pf;var Nf=e=>{let{x:t,y:r,z:o,alpha:n}=e,a={mode:"xyz65",x:.9554734527042182*t-.0230985368742614*r+.0632593086610217*o,y:-.0283697069632081*t+1.0099954580058226*r+.021041398966943*o,z:.0123140016883199*t-.0205076964334779*r+1.3303659366080753*o};return n!==void 0&&(a.alpha=n),a},ur=Nf;var $f={mode:"xyz65",toMode:{rgb:Y,xyz50:pr},fromMode:{rgb:E,xyz50:ur},ranges:{x:[0,.95],y:[0,1],z:[0,1.088]},channels:["x","y","z","alpha"],parse:["xyz","xyz-d65"],serialize:"xyz-d65",interpolate:{x:u,y:u,z:u,alpha:{use:u,fixup:M}}},Lo=$f;var Cf=({r:e,g:t,b:r,alpha:o})=>{let n={mode:"yiq",y:.29889531*e+.58662247*t+.11448223*r,i:.59597799*e-.2741761*t-.32180189*r,q:.21147017*e-.52261711*t+.31114694*r};return o!==void 0&&(n.alpha=o),n},mr=Cf;var Of=({y:e,i:t,q:r,alpha:o})=>{let n={mode:"rgb",r:e+.95608445*t+.6208885*r,g:e-.27137664*t-.6486059*r,b:e-1.10561724*t+1.70250126*r};return o!==void 0&&(n.alpha=o),n},sr=Of;var If={mode:"yiq",toMode:{rgb:sr},fromMode:{rgb:mr},channels:["y","i","q","alpha"],parse:["--yiq"],serialize:"--yiq",ranges:{i:[-.595,.595],q:[-.522,.522]},interpolate:{y:u,i:u,q:u,alpha:{use:u,fixup:M}}},Ro=If;var Af=(e,t)=>Math.round(e*(t=Math.pow(10,t)))/t,qf=(e=4)=>t=>typeof t=="number"?Af(t,e):t,dr=qf;var kt=dr(2),Lt=e=>Math.max(0,Math.min(1,e)),Se=e=>Math.round(Lt(e)*255),cr=e=>{if(e===void 0)return;let t=Se(e.r),r=Se(e.g),o=Se(e.b);return"#"+(1<<24|t<<16|r<<8|o).toString(16).slice(1)},wo=e=>{if(e===void 0)return;let t=Se(e.alpha!==void 0?e.alpha:1);return cr(e)+(256|t).toString(16).slice(1)},_o=e=>{if(e===void 0)return;let t=e.r!==void 0?Se(e.r):"none",r=e.g!==void 0?Se(e.g):"none",o=e.b!==void 0?Se(e.b):"none";return e.alpha===void 0||e.alpha===1?`rgb(${t}, ${r}, ${o})`:`rgba(${t}, ${r}, ${o}, ${kt(Lt(e.alpha))})`},Ho=e=>{if(e===void 0)return;let t=kt(e.h||0),r=e.s!==void 0?kt(Lt(e.s)*100)+"%":"none",o=e.l!==void 0?kt(Lt(e.l)*100)+"%":"none";return e.alpha===void 0||e.alpha===1?`hsl(${t}, ${r}, ${o})`:`hsla(${t}, ${r}, ${o}, ${kt(Lt(e.alpha))})`},c0=e=>{let t=P(e);if(!t)return;let r=L(t.mode);if(!r.serialize||typeof r.serialize=="string"){let o=`color(${r.serialize||`--${t.mode}`} `;return r.channels.forEach((n,a)=>{n!=="alpha"&&(o+=(a?" ":"")+(t[n]!==void 0?t[n]:"none"))}),t.alpha!==void 0&&t.alpha<1&&(o+=` / ${t.alpha}`),o+")"}if(typeof r.serialize=="function")return r.serialize(t)},h0=e=>cr(g("rgb")(e)),b0=e=>wo(g("rgb")(e)),x0=e=>_o(g("rgb")(e)),g0=e=>Ho(g("hsl")(e));var Df={normal:(e,t)=>t,multiply:(e,t)=>e*t,screen:(e,t)=>e+t-e*t,"hard-light":(e,t)=>t<.5?e*2*t:2*t*(1-e)-1,overlay:(e,t)=>e<.5?t*2*e:2*e*(1-t)-1,darken:(e,t)=>Math.min(e,t),lighten:(e,t)=>Math.max(e,t),"color-dodge":(e,t)=>e===0?0:t===1?1:Math.min(1,e/(1-t)),"color-burn":(e,t)=>e===1?1:t===0?0:1-Math.min(1,(1-e)/t),"soft-light":(e,t)=>t<.5?e-(1-2*t)*e*(1-e):e+(2*t-1)*((e<.25?((16*e-12)*e+4)*e:Math.sqrt(e))-e),difference:(e,t)=>Math.abs(e-t),exclusion:(e,t)=>e+t-2*e*t},Jf=(e,t="normal",r="rgb")=>{let o=typeof t=="function"?t:Df[t],n=g(r),a=L(r).channels;return e.map(i=>{let l=n(i);return l.alpha===void 0&&(l.alpha=1),l}).reduce((i,l)=>{if(i===void 0)return l;let m=l.alpha+i.alpha*(1-l.alpha);return a.reduce((s,d)=>(d!=="alpha"&&(m===0?s[d]=0:(s[d]=l.alpha*(1-i.alpha)*l[d]+l.alpha*i.alpha*o(i[d],l[d])+(1-l.alpha)*i.alpha*i[d],s[d]=Math.max(0,Math.min(1,s[d]/m)))),s),{mode:r,alpha:m})})},v0=Jf;var Ef=([e,t])=>e+Math.random()*(t-e),jf=e=>Object.keys(e).reduce((t,r)=>{let o=e[r];return t[r]=Array.isArray(o)?o:[o,o],t},{}),Yf=(e="rgb",t={})=>{let r=L(e),o=jf(t);return r.channels.reduce((n,a)=>((o.alpha||a!=="alpha")&&(n[a]=Ef(o[a]||r.ranges[a])),n),{mode:e})},M0=Yf;var ce=(e,t="rgb",r=!1)=>{let o=t?L(t).channels:null,n=t?g(t):P;return a=>{let f=n(a);if(!f)return;let i=(o||L(f.mode).channels).reduce((m,s)=>{let d=e(f[s],s,f,t);return d!==void 0&&!isNaN(d)&&(m[s]=d),m},{mode:f.mode});if(!r)return i;let l=P(a);return l&&l.mode!==i.mode?g(l.mode)(i):i}},hr=(e,t,r)=>t!=="alpha"?(e||0)*(r.alpha!==void 0?r.alpha:1):e,br=(e,t,r)=>t!=="alpha"&&r.alpha!==0?(e||0)/(r.alpha!==void 0?r.alpha:1):e,Rt=(e=1,t=0)=>(r,o)=>o!=="alpha"?r*e+t:r,y0=(e=1,t=1,r=0)=>(o,n)=>n!=="alpha"?e*Math.pow(o,t)+r:o;var Gf=e=>{e[0]===void 0&&(e[0]=0),e[e.length-1]===void 0&&(e[e.length-1]=1);let t=1,r,o,n,a;for(;t<e.length;){if(e[t]===void 0){for(o=t,n=e[t-1],r=t;e[r]===void 0;)r++;for(a=(e[r]-n)/(r-t+1);t<r;)e[t]=n+(t+1-o)*a,t++}else e[t]<e[t-1]&&(e[t]=e[t-1]);t++}return e},T0=Gf;var Bf=(e=.5)=>t=>e<=0?1:e>=1?0:Math.pow(t,Math.log(.5)/Math.log(e)),xr=Bf;var gr=e=>typeof e=="function",Xe=e=>e&&typeof e=="object",z0=e=>typeof e=="number",k0=(e,t="rgb",r,o)=>{let n=L(t),a=g(t),f=[],i=[],l={};e.forEach(h=>{Array.isArray(h)?(f.push(a(h[0])),i.push(h[1])):z0(h)||gr(h)?l[i.length]=h:(f.push(a(h)),i.push(void 0))}),T0(i);let m=n.channels.reduce((h,c)=>{let b;return Xe(r)&&Xe(r[c])&&r[c].fixup?b=r[c].fixup:Xe(n.interpolate[c])&&n.interpolate[c].fixup?b=n.interpolate[c].fixup:b=y=>y,h[c]=b(f.map(y=>y[c])),h},{});if(o){let h=f.map((c,b)=>n.channels.reduce((y,v)=>(y[v]=m[v][b],y),{mode:t}));m=n.channels.reduce((c,b)=>(c[b]=h.map(y=>{let v=o(y[b],b,y,t);return isNaN(v)?void 0:v}),c),{})}let s=n.channels.reduce((h,c)=>{let b;return gr(r)?b=r:Xe(r)&&gr(r[c])?b=r[c]:Xe(r)&&Xe(r[c])&&r[c].use?b=r[c].use:gr(n.interpolate[c])?b=n.interpolate[c]:Xe(n.interpolate[c])&&(b=n.interpolate[c].use),h[c]=b(m[c]),h},{}),d=f.length-1;return h=>{if(h=Math.min(Math.max(0,h),1),h<=i[0])return f[0];if(h>i[d])return f[d];let c=0;for(;i[c]<h;)c++;let b=i[c-1],y=i[c]-b,v=(h-b)/y,z=l[c]||l[0];z!==void 0&&(z0(z)&&(z=xr((z-b)/y)),v=z(v));let S=(c-1+v)/d;return n.channels.reduce((X,N)=>{let $=s[N](S);return $!==void 0&&(X[N]=$),X},{mode:t})}},L0=(e,t="rgb",r)=>k0(e,t,r),So=(e,t)=>(r,o="rgb",n)=>{let a=t?ce(t,o):void 0,f=k0(r,o,n,e);return a?i=>a(f(i)):f},R0=So(hr,br);var vr=(e,t)=>(e+t)%t,w0=(e,t,r,o,n)=>{let a=n*n,f=a*n;return((1-3*n+3*a-f)*e+(4-6*a+3*f)*t+(1+3*n+3*a-3*f)*r+f*o)/6},Mr=e=>t=>{let r=e.length-1,o=t>=1?r-1:Math.max(0,Math.floor(t*r));return w0(o>0?e[o-1]:2*e[o]-e[o+1],e[o],e[o+1],o<r-1?e[o+2]:2*e[o+1]-e[o],(t-o/r)*r)},yr=e=>t=>{let r=e.length-1,o=Math.floor(t*r);return w0(e[vr(o-1,e.length)],e[vr(o,e.length)],e[vr(o+1,e.length)],e[vr(o+2,e.length)],(t-o/r)*r)};var _0=e=>{let t,r=e.length-1,o=new Array(r),n=new Array(r),a=new Array(r);for(o[1]=1/4,n[1]=(6*e[1]-e[0])/4,t=2;t<r;++t)o[t]=1/(4-o[t-1]),n[t]=(6*e[t]-(t==r-1?e[r]:0)-n[t-1])*o[t];for(a[0]=e[0],a[r]=e[r],r-1>0&&(a[r-1]=n[r-1]),t=r-2;t>0;--t)a[t]=n[t]-o[t]*a[t+1];return a},H0=e=>Mr(_0(e)),S0=e=>yr(_0(e));var We=Math.sign,Xo=Math.min,B=Math.abs,Po=e=>{let t=e.length-1,r=[],o=[],n=[];for(let a=0;a<t;a++)r.push((e[a+1]-e[a])*t),o.push(a>0?.5*(e[a+1]-e[a-1])*t:void 0),n.push(a>0?(We(r[a-1])+We(r[a]))*Xo(B(r[a-1]),B(r[a]),.5*B(o[a])):void 0);return[r,o,n]},No=(e,t,r)=>{let o=e.length-1,n=o*o;return a=>{let f;a>=1?f=o-1:f=Math.max(0,Math.floor(a*o));let i=a-f/o,l=i*i,m=l*i;return(t[f]+t[f+1]-2*r[f])*n*m+(3*r[f]-2*t[f]-t[f+1])*o*l+t[f]*i+e[f]}},X0=e=>{if(e.length<3)return u(e);let t=e.length-1,[r,,o]=Po(e);return o[0]=r[0],o[t]=r[t-1],No(e,o,r)},P0=e=>{if(e.length<3)return u(e);let t=e.length-1,[r,o,n]=Po(e);return o[0]=(e[1]*2-e[0]*1.5-e[2]*.5)*t,o[t]=(e[t]*1.5-e[t-1]*2+e[t-2]*.5)*t,n[0]=o[0]*r[0]<=0?0:B(o[0])>2*B(r[0])?2*r[0]:o[0],n[t]=o[t]*r[t-1]<=0?0:B(o[t])>2*B(r[t-1])?2*r[t-1]:o[t],No(e,n,r)},N0=e=>{let t=e.length-1,[r,o,n]=Po(e);o[0]=.5*(e[1]-e[t])*t,o[t]=.5*(e[0]-e[t-1])*t;let a=(e[0]-e[t])*t,f=a;return n[0]=(We(a)+We(r[0]))*Xo(B(a),B(r[0]),.5*B(o[0])),n[t]=(We(r[t-1])+We(f))*Xo(B(r[t-1]),B(f),.5*B(o[t])),No(e,n,r)};var Zf=(e=1)=>e===1?t=>t:t=>Math.pow(t,e),Tr=Zf;var Ff=(e=2,t=1)=>{let r=Tr(t);if(e<2)return e<1?[]:[r(.5)];let o=[];for(let n=0;n<e;n++)o.push(r(n/(e-1)));return o},$0=Ff;var C0=g("rgb"),O0=e=>{let t={mode:e.mode,r:Math.max(0,Math.min(e.r,1)),g:Math.max(0,Math.min(e.g,1)),b:Math.max(0,Math.min(e.b,1))};return e.alpha!==void 0&&(t.alpha=e.alpha),t},I0=e=>O0(C0(e)),A0=e=>e!==void 0&&e.r>=0&&e.r<=1&&e.g>=0&&e.g<=1&&e.b>=0&&e.b<=1;function zr(e){return A0(C0(e))}function wt(e="rgb"){let{gamut:t}=L(e);if(!t)return o=>!0;let r=g(typeof t=="string"?t:e);return o=>A0(r(o))}function q0(e){return e=P(e),e===void 0||zr(e)?e:g(e.mode)(I0(e))}function kr(e="rgb"){let{gamut:t}=L(e);if(!t)return a=>P(a);let r=typeof t=="string"?t:e,o=g(r),n=wt(r);return a=>{let f=P(a);if(!f)return;let i=o(f);if(n(i))return f;let l=O0(i);return f.mode===l.mode?l:g(f.mode)(l)}}function D0(e,t="lch",r="rgb"){e=P(e);let o=r==="rgb"?zr:wt(r),n=r==="rgb"?I0:kr(r);if(e===void 0||o(e))return e;let a=g(e.mode);e=g(t)(e);let f={...e,c:0};if(!o(f))return a(n(f));let i=0,l=e.c,m=L(t).ranges.c,s=(m[1]-m[0])/Math.pow(2,13),d;for(;l-i>s;)f.c=i+(l-i)*.5,o(f)?(d=f.c,i=f.c):l=f.c;return a(o(f)?f:{...f,c:d})}function J0(e="rgb",t="oklch",r=pe("oklch"),o=.02){let n=g(e);if(!L(e).gamut)return d=>n(d);let a=wt(e),f=kr(e),i=g(t),{ranges:l}=L(t),m=n("white"),s=n("black");return d=>{if(d=P(d),d===void 0)return;let h={...i(d)};if(h.l>=l.l[1]){let z={...m};return d.alpha!==void 0&&(z.alpha=d.alpha),z}if(h.l<=l.l[0]){let z={...s};return d.alpha!==void 0&&(z.alpha=d.alpha),z}if(a(h))return n(h);let c=0,b=h.c,y=(l.c[1]-l.c[0])/4e3,v=f(h);for(;b-c>y;)h.c=(c+b)*.5,v=f(h),a(h)||r&&o>0&&r(h,v)<=o?c=h.c:b=h.c;return n(a(h)?h:v)}}var Wf=(e,t=pe(),r=o=>o)=>{let o=e.map((n,a)=>({color:r(n),i:a}));return(n,a=1,f=1/0)=>(isFinite(a)&&(a=Math.max(1,Math.min(a,o.length-1))),o.forEach(i=>{i.d=t(n,i.color)}),o.sort((i,l)=>i.d-l.d).slice(0,a).filter(i=>i.d<f).map(i=>e[i.i]))},E0=Wf;var $o=e=>Math.max(e,0),Co=e=>Math.max(Math.min(e,1),0),Uf=(e,t,r)=>e===void 0||t===void 0?void 0:e+r*(t-e),Kf=e=>{let t=1-Co(e);return[.393+.607*t,.769-.769*t,.189-.189*t,0,.349-.349*t,.686+.314*t,.168-.168*t,0,.272-.272*t,.534-.534*t,.131+.869*t,0,0,0,0,1]},Qf=e=>{let t=$o(e);return[.213+.787*t,.715-.715*t,.072-.072*t,0,.213-.213*t,.715+.285*t,.072-.072*t,0,.213-.213*t,.715-.715*t,.072+.928*t,0,0,0,0,1]},Vf=e=>{let t=1-Co(e);return[.2126+.7874*t,.7152-.7152*t,.0722-.0722*t,0,.2126-.2126*t,.7152+.2848*t,.0722-.0722*t,0,.2126-.2126*t,.7152-.7152*t,.0722+.9278*t,0,0,0,0,1]},ei=e=>{let t=Math.PI*e/180,r=Math.cos(t),o=Math.sin(t);return[.213+r*.787-o*.213,.715-r*.715-o*.715,.072-r*.072+o*.928,0,.213-r*.213+o*.143,.715+r*.285+o*.14,.072-r*.072-o*.283,0,.213-r*.213-o*.787,.715-r*.715+o*.715,.072+r*.928+o*.072,0,0,0,0,1]},Lr=(e,t,r=!1)=>{let o=g(t),n=L(t).channels;return a=>{let f=o(a);if(!f)return;let i={mode:t},l,m=n.length;for(let d=0;d<e.length;d++)l=n[Math.floor(d/m)],f[l]!==void 0&&(i[l]=(i[l]||0)+e[d]*(f[n[d%m]]||0));if(!r)return i;let s=P(a);return s&&i.mode!==s.mode?g(s.mode)(i):i}},j0=(e=1,t="rgb")=>{let r=$o(e);return ce(Rt(r),t,!0)},Y0=(e=1,t="rgb")=>{let r=$o(e);return ce(Rt(r,(1-r)/2),t,!0)},G0=(e=1,t="rgb")=>Lr(Kf(e),t,!0),B0=(e=1,t="rgb")=>Lr(Qf(e),t,!0),Z0=(e=1,t="rgb")=>Lr(Vf(e),t,!0),F0=(e=1,t="rgb")=>{let r=Co(e);return ce((o,n)=>n==="alpha"?o:Uf(r,1-r,o),t,!0)},W0=(e=0,t="rgb")=>Lr(ei(e),t,!0);var ti=g("rgb"),ri=[[1,0,-0,0,1,0,-0,-0,1],[.856167,.182038,-.038205,.029342,.955115,.015544,-.00288,-.001563,1.004443],[.734766,.334872,-.069637,.05184,.919198,.028963,-.004928,-.004209,1.009137],[.630323,.465641,-.095964,.069181,.890046,.040773,-.006308,-.007724,1.014032],[.539009,.579343,-.118352,.082546,.866121,.051332,-.007136,-.011959,1.019095],[.458064,.679578,-.137642,.092785,.846313,.060902,-.007494,-.016807,1.024301],[.38545,.769005,-.154455,.100526,.829802,.069673,-.007442,-.02219,1.029632],[.319627,.849633,-.169261,.106241,.815969,.07779,-.007025,-.028051,1.035076],[.259411,.923008,-.18242,.110296,.80434,.085364,-.006276,-.034346,1.040622],[.203876,.990338,-.194214,.112975,.794542,.092483,-.005222,-.041043,1.046265],[.152286,1.052583,-.204868,.114503,.786281,.099216,-.003882,-.048116,1.051998]],oi=[[1,0,-0,0,1,0,-0,-0,1],[.866435,.177704,-.044139,.049567,.939063,.01137,-.003453,.007233,.99622],[.760729,.319078,-.079807,.090568,.889315,.020117,-.006027,.013325,.992702],[.675425,.43385,-.109275,.125303,.847755,.026942,-.00795,.018572,.989378],[.605511,.52856,-.134071,.155318,.812366,.032316,-.009376,.023176,.9862],[.547494,.607765,-.155259,.181692,.781742,.036566,-.01041,.027275,.983136],[.498864,.674741,-.173604,.205199,.754872,.039929,-.011131,.030969,.980162],[.457771,.731899,-.18967,.226409,.731012,.042579,-.011595,.034333,.977261],[.422823,.781057,-.203881,.245752,.709602,.044646,-.011843,.037423,.974421],[.392952,.82361,-.216562,.263559,.69021,.046232,-.01191,.040281,.97163],[.367322,.860646,-.227968,.280085,.672501,.047413,-.01182,.04294,.968881]],ni=[[1,0,-0,0,1,0,-0,-0,1],[.92667,.092514,-.019184,.021191,.964503,.014306,.008437,.054813,.93675],[.89572,.13333,-.02905,.029997,.9454,.024603,.013027,.104707,.882266],[.905871,.127791,-.033662,.026856,.941251,.031893,.01341,.148296,.838294],[.948035,.08949,-.037526,.014364,.946792,.038844,.010853,.193991,.795156],[1.017277,.027029,-.044306,-.006113,.958479,.047634,.006379,.248708,.744913],[1.104996,-.046633,-.058363,-.032137,.971635,.060503,.001336,.317922,.680742],[1.193214,-.109812,-.083402,-.058496,.97941,.079086,-.002346,.403492,.598854],[1.257728,-.139648,-.118081,-.078003,.975409,.102594,-.003316,.501214,.502102],[1.278864,-.125333,-.153531,-.084748,.957674,.127074,-989e-6,.601151,.399838],[1.255528,-.076749,-.178779,-.078411,.930809,.147602,.004733,.691367,.3039]],Oo=(e,t)=>{let r=Math.max(0,Math.min(1,t)),o=Math.round(r/.1),n=Math.round(r%.1),a=e[o];if(n>0&&o<e.length-1){let f=e[o+1];a=a.map((i,l)=>V(a[l],f[l],n))}return f=>{let i=P(f);if(i===void 0)return;let{r:l,g:m,b:s}=ti(i),d={mode:"rgb",r:a[0]*l+a[1]*m+a[2]*s,g:a[3]*l+a[4]*m+a[5]*s,b:a[6]*l+a[7]*m+a[8]*s};return i.alpha!==void 0&&(d.alpha=i.alpha),g(i.mode)(d)}},U0=(e=1)=>Oo(ri,e),K0=(e=1)=>Oo(oi,e),Q0=(e=1)=>Oo(ni,e);var V0=e=>e*e*(3-2*e),en=e=>.5-Math.sin(Math.asin(1-2*e)/3);var ai=e=>e*e*e*(e*(e*6-15)+10),tn=ai;var fi=e=>(1-Math.cos(e*Math.PI))/2,rn=fi;function Rr(e){let t=g("lrgb")(e);return .2126*t.r+.7152*t.g+.0722*t.b}function on(e,t){let r=Rr(e),o=Rr(t);return(Math.max(r,o)+.05)/(Math.min(r,o)+.05)}var ii=T(Ar),li=T(Jr),pi=T(Gr),ui=T(Br),mi=T(Zr),si=T(lt),di=T(pt),ci=T(Fr),hi=T(Vr),bi=T(eo),xi=T(Le),gi=T(oo),vi=T(Re),Mi=T(no),yi=T(ao),Ti=T(fo),zi=T(io),ki=T(po),Li=T(uo),Ri=T(mo),wi=T(so),_i=T(co),Hi=T(xo),Si=T(Mo),Xi=T(G),Pi=T(zo),Ni=T(ko),$i=T(Lo),Ci=T(Ro);
