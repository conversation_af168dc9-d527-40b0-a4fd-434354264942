var u0=(e,t)=>{if(typeof e=="number"){if(t===3)return{mode:"rgb",r:(e>>8&15|e>>4&240)/255,g:(e>>4&15|e&240)/255,b:(e&15|e<<4&240)/255};if(t===4)return{mode:"rgb",r:(e>>12&15|e>>8&240)/255,g:(e>>8&15|e>>4&240)/255,b:(e>>4&15|e&240)/255,alpha:(e&15|e<<4&240)/255};if(t===6)return{mode:"rgb",r:(e>>16&255)/255,g:(e>>8&255)/255,b:(e&255)/255};if(t===8)return{mode:"rgb",r:(e>>24&255)/255,g:(e>>16&255)/255,b:(e>>8&255)/255,alpha:(e&255)/255}}},pt=u0;var m0={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},Ft=m0;var s0=e=>pt(Ft[e.toLowerCase()],6),Wt=s0;var d0=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,c0=e=>{let t;return(t=e.match(d0))?pt(parseInt(t[1],16),t[1].length):void 0},Ut=c0;var O="([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)",ii=`(?:${O}|none)`,ce=`${O}%`,li=`(?:${O}%|none)`,De=`(?:${O}%|${O})`,h0=`(?:${O}%|${O}|none)`,io=`(?:${O}(deg|grad|rad|turn)|${O})`,pi=`(?:${O}(deg|grad|rad|turn)|${O}|none)`,le="\\s*,\\s*";var ui=new RegExp("^"+h0+"$");var b0=new RegExp(`^rgba?\\(\\s*${O}${le}${O}${le}${O}\\s*(?:,\\s*${De}\\s*)?\\)$`),x0=new RegExp(`^rgba?\\(\\s*${ce}${le}${ce}${le}${ce}\\s*(?:,\\s*${De}\\s*)?\\)$`),g0=e=>{let t={mode:"rgb"},r;if(r=e.match(b0))r[1]!==void 0&&(t.r=r[1]/255),r[2]!==void 0&&(t.g=r[2]/255),r[3]!==void 0&&(t.b=r[3]/255);else if(r=e.match(x0))r[1]!==void 0&&(t.r=r[1]/100),r[2]!==void 0&&(t.g=r[2]/100),r[3]!==void 0&&(t.b=r[3]/100);else return;return r[4]!==void 0?t.alpha=r[4]/100:r[5]!==void 0&&(t.alpha=+r[5]),t},Kt=g0;var v0=(e,t)=>e===void 0?void 0:typeof e!="object"?Qt(e):e.mode!==void 0?e:t?{...e,mode:t}:void 0,P=v0;var M0=(e="rgb")=>t=>(t=P(t,e))!==void 0?t.mode===e?t:F[t.mode][e]?F[t.mode][e](t):e==="rgb"?F[t.mode].rgb(t):F.rgb[e](F[t.mode].rgb(t)):void 0,g=M0;var F={},lo={},he=[],ut={},y0=e=>e,T=e=>(F[e.mode]={...F[e.mode],...e.toMode},Object.keys(e.fromMode||{}).forEach(t=>{F[t]||(F[t]={}),F[t][e.mode]=e.fromMode[t]}),e.ranges||(e.ranges={}),e.difference||(e.difference={}),e.channels.forEach(t=>{if(e.ranges[t]===void 0&&(e.ranges[t]=[0,1]),!e.interpolate[t])throw new Error(`Missing interpolator for: ${t}`);typeof e.interpolate[t]=="function"&&(e.interpolate[t]={use:e.interpolate[t]}),e.interpolate[t].fixup||(e.interpolate[t].fixup=y0)}),lo[e.mode]=e,(e.parse||[]).forEach(t=>{po(t,e.mode)}),g(e.mode)),L=e=>lo[e],po=(e,t)=>{if(typeof e=="string"){if(!t)throw new Error("'mode' required when 'parser' is a string");ut[e]=t}else typeof e=="function"&&he.indexOf(e)<0&&he.push(e)},T0=e=>{if(typeof e=="string")delete ut[e];else if(typeof e=="function"){let t=he.indexOf(e);t>0&&he.splice(t,1)}};var Vt=/[^\x00-\x7F]|[a-zA-Z_]/,z0=/[^\x00-\x7F]|[-\w]/,p={Function:"function",Ident:"ident",Number:"number",Percentage:"percentage",ParenClose:")",None:"none",Hue:"hue",Alpha:"alpha"},x=0;function mt(e){let t=e[x],r=e[x+1];return t==="-"||t==="+"?/\d/.test(r)||r==="."&&/\d/.test(e[x+2]):t==="."?/\d/.test(r):/\d/.test(t)}function er(e){if(x>=e.length)return!1;let t=e[x];if(Vt.test(t))return!0;if(t==="-"){if(e.length-x<2)return!1;let r=e[x+1];return!!(r==="-"||Vt.test(r))}return!1}var k0={deg:1,rad:180/Math.PI,grad:9/10,turn:360};function Je(e){let t="";if((e[x]==="-"||e[x]==="+")&&(t+=e[x++]),t+=st(e),e[x]==="."&&/\d/.test(e[x+1])&&(t+=e[x++]+st(e)),(e[x]==="e"||e[x]==="E")&&((e[x+1]==="-"||e[x+1]==="+")&&/\d/.test(e[x+2])?t+=e[x++]+e[x++]+st(e):/\d/.test(e[x+1])&&(t+=e[x++]+st(e))),er(e)){let r=dt(e);return r==="deg"||r==="rad"||r==="turn"||r==="grad"?{type:p.Hue,value:t*k0[r]}:void 0}return e[x]==="%"?(x++,{type:p.Percentage,value:+t}):{type:p.Number,value:+t}}function st(e){let t="";for(;/\d/.test(e[x]);)t+=e[x++];return t}function dt(e){let t="";for(;x<e.length&&z0.test(e[x]);)t+=e[x++];return t}function L0(e){let t=dt(e);return e[x]==="("?(x++,{type:p.Function,value:t}):t==="none"?{type:p.None,value:void 0}:{type:p.Ident,value:t}}function R0(e=""){let t=e.trim(),r=[],o;for(x=0;x<t.length;){if(o=t[x++],o===`
`||o==="	"||o===" "){for(;x<t.length&&(t[x]===`
`||t[x]==="	"||t[x]===" ");)x++;continue}if(o===",")return;if(o===")"){r.push({type:p.ParenClose});continue}if(o==="+"){if(x--,mt(t)){r.push(Je(t));continue}return}if(o==="-"){if(x--,mt(t)){r.push(Je(t));continue}if(er(t)){r.push({type:p.Ident,value:dt(t)});continue}return}if(o==="."){if(x--,mt(t)){r.push(Je(t));continue}return}if(o==="/"){for(;x<t.length&&(t[x]===`
`||t[x]==="	"||t[x]===" ");)x++;let n;if(mt(t)&&(n=Je(t),n.type!==p.Hue)){r.push({type:p.Alpha,value:n});continue}if(er(t)&&dt(t)==="none"){r.push({type:p.Alpha,value:{type:p.None,value:void 0}});continue}return}if(/\d/.test(o)){x--,r.push(Je(t));continue}if(Vt.test(o)){x--,r.push(L0(t));continue}return}return r}function w0(e){e._i=0;let t=e[e._i++];if(!t||t.type!==p.Function||t.value!=="color"||(t=e[e._i++],t.type!==p.Ident))return;let r=ut[t.value];if(!r)return;let o={mode:r},n=uo(e,!1);if(!n)return;let a=L(r).channels;for(let f=0,i;f<a.length;f++)i=n[f],i.type!==p.None&&(o[a[f]]=i.type===p.Number?i.value:i.value/100);return o}function uo(e,t){let r=[],o;for(;e._i<e.length;){if(o=e[e._i++],o.type===p.None||o.type===p.Number||o.type===p.Alpha||o.type===p.Percentage||t&&o.type===p.Hue){r.push(o);continue}if(o.type===p.ParenClose){if(e._i<e.length)return;continue}return}if(!(r.length<3||r.length>4)){if(r.length===4){if(r[3].type!==p.Alpha)return;r[3]=r[3].value}return r.length===3&&r.push({type:p.None,value:void 0}),r.every(n=>n.type!==p.Alpha)?r:void 0}}function _0(e,t){e._i=0;let r=e[e._i++];if(!r||r.type!==p.Function)return;let o=uo(e,t);if(o)return o.unshift(r.value),o}var H0=e=>{if(typeof e!="string")return;let t=R0(e),r=t?_0(t,!0):void 0,o,n=0,a=he.length;for(;n<a;)if((o=he[n++](e,r))!==void 0)return o;return t?w0(t):void 0},Qt=H0;function S0(e,t){if(!t||t[0]!=="rgb"&&t[0]!=="rgba")return;let r={mode:"rgb"},[,o,n,a,f]=t;if(!(o.type===p.Hue||n.type===p.Hue||a.type===p.Hue))return o.type!==p.None&&(r.r=o.type===p.Number?o.value/255:o.value/100),n.type!==p.None&&(r.g=n.type===p.Number?n.value/255:n.value/100),a.type!==p.None&&(r.b=a.type===p.Number?a.value/255:a.value/100),f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var tr=S0;var X0=e=>e==="transparent"?{mode:"rgb",r:0,g:0,b:0,alpha:0}:void 0,rr=X0;var V=(e,t,r)=>e+r*(t-e),P0=(e,t,r)=>(r-e)/(t-e),or=(e,t,r,o,n,a)=>V(V(e,t,n),V(r,o,n),a),N0=(e,t,r,o,n,a,f,i,l,m,s)=>V(or(e,t,r,o,l,m),or(n,a,f,i,l,m),s);var $0=e=>{let t=[];for(let r=0;r<e.length-1;r++){let o=e[r],n=e[r+1];o===void 0&&n===void 0?t.push(void 0):o!==void 0&&n!==void 0?t.push([o,n]):t.push(o!==void 0?[o,o]:[n,n])}return t},nr=e=>t=>{let r=$0(t);return o=>{let n=o*r.length,a=o>=1?r.length-1:Math.max(Math.floor(n),0),f=r[a];return f===void 0?void 0:e(f[0],f[1],n-a)}};var u=nr(V);var y=e=>{let t=!1,r=e.map(o=>o!==void 0?(t=!0,o):1);return t?r:e};var C0={mode:"rgb",channels:["r","g","b","alpha"],parse:[tr,Ut,Kt,Wt,rr,"srgb"],serialize:"srgb",interpolate:{r:u,g:u,b:u,alpha:{use:u,fixup:y}},gamut:!0},Z=C0;var ar=e=>Math.pow(Math.abs(e),2.19921875)*Math.sign(e),O0=e=>{let t=ar(e.r),r=ar(e.g),o=ar(e.b),n={mode:"xyz65",x:.5766690429101305*t+.1855582379065463*r+.1882286462349947*o,y:.297344975250536*t+.6273635662554661*r+.0752914584939979*o,z:.0270313613864123*t+.0706888525358272*r+.9913375368376386*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},ct=O0;var fr=e=>Math.pow(Math.abs(e),.4547069271758437)*Math.sign(e),I0=({x:e,y:t,z:r,alpha:o})=>{let n={mode:"a98",r:fr(e*2.0415879038107465-t*.5650069742788597-.3447313507783297*r),g:fr(e*-.9692436362808798+t*1.8759675015077206+.0415550574071756*r),b:fr(e*.0134442806320312-t*.1183623922310184+1.0151749943912058*r)};return o!==void 0&&(n.alpha=o),n},ht=I0;var ir=e=>{let t=Math.abs(e);return t<=.04045?e/12.92:(Math.sign(e)||1)*Math.pow((t+.055)/1.055,2.4)},A0=({r:e,g:t,b:r,alpha:o})=>{let n={mode:"lrgb",r:ir(e),g:ir(t),b:ir(r)};return o!==void 0&&(n.alpha=o),n},J=A0;var q0=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a={mode:"xyz65",x:.4123907992659593*t+.357584339383878*r+.1804807884018343*o,y:.2126390058715102*t+.715168678767756*r+.0721923153607337*o,z:.0193308187155918*t+.119194779794626*r+.9505321522496607*o};return n!==void 0&&(a.alpha=n),a},E=q0;var lr=e=>{let t=Math.abs(e);return t>.0031308?(Math.sign(e)||1)*(1.055*Math.pow(t,.4166666666666667)-.055):e*12.92},D0=({r:e,g:t,b:r,alpha:o},n="rgb")=>{let a={mode:n,r:lr(e),g:lr(t),b:lr(r)};return o!==void 0&&(a.alpha=o),a},j=D0;var J0=({x:e,y:t,z:r,alpha:o})=>{let n=j({r:e*3.2409699419045226-t*1.537383177570094-.4986107602930034*r,g:e*-.9692436362808796+t*1.8759675015077204+.0415550574071756*r,b:e*.0556300796969936-t*.2039769588889765+1.0569715142428784*r});return o!==void 0&&(n.alpha=o),n},Y=J0;var E0={...Z,mode:"a98",parse:["a98-rgb"],serialize:"a98-rgb",fromMode:{rgb:e=>ht(E(e)),xyz65:ht},toMode:{rgb:e=>Y(ct(e)),xyz65:ct}},mo=E0;var j0=e=>(e=e%360)<0?e+360:e,k=j0;var bt=(e,t)=>e.map((r,o,n)=>{if(r===void 0)return r;let a=k(r);return o===0||e[o-1]===void 0?a:t(a-k(n[o-1]))}).reduce((r,o)=>!r.length||o===void 0||r[r.length-1]===void 0?(r.push(o),r):(r.push(o+r[r.length-1]),r),[]),S=e=>bt(e,t=>Math.abs(t)<=180?t:t-360*Math.sign(t)),Y0=e=>bt(e,t=>Math.abs(t)>=180||t===0?t:t-360*Math.sign(t)),G0=e=>bt(e,t=>t>=0?t:t+360),B0=e=>bt(e,t=>t<=0?t:t-360);var w=[-.14861,1.78277,-.29227,-.90649,1.97294,0],so=Math.PI/180,co=180/Math.PI;var ho=w[3]*w[4],bo=w[1]*w[4],xo=w[1]*w[2]-w[0]*w[3],Z0=({r:e,g:t,b:r,alpha:o})=>{let n=(xo*r+e*ho-t*bo)/(xo+ho-bo),a=r-n,f=(w[4]*(t-n)-w[2]*a)/w[3],i={mode:"cubehelix",l:n,s:n===0||n===1?void 0:Math.sqrt(a*a+f*f)/(w[4]*n*(1-n))};return i.s&&(i.h=Math.atan2(f,a)*co-120),o!==void 0&&(i.alpha=o),i},pr=Z0;var F0=({h:e,s:t,l:r,alpha:o})=>{let n={mode:"rgb"};e=(e===void 0?0:e+120)*so;let a=t===void 0?0:t*r*(1-r),f=Math.cos(e),i=Math.sin(e);return n.r=r+a*(w[0]*f+w[1]*i),n.g=r+a*(w[2]*f+w[3]*i),n.b=r+a*(w[4]*f+w[5]*i),o!==void 0&&(n.alpha=o),n},ur=F0;var ee=(e,t)=>{if(e.h===void 0||t.h===void 0||!e.s||!t.s)return 0;let r=k(e.h),o=k(t.h),n=Math.sin((o-r+360)/2*Math.PI/180);return 2*Math.sqrt(e.s*t.s)*n},mr=(e,t)=>{if(e.h===void 0||t.h===void 0)return 0;let r=k(e.h),o=k(t.h);return Math.abs(o-r)>180?r-(o-360*Math.sign(o-r)):o-r},te=(e,t)=>{if(e.h===void 0||t.h===void 0||!e.c||!t.c)return 0;let r=k(e.h),o=k(t.h),n=Math.sin((o-r+360)/2*Math.PI/180);return 2*Math.sqrt(e.c*t.c)*n},be=(e="rgb",t=[1,1,1,0])=>{let r=L(e),o=r.channels,n=r.difference,a=g(e);return(f,i)=>{let l=a(f),m=a(i);return Math.sqrt(o.reduce((s,d,h)=>{let c=n[d]?n[d](l,m):l[d]-m[d];return s+(t[h]||0)*Math.pow(isNaN(c)?0:c,2)},0))}},W0=()=>be("lab65"),U0=(e=1,t=.045,r=.015)=>{let o=g("lab65");return(n,a)=>{let f=o(n),i=o(a),l=f.l,m=f.a,s=f.b,d=Math.sqrt(m*m+s*s),h=i.l,c=i.a,b=i.b,M=Math.sqrt(c*c+b*b),v=Math.pow(l-h,2),z=Math.pow(d-M,2),_=Math.pow(m-c,2)+Math.pow(s-b,2)-z;return Math.sqrt(v/Math.pow(e,2)+z/Math.pow(1+t*d,2)+_/Math.pow(1+r*d,2))}},K0=(e=1,t=1,r=1)=>{let o=g("lab65");return(n,a)=>{let f=o(n),i=o(a),l=f.l,m=f.a,s=f.b,d=Math.sqrt(m*m+s*s),h=i.l,c=i.a,b=i.b,M=Math.sqrt(c*c+b*b),v=(d+M)/2,z=.5*(1-Math.sqrt(Math.pow(v,7)/(Math.pow(v,7)+Math.pow(25,7)))),_=m*(1+z),H=c*(1+z),N=Math.sqrt(_*_+s*s),$=Math.sqrt(H*H+b*b),C=Math.abs(_)+Math.abs(s)===0?0:Math.atan2(s,_);C+=(C<0)*2*Math.PI;let I=Math.abs(H)+Math.abs(b)===0?0:Math.atan2(b,H);I+=(I<0)*2*Math.PI;let me=h-l,ie=$-N,B=N*$===0?0:I-C;B-=(B>Math.PI)*2*Math.PI,B+=(B<-Math.PI)*2*Math.PI;let Q=2*Math.sqrt(N*$)*Math.sin(B/2),se=(l+h)/2,de=(N+$)/2,A;N*$===0?A=C+I:(A=(C+I)/2,A-=(Math.abs(C-I)>Math.PI)*Math.PI,A+=(A<0)*2*Math.PI);let ft=Math.pow(se-50,2),it=1-.17*Math.cos(A-Math.PI/6)+.24*Math.cos(2*A)+.32*Math.cos(3*A+Math.PI/30)-.2*Math.cos(4*A-63*Math.PI/180),Ie=1+.015*ft/Math.sqrt(20+ft),Ae=1+.045*de,ke=1+.015*de*it,Gt=30*Math.PI/180*Math.exp(-1*Math.pow((180/Math.PI*A-275)/25,2)),lt=2*Math.sqrt(Math.pow(de,7)/(Math.pow(de,7)+Math.pow(25,7))),qe=-1*Math.sin(2*Gt)*lt;return Math.sqrt(Math.pow(me/(e*Ie),2)+Math.pow(ie/(t*Ae),2)+Math.pow(Q/(r*ke),2)+qe*ie/(t*Ae)*Q/(r*ke))}},Q0=(e=1,t=1)=>{let r=g("lab65");return(o,n)=>{let a=r(o),f=a.l,i=a.a,l=a.b,m=Math.sqrt(i*i+l*l),s=Math.atan2(l,i);s=s+2*Math.PI*(s<0);let d=r(n),h=d.l,c=d.a,b=d.b,M=Math.sqrt(c*c+b*b),v=Math.pow(f-h,2),z=Math.pow(m-M,2),_=Math.pow(i-c,2)+Math.pow(l-b,2)-z,H=Math.sqrt(Math.pow(m,4)/(Math.pow(m,4)+1900)),N=s>=164/180*Math.PI&&s<=345/180*Math.PI?.56+Math.abs(.2*Math.cos(s+168/180*Math.PI)):.36+Math.abs(.4*Math.cos(s+35/180*Math.PI)),$=f<16?.511:.040975*f/(1+.01765*f),C=.0638*m/(1+.0131*m)+.638,I=C*(H*N+1-H);return Math.sqrt(v/Math.pow(e*$,2)+z/Math.pow(t*C,2)+_/Math.pow(I,2))}},V0=()=>{let e=g("lab65");return(t,r)=>{let o=e(t),n=e(r),a=o.l-n.l,f=o.a-n.a,i=o.b-n.b;return Math.abs(a)+Math.sqrt(f*f+i*i)}},en=()=>be("yiq",[.5053,.299,.1957]);var X=e=>{let t=e.reduce((r,o)=>{if(o!==void 0){let n=o*Math.PI/180;r.sin+=Math.sin(n),r.cos+=Math.cos(n)}return r},{sin:0,cos:0});return Math.atan2(t.sin,t.cos)*180/Math.PI},go=e=>{let t=e.filter(r=>r!==void 0);return t.length?t.reduce((r,o)=>r+o,0)/t.length:void 0},sr=e=>typeof e=="function";function tn(e,t="rgb",r){let o=L(t),n=e.map(g(t));return o.channels.reduce((a,f)=>{let i=n.map(l=>l[f]).filter(l=>l!==void 0);if(i.length){let l;sr(r)?l=r:r&&sr(r[f])?l=r[f]:o.average&&sr(o.average[f])?l=o.average[f]:l=go,a[f]=l(i,f)}return a},{mode:t})}var rn={mode:"cubehelix",channels:["h","s","l","alpha"],parse:["--cubehelix"],serialize:"--cubehelix",ranges:{h:[0,360],s:[0,4.614],l:[0,1]},fromMode:{rgb:pr},toMode:{rgb:ur},interpolate:{h:{use:u,fixup:S},s:u,l:u,alpha:{use:u,fixup:y}},difference:{h:ee},average:{h:X}},vo=rn;var on=({l:e,a:t,b:r,alpha:o},n="lch")=>{let a=Math.sqrt(t*t+r*r),f={mode:n,l:e,c:a};return a&&(f.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(f.alpha=o),f},q=on;var nn=({l:e,c:t,h:r,alpha:o},n="lab")=>{let a={mode:n,l:e,a:t?t*Math.cos(r/180*Math.PI):0,b:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(a.alpha=o),a},D=nn;var xt=Math.pow(29,3)/Math.pow(3,3),gt=Math.pow(6,3)/Math.pow(29,3);var R={X:.9642956764295677,Y:1,Z:.8251046025104602},pe={X:.3127/.329,Y:1,Z:(1-.3127-.329)/.329},wl=Math.pow(29,3)/Math.pow(3,3),_l=Math.pow(6,3)/Math.pow(29,3);var dr=e=>Math.pow(e,3)>gt?Math.pow(e,3):(116*e-16)/xt,an=({l:e,a:t,b:r,alpha:o})=>{let n=(e+16)/116,a=t/500+n,f=n-r/200,i={mode:"xyz65",x:dr(a)*pe.X,y:dr(n)*pe.Y,z:dr(f)*pe.Z};return o!==void 0&&(i.alpha=o),i},Ee=an;var fn=e=>Y(Ee(e)),re=fn;var cr=e=>e>gt?Math.cbrt(e):(xt*e+16)/116,ln=({x:e,y:t,z:r,alpha:o})=>{let n=cr(e/pe.X),a=cr(t/pe.Y),f=cr(r/pe.Z),i={mode:"lab65",l:116*a-16,a:500*(n-a),b:200*(a-f)};return o!==void 0&&(i.alpha=o),i},je=ln;var pn=e=>{let t=je(E(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},oe=pn;var xe=.14444444444444443*Math.PI,Le=Math.cos(xe),Re=Math.sin(xe),vt=100/Math.log(139/100);var un=({l:e,c:t,h:r,alpha:o})=>{let n={mode:"lab65",l:(Math.exp(e*1/vt)-1)/.0039};if(r===void 0)n.a=n.b=0;else{let a=(Math.exp(.0435*t*1*1)-1)/.075,f=a*Math.cos(r/180*Math.PI-xe),i=a*Math.sin(r/180*Math.PI-xe);n.a=f*Le-i/.83*Re,n.b=f*Re+i/.83*Le}return o!==void 0&&(n.alpha=o),n},we=un;var mn=({l:e,a:t,b:r,alpha:o})=>{let n=t*Le+r*Re,a=.83*(r*Le-t*Re),f=Math.sqrt(n*n+a*a),i={mode:"dlch",l:vt/1*Math.log(1+.0039*e),c:Math.log(1+.075*f)/(.0435*1*1)};return i.c&&(i.h=k((Math.atan2(a,n)+xe)/Math.PI*180)),o!==void 0&&(i.alpha=o),i},_e=mn;var Mo=e=>we(q(e,"dlch")),yo=e=>D(_e(e),"dlab"),sn={mode:"dlab",parse:["--din99o-lab"],serialize:"--din99o-lab",toMode:{lab65:Mo,rgb:e=>re(Mo(e))},fromMode:{lab65:yo,rgb:e=>yo(oe(e))},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-40.09,45.501],b:[-40.469,44.344]},interpolate:{l:u,a:u,b:u,alpha:{use:u,fixup:y}}},To=sn;var dn={mode:"dlch",parse:["--din99o-lch"],serialize:"--din99o-lch",toMode:{lab65:we,dlab:e=>D(e,"dlab"),rgb:e=>re(we(e))},fromMode:{lab65:_e,dlab:e=>q(e,"dlch"),rgb:e=>_e(oe(e))},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,51.484],h:[0,360]},interpolate:{l:u,c:u,h:{use:u,fixup:S},alpha:{use:u,fixup:y}},difference:{h:te},average:{h:X}},zo=dn;function Mt({h:e,s:t,i:r,alpha:o}){e=k(e);let n=Math.abs(e/60%2-1),a;switch(Math.floor(e/60)){case 0:a={r:r*(1+t*(3/(2-n)-1)),g:r*(1+t*(3*(1-n)/(2-n)-1)),b:r*(1-t)};break;case 1:a={r:r*(1+t*(3*(1-n)/(2-n)-1)),g:r*(1+t*(3/(2-n)-1)),b:r*(1-t)};break;case 2:a={r:r*(1-t),g:r*(1+t*(3/(2-n)-1)),b:r*(1+t*(3*(1-n)/(2-n)-1))};break;case 3:a={r:r*(1-t),g:r*(1+t*(3*(1-n)/(2-n)-1)),b:r*(1+t*(3/(2-n)-1))};break;case 4:a={r:r*(1+t*(3*(1-n)/(2-n)-1)),g:r*(1-t),b:r*(1+t*(3/(2-n)-1))};break;case 5:a={r:r*(1+t*(3/(2-n)-1)),g:r*(1-t),b:r*(1+t*(3*(1-n)/(2-n)-1))};break;default:a={r:r*(1-t),g:r*(1-t),b:r*(1-t)}}return a.mode="rgb",o!==void 0&&(a.alpha=o),a}function yt({r:e,g:t,b:r,alpha:o}){let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsi",s:e+t+r===0?0:1-3*a/(e+t+r),i:(e+t+r)/3};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var cn={mode:"hsi",toMode:{rgb:Mt},parse:["--hsi"],serialize:"--hsi",fromMode:{rgb:yt},channels:["h","s","i","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:u,fixup:S},s:u,i:u,alpha:{use:u,fixup:y}},difference:{h:ee},average:{h:X}},ko=cn;function Tt({h:e,s:t,l:r,alpha:o}){e=k(e);let n=r+t*(r<.5?r:1-r),a=n-(n-r)*2*Math.abs(e/60%2-1),f;switch(Math.floor(e/60)){case 0:f={r:n,g:a,b:2*r-n};break;case 1:f={r:a,g:n,b:2*r-n};break;case 2:f={r:2*r-n,g:n,b:a};break;case 3:f={r:2*r-n,g:a,b:n};break;case 4:f={r:a,g:2*r-n,b:n};break;case 5:f={r:n,g:2*r-n,b:a};break;default:f={r:2*r-n,g:2*r-n,b:2*r-n}}return f.mode="rgb",o!==void 0&&(f.alpha=o),f}function zt({r:e,g:t,b:r,alpha:o}){let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsl",s:n===a?0:(n-a)/(1-Math.abs(n+a-1)),l:.5*(n+a)};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var hn=(e,t)=>{switch(t){case"deg":return+e;case"rad":return e/Math.PI*180;case"grad":return e/10*9;case"turn":return e*360}},Lo=hn;var bn=new RegExp(`^hsla?\\(\\s*${io}${le}${ce}${le}${ce}\\s*(?:,\\s*${De}\\s*)?\\)$`),xn=e=>{let t=e.match(bn);if(!t)return;let r={mode:"hsl"};return t[3]!==void 0?r.h=+t[3]:t[1]!==void 0&&t[2]!==void 0&&(r.h=Lo(t[1],t[2])),t[4]!==void 0&&(r.s=Math.min(Math.max(0,t[4]/100),1)),t[5]!==void 0&&(r.l=Math.min(Math.max(0,t[5]/100),1)),t[6]!==void 0?r.alpha=t[6]/100:t[7]!==void 0&&(r.alpha=+t[7]),r},br=xn;function gn(e,t){if(!t||t[0]!=="hsl"&&t[0]!=="hsla")return;let r={mode:"hsl"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Percentage)return;r.h=o.value}if(n.type!==p.None){if(n.type===p.Hue)return;r.s=n.type===p.Number?n.value:n.value/100}if(a.type!==p.None){if(a.type===p.Hue)return;r.l=a.type===p.Number?a.value:a.value/100}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var xr=gn;var vn={mode:"hsl",toMode:{rgb:Tt},fromMode:{rgb:zt},channels:["h","s","l","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[xr,br],serialize:e=>`hsl(${e.h||0} ${e.s!==void 0?e.s*100+"%":"none"} ${e.l!==void 0?e.l*100+"%":"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:u,fixup:S},s:u,l:u,alpha:{use:u,fixup:y}},difference:{h:ee},average:{h:X}},kt=vn;function He({h:e,s:t,v:r,alpha:o}){e=k(e);let n=Math.abs(e/60%2-1),a;switch(Math.floor(e/60)){case 0:a={r,g:r*(1-t*n),b:r*(1-t)};break;case 1:a={r:r*(1-t*n),g:r,b:r*(1-t)};break;case 2:a={r:r*(1-t),g:r,b:r*(1-t*n)};break;case 3:a={r:r*(1-t),g:r*(1-t*n),b:r};break;case 4:a={r:r*(1-t*n),g:r*(1-t),b:r};break;case 5:a={r,g:r*(1-t),b:r*(1-t*n)};break;default:a={r:r*(1-t),g:r*(1-t),b:r*(1-t)}}return a.mode="rgb",o!==void 0&&(a.alpha=o),a}function Se({r:e,g:t,b:r,alpha:o}){let n=Math.max(e,t,r),a=Math.min(e,t,r),f={mode:"hsv",s:n===0?0:1-a/n,v:n};return n-a!==0&&(f.h=(n===e?(t-r)/(n-a)+(t<r)*6:n===t?(r-e)/(n-a)+2:(e-t)/(n-a)+4)*60),o!==void 0&&(f.alpha=o),f}var Mn={mode:"hsv",toMode:{rgb:He},parse:["--hsv"],serialize:"--hsv",fromMode:{rgb:Se},channels:["h","s","v","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:u,fixup:S},s:u,v:u,alpha:{use:u,fixup:y}},difference:{h:ee},average:{h:X}},Lt=Mn;function Rt({h:e,w:t,b:r,alpha:o}){if(t+r>1){let n=t+r;t/=n,r/=n}return He({h:e,s:r===1?1:1-t/(1-r),v:1-r,alpha:o})}function wt(e){let t=Se(e);if(t===void 0)return;let r={mode:"hwb",w:(1-t.s)*t.v,b:1-t.v};return t.h!==void 0&&(r.h=t.h),t.alpha!==void 0&&(r.alpha=t.alpha),r}function yn(e,t){if(!t||t[0]!=="hwb")return;let r={mode:"hwb"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Percentage)return;r.h=o.value}if(n.type!==p.None){if(n.type===p.Hue)return;r.w=n.type===p.Number?n.value:n.value/100}if(a.type!==p.None){if(a.type===p.Hue)return;r.b=a.type===p.Number?a.value:a.value/100}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var gr=yn;var Tn={mode:"hwb",toMode:{rgb:Rt},fromMode:{rgb:wt},channels:["h","w","b","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[gr],serialize:e=>`hwb(${e.h||0} ${e.w*100}% ${e.b*100}%${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:u,fixup:S},w:u,b:u,alpha:{use:u,fixup:y}},difference:{h:mr},average:{h:X}},Ro=Tn;var zn=.1593017578125,kn=134.03437499999998,Ln=.8359375,Rn=18.8515625,wn=18.6875,_n=16295499532821565e-27,vr=e=>{let t=Math.pow(e/1e4,zn);return Math.pow((Ln+Rn*t)/(1+wn*t),kn)||0},Mr=e=>Math.max(e*203,0),Hn=({x:e,y:t,z:r,alpha:o})=>{e=Mr(e),t=Mr(t),r=Mr(r);let n=1.15*e-.15*r,a=.66*t+.34*e,f=vr(.41478972*n+.579999*a+.014648*r),i=vr(-.20151*n+1.120649*a+.0531008*r),l=vr(-.0166008*n+.2648*a+.6684799*r),m=(f+i)/2,s={mode:"jab",j:.44*m/(1-.56*m)-_n,a:3.524*f-4.066708*i+.542708*l,b:.199076*f+1.096799*i-1.295875*l};return o!==void 0&&(s.alpha=o),s},Ge=Hn;var Sn=.1593017578125,Xn=134.03437499999998,Pn=.8359375,Nn=18.8515625,$n=18.6875,wo=16295499532821565e-27,yr=e=>{let t=Math.pow(e,1/Xn);return 1e4*Math.pow((Pn-t)/($n*t-Nn),1/Sn)||0},Tr=e=>e/203,Cn=({j:e,a:t,b:r,alpha:o})=>{let n=(e+wo)/(.44+.56*(e+wo)),a=yr(n+.13860504*t+.058047316*r),f=yr(n-.13860504*t-.058047316*r),i=yr(n-.096019242*t-.8118919*r),l={mode:"xyz65",x:Tr(1.661373024652174*a-.914523081304348*f+.23136208173913045*i),y:Tr(-.3250758611844533*a+1.571847026732543*f-.21825383453227928*i),z:Tr(-.090982811*a-.31272829*f+1.5227666*i)};return o!==void 0&&(l.alpha=o),l},Be=Cn;var On=e=>{let t=Ge(E(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},Ze=On;var In=e=>Y(Be(e)),Fe=In;var An={mode:"jab",channels:["j","a","b","alpha"],parse:["--jzazbz"],serialize:"--jzazbz",fromMode:{rgb:Ze,xyz65:Ge},toMode:{rgb:Fe,xyz65:Be},ranges:{j:[0,.222],a:[-.109,.129],b:[-.185,.134]},interpolate:{j:u,a:u,b:u,alpha:{use:u,fixup:y}}},_o=An;var qn=({j:e,a:t,b:r,alpha:o})=>{let n=Math.sqrt(t*t+r*r),a={mode:"jch",j:e,c:n};return n&&(a.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(a.alpha=o),a},_t=qn;var Dn=({j:e,c:t,h:r,alpha:o})=>{let n={mode:"jab",j:e,a:t?t*Math.cos(r/180*Math.PI):0,b:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(n.alpha=o),n},Ht=Dn;var Jn={mode:"jch",parse:["--jzczhz"],serialize:"--jzczhz",toMode:{jab:Ht,rgb:e=>Fe(Ht(e))},fromMode:{rgb:e=>_t(Ze(e)),jab:_t},channels:["j","c","h","alpha"],ranges:{j:[0,.221],c:[0,.19],h:[0,360]},interpolate:{h:{use:u,fixup:S},c:u,j:u,alpha:{use:u,fixup:y}},difference:{h:te},average:{h:X}},Ho=Jn;var ue=Math.pow(29,3)/Math.pow(3,3),Xe=Math.pow(6,3)/Math.pow(29,3);var zr=e=>Math.pow(e,3)>Xe?Math.pow(e,3):(116*e-16)/ue,En=({l:e,a:t,b:r,alpha:o})=>{let n=(e+16)/116,a=t/500+n,f=n-r/200,i={mode:"xyz50",x:zr(a)*R.X,y:zr(n)*R.Y,z:zr(f)*R.Z};return o!==void 0&&(i.alpha=o),i},ge=En;var jn=({x:e,y:t,z:r,alpha:o})=>{let n=j({r:e*3.1341359569958707-t*1.6173863321612538-.4906619460083532*r,g:e*-.978795502912089+t*1.916254567259524+.03344273116131949*r,b:e*.07195537988411677-t*.2289768264158322+1.405386058324125*r});return o!==void 0&&(n.alpha=o),n},W=jn;var Yn=e=>W(ge(e)),We=Yn;var Gn=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a={mode:"xyz50",x:.436065742824811*t+.3851514688337912*r+.14307845442264197*o,y:.22249319175623702*t+.7168870538238823*r+.06061979053616537*o,z:.013923904500943465*t+.09708128566574634*r+.7140993584005155*o};return n!==void 0&&(a.alpha=n),a},U=Gn;var kr=e=>e>Xe?Math.cbrt(e):(ue*e+16)/116,Bn=({x:e,y:t,z:r,alpha:o})=>{let n=kr(e/R.X),a=kr(t/R.Y),f=kr(r/R.Z),i={mode:"lab",l:116*a-16,a:500*(n-a),b:200*(a-f)};return o!==void 0&&(i.alpha=o),i},ve=Bn;var Zn=e=>{let t=ve(U(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},Ue=Zn;function Fn(e,t){if(!t||t[0]!=="lab")return;let r={mode:"lab"},[,o,n,a,f]=t;if(!(o.type===p.Hue||n.type===p.Hue||a.type===p.Hue))return o.type!==p.None&&(r.l=o.value),n.type!==p.None&&(r.a=n.type===p.Number?n.value:n.value*125/100),a.type!==p.None&&(r.b=a.type===p.Number?a.value:a.value*125/100),f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var Lr=Fn;var Wn={mode:"lab",toMode:{xyz50:ge,rgb:We},fromMode:{xyz50:ve,rgb:Ue},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-100,100],b:[-100,100]},parse:[Lr],serialize:e=>`lab(${e.l!==void 0?e.l:"none"} ${e.a!==void 0?e.a:"none"} ${e.b!==void 0?e.b:"none"}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{l:u,a:u,b:u,alpha:{use:u,fixup:y}}},Pe=Wn;var Un={...Pe,mode:"lab65",parse:["--lab-d65"],serialize:"--lab-d65",toMode:{xyz65:Ee,rgb:re},fromMode:{xyz65:je,rgb:oe},ranges:{l:[0,100],a:[-86.182,98.234],b:[-107.86,94.477]}},So=Un;function Kn(e,t){if(!t||t[0]!=="lch")return;let r={mode:"lch"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Hue)return;r.l=o.value}if(n.type!==p.None&&(r.c=Math.max(0,n.type===p.Number?n.value:n.value*150/100)),a.type!==p.None){if(a.type===p.Percentage)return;r.h=a.value}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var Rr=Kn;var Qn={mode:"lch",toMode:{lab:D,rgb:e=>We(D(e))},fromMode:{rgb:e=>q(Ue(e)),lab:q},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,150],h:[0,360]},parse:[Rr],serialize:e=>`lch(${e.l!==void 0?e.l:"none"} ${e.c!==void 0?e.c:"none"} ${e.h||0}${e.alpha<1?` / ${e.alpha}`:""})`,interpolate:{h:{use:u,fixup:S},c:u,l:u,alpha:{use:u,fixup:y}},difference:{h:te},average:{h:X}},Ne=Qn;var Vn={...Ne,mode:"lch65",parse:["--lch-d65"],serialize:"--lch-d65",toMode:{lab65:e=>D(e,"lab65"),rgb:e=>re(D(e,"lab65"))},fromMode:{rgb:e=>q(oe(e),"lch65"),lab65:e=>q(e,"lch65")},ranges:{l:[0,100],c:[0,133.807],h:[0,360]}},Xo=Vn;var ea=({l:e,u:t,v:r,alpha:o})=>{let n=Math.sqrt(t*t+r*r),a={mode:"lchuv",l:e,c:n};return n&&(a.h=k(Math.atan2(r,t)*180/Math.PI)),o!==void 0&&(a.alpha=o),a},St=ea;var ta=({l:e,c:t,h:r,alpha:o})=>{let n={mode:"luv",l:e,u:t?t*Math.cos(r/180*Math.PI):0,v:t?t*Math.sin(r/180*Math.PI):0};return o!==void 0&&(n.alpha=o),n},Xt=ta;var Po=(e,t,r)=>4*e/(e+15*t+3*r),No=(e,t,r)=>9*t/(e+15*t+3*r),ra=Po(R.X,R.Y,R.Z),oa=No(R.X,R.Y,R.Z),na=e=>e<=Xe?ue*e:116*Math.cbrt(e)-16,aa=({x:e,y:t,z:r,alpha:o})=>{let n=na(t/R.Y),a=Po(e,t,r),f=No(e,t,r);!isFinite(a)||!isFinite(f)?n=a=f=0:(a=13*n*(a-ra),f=13*n*(f-oa));let i={mode:"luv",l:n,u:a,v:f};return o!==void 0&&(i.alpha=o),i},$e=aa;var fa=(e,t,r)=>4*e/(e+15*t+3*r),ia=(e,t,r)=>9*t/(e+15*t+3*r),la=fa(R.X,R.Y,R.Z),pa=ia(R.X,R.Y,R.Z),ua=({l:e,u:t,v:r,alpha:o})=>{let n=t/(13*e)+la,a=r/(13*e)+pa,f=R.Y*(e<=8?e/ue:Math.pow((e+16)/116,3)),i=f*(9*n)/(4*a),l=f*(12-3*n-20*a)/(4*a),m={mode:"xyz50",x:i,y:f,z:l};return o!==void 0&&(m.alpha=o),m},Ce=ua;var ma=e=>St($e(U(e))),sa=e=>W(Ce(Xt(e))),da={mode:"lchuv",toMode:{luv:Xt,rgb:sa},fromMode:{rgb:ma,luv:St},channels:["l","c","h","alpha"],parse:["--lchuv"],serialize:"--lchuv",ranges:{l:[0,100],c:[0,176.956],h:[0,360]},interpolate:{h:{use:u,fixup:S},c:u,l:u,alpha:{use:u,fixup:y}},difference:{h:te},average:{h:X}},$o=da;var ca={...Z,mode:"lrgb",toMode:{rgb:j},fromMode:{rgb:J},parse:["srgb-linear"],serialize:"srgb-linear"},Co=ca;var ha={mode:"luv",toMode:{xyz50:Ce,rgb:e=>W(Ce(e))},fromMode:{xyz50:$e,rgb:e=>$e(U(e))},channels:["l","u","v","alpha"],parse:["--luv"],serialize:"--luv",ranges:{l:[0,100],u:[-84.936,175.042],v:[-125.882,87.243]},interpolate:{l:u,u,v:u,alpha:{use:u,fixup:y}}},Oo=ha;var ba=({r:e,g:t,b:r,alpha:o})=>{let n=Math.cbrt(.41222147079999993*e+.5363325363*t+.0514459929*r),a=Math.cbrt(.2119034981999999*e+.6806995450999999*t+.1073969566*r),f=Math.cbrt(.08830246189999998*e+.2817188376*t+.6299787005000002*r),i={mode:"oklab",l:.2104542553*n+.793617785*a-.0040720468*f,a:1.9779984951*n-2.428592205*a+.4505937099*f,b:.0259040371*n+.7827717662*a-.808675766*f};return o!==void 0&&(i.alpha=o),i},Ke=ba;var xa=e=>{let t=Ke(J(e));return e.r===e.b&&e.b===e.g&&(t.a=t.b=0),t},ne=xa;var ga=({l:e,a:t,b:r,alpha:o})=>{let n=Math.pow(e*.9999999984505198+.39633779217376786*t+.2158037580607588*r,3),a=Math.pow(e*1.0000000088817609-.10556134232365635*t-.06385417477170591*r,3),f=Math.pow(e*1.0000000546724108-.08948418209496575*t-1.2914855378640917*r,3),i={mode:"lrgb",r:4.076741661347994*n-3.307711590408193*a+.230969928729428*f,g:-1.2684380040921763*n+2.6097574006633715*a-.3413193963102197*f,b:-.004196086541837188*n-.7034186144594493*a+1.7076147009309444*f};return o!==void 0&&(i.alpha=o),i},K=ga;var va=e=>j(K(e)),ae=va;function Qe(e){let o=1.170873786407767;return .5*(o*e-.206+Math.sqrt((o*e-.206)*(o*e-.206)+4*.03*o*e))}function Me(e){let o=1.170873786407767;return(e*e+.206*e)/(o*(e+.03))}function Ma(e,t){let r,o,n,a,f,i,l,m;-1.88170328*e-.80936493*t>1?(r=1.19086277,o=1.76576728,n=.59662641,a=.75515197,f=.56771245,i=4.0767416621,l=-3.3077115913,m=.2309699292):1.81444104*e-1.19445276*t>1?(r=.73956515,o=-.45954404,n=.08285427,a=.1254107,f=.14503204,i=-1.2684380046,l=2.6097574011,m=-.3413193965):(r=1.35733652,o=-.00915799,n=-1.1513021,a=-.50559606,f=.00692167,i=-.0041960863,l=-.7034186147,m=1.707614701);let s=r+o*e+n*t+a*e*e+f*e*t,d=.3963377774*e+.2158037573*t,h=-.1055613458*e-.0638541728*t,c=-.0894841775*e-1.291485548*t;{let b=1+s*d,M=1+s*h,v=1+s*c,z=b*b*b,_=M*M*M,H=v*v*v,N=3*d*b*b,$=3*h*M*M,C=3*c*v*v,I=6*d*d*b,me=6*h*h*M,ie=6*c*c*v,B=i*z+l*_+m*H,Q=i*N+l*$+m*C,se=i*I+l*me+m*ie;s=s-B*Q/(Q*Q-.5*B*se)}return s}function wr(e,t){let r=Ma(e,t),o=K({l:1,a:r*e,b:r*t}),n=Math.cbrt(1/Math.max(o.r,o.g,o.b)),a=n*r;return[n,a]}function ya(e,t,r,o,n,a=null){a||(a=wr(e,t));let f;if((r-n)*a[1]-(a[0]-n)*o<=0)f=a[1]*n/(o*a[0]+a[1]*(n-r));else{f=a[1]*(n-1)/(o*(a[0]-1)+a[1]*(n-r));{let i=r-n,l=o,m=.3963377774*e+.2158037573*t,s=-.1055613458*e-.0638541728*t,d=-.0894841775*e-1.291485548*t,h=i+l*m,c=i+l*s,b=i+l*d;{let M=n*(1-f)+f*r,v=f*o,z=M+v*m,_=M+v*s,H=M+v*d,N=z*z*z,$=_*_*_,C=H*H*H,I=3*h*z*z,me=3*c*_*_,ie=3*b*H*H,B=6*h*h*z,Q=6*c*c*_,se=6*b*b*H,de=4.0767416621*N-3.3077115913*$+.2309699292*C-1,A=4.0767416621*I-3.3077115913*me+.2309699292*ie,ft=4.0767416621*B-3.3077115913*Q+.2309699292*se,it=A/(A*A-.5*de*ft),Ie=-de*it,Ae=-1.2684380046*N+2.6097574011*$-.3413193965*C-1,ke=-1.2684380046*I+2.6097574011*me-.3413193965*ie,Gt=-1.2684380046*B+2.6097574011*Q-.3413193965*se,lt=ke/(ke*ke-.5*Ae*Gt),qe=-Ae*lt,ao=-.0041960863*N-.7034186147*$+1.707614701*C-1,Bt=-.0041960863*I-.7034186147*me+1.707614701*ie,p0=-.0041960863*B-.7034186147*Q+1.707614701*se,fo=Bt/(Bt*Bt-.5*ao*p0),Zt=-ao*fo;Ie=it>=0?Ie:1e6,qe=lt>=0?qe:1e6,Zt=fo>=0?Zt:1e6,f+=Math.min(Ie,Math.min(qe,Zt))}}}return f}function Ve(e,t,r=null){r||(r=wr(e,t));let o=r[0],n=r[1];return[n/o,n/(1-o)]}function Pt(e,t,r){let o=wr(t,r),n=ya(t,r,e,1,e,o),a=Ve(t,r,o),f=.11516993+1/(7.4477897+4.1590124*r+t*(-2.19557347+1.75198401*r+t*(-2.13704948-10.02301043*r+t*(-4.24894561+5.38770819*r+4.69891013*t)))),i=.11239642+1/(1.6132032-.68124379*r+t*(.40370612+.90148123*r+t*(-.27087943+.6122399*r+t*(.00299215-.45399568*r-.14661872*t)))),l=n/Math.min(e*a[0],(1-e)*a[1]),m=e*f,s=(1-e)*i,d=.9*l*Math.sqrt(Math.sqrt(1/(1/(m*m*m*m)+1/(s*s*s*s))));return m=e*.4,s=(1-e)*.8,[Math.sqrt(1/(1/(m*m)+1/(s*s))),d,n]}function et(e){let t={mode:"okhsl",l:Qe(e.l)};e.alpha!==void 0&&(t.alpha=e.alpha);let r=Math.sqrt(e.a*e.a+e.b*e.b);if(!r)return t.s=0,t;let[o,n,a]=Pt(e.l,e.a/r,e.b/r),f;if(r<n){let i=0,l=.8*o,m=1-l/n;f=(r-i)/(l+m*(r-i))*.8}else{let i=n,l=.2*n*n*1.25*1.25/o,m=1-l/(a-n);f=.8+.2*((r-i)/(l+m*(r-i)))}return f&&(t.s=f,t.h=k(Math.atan2(e.b,e.a)*180/Math.PI)),t}function tt(e){let t=Me(e.l),r={mode:"oklab",l:t};if(e.alpha!==void 0&&(r.alpha=e.alpha),!e.s||e.l===1)return r.a=r.b=0,r;let o=Math.cos(e.h/180*Math.PI),n=Math.sin(e.h/180*Math.PI),[a,f,i]=Pt(t,o,n),l,m,s,d;e.s<.8?(l=1.25*e.s,m=0,s=.8*a,d=1-s/f):(l=5*(e.s-.8),m=f,s=.2*f*f*1.25*1.25/a,d=1-s/(i-f));let h=m+l*s/(1-d*l);return r.a=h*o,r.b=h*n,r}var Ta={...kt,mode:"okhsl",channels:["h","s","l","alpha"],parse:["--okhsl"],serialize:"--okhsl",fromMode:{oklab:et,rgb:e=>et(ne(e))},toMode:{oklab:tt,rgb:e=>ae(tt(e))}},Io=Ta;function rt(e){let t=Math.sqrt(e.a*e.a+e.b*e.b),r=e.l,o=t?e.a/t:1,n=t?e.b/t:1,[a,f]=Ve(o,n),i=.5,l=1-i/a,m=f/(t+r*f),s=m*r,d=m*t,h=Me(s),c=d*h/s,b=K({l:h,a:o*c,b:n*c}),M=Math.cbrt(1/Math.max(b.r,b.g,b.b,0));r=r/M,t=t/M*Qe(r)/r,r=Qe(r);let v={mode:"okhsv",s:t?(i+f)*d/(f*i+f*l*d):0,v:r?r/s:0};return v.s&&(v.h=k(Math.atan2(e.b,e.a)*180/Math.PI)),e.alpha!==void 0&&(v.alpha=e.alpha),v}function ot(e){let t={mode:"oklab"};e.alpha!==void 0&&(t.alpha=e.alpha);let r=e.h||0,o=Math.cos(r/180*Math.PI),n=Math.sin(r/180*Math.PI),[a,f]=Ve(o,n),i=.5,l=1-i/a,m=1-e.s*i/(i+f-f*l*e.s),s=e.s*f*i/(i+f-f*l*e.s),d=Me(m),h=s*d/m,c=K({l:d,a:o*h,b:n*h}),b=Math.cbrt(1/Math.max(c.r,c.g,c.b,0)),M=Me(e.v*m),v=s*M/m;return t.l=M*b,t.a=v*o*b,t.b=v*n*b,t}var za={...Lt,mode:"okhsv",channels:["h","s","v","alpha"],parse:["--okhsv"],serialize:"--okhsv",fromMode:{oklab:rt,rgb:e=>rt(ne(e))},toMode:{oklab:ot,rgb:e=>ae(ot(e))}},Ao=za;function ka(e,t){if(!t||t[0]!=="oklab")return;let r={mode:"oklab"},[,o,n,a,f]=t;if(!(o.type===p.Hue||n.type===p.Hue||a.type===p.Hue))return o.type!==p.None&&(r.l=o.type===p.Number?o.value:o.value/100),n.type!==p.None&&(r.a=n.type===p.Number?n.value:n.value*.4/100),a.type!==p.None&&(r.b=a.type===p.Number?a.value:a.value*.4/100),f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var _r=ka;var La={...Pe,mode:"oklab",toMode:{lrgb:K,rgb:ae},fromMode:{lrgb:Ke,rgb:ne},ranges:{l:[0,1],a:[-.4,.4],b:[-.4,.4]},parse:[_r],serialize:e=>`oklab(${e.l!==void 0?e.l:"none"} ${e.a!==void 0?e.a:"none"} ${e.b!==void 0?e.b:"none"}${e.alpha<1?` / ${e.alpha}`:""})`},qo=La;function Ra(e,t){if(!t||t[0]!=="oklch")return;let r={mode:"oklch"},[,o,n,a,f]=t;if(o.type!==p.None){if(o.type===p.Hue)return;r.l=o.type===p.Number?o.value:o.value/100}if(n.type!==p.None&&(r.c=Math.max(0,n.type===p.Number?n.value:n.value*.4/100)),a.type!==p.None){if(a.type===p.Percentage)return;r.h=a.value}return f.type!==p.None&&(r.alpha=f.type===p.Number?f.value:f.value/100),r}var Hr=Ra;var wa={...Ne,mode:"oklch",toMode:{oklab:e=>D(e,"oklab"),rgb:e=>ae(D(e,"oklab"))},fromMode:{rgb:e=>q(ne(e),"oklch"),oklab:e=>q(e,"oklch")},parse:[Hr],serialize:e=>`oklch(${e.l!==void 0?e.l:"none"} ${e.c!==void 0?e.c:"none"} ${e.h||0}${e.alpha<1?` / ${e.alpha}`:""})`,ranges:{l:[0,1],c:[0,.4],h:[0,360]}},Do=wa;var _a=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a={mode:"xyz65",x:.486570948648216*t+.265667693169093*r+.1982172852343625*o,y:.2289745640697487*t+.6917385218365062*r+.079286914093745*o,z:0*t+.0451133818589026*r+1.043944368900976*o};return n!==void 0&&(a.alpha=n),a},Nt=_a;var Ha=({x:e,y:t,z:r,alpha:o})=>{let n=j({r:e*2.4934969119414263-t*.9313836179191242-.402710784450717*r,g:e*-.8294889695615749+t*1.7626640603183465+.0236246858419436*r,b:e*.0358458302437845-t*.0761723892680418+.9568845240076871*r},"p3");return o!==void 0&&(n.alpha=o),n},$t=Ha;var Sa={...Z,mode:"p3",parse:["display-p3"],serialize:"display-p3",fromMode:{rgb:e=>$t(E(e)),xyz65:$t},toMode:{rgb:e=>Y(Nt(e)),xyz65:Nt}},Jo=Sa;var Sr=e=>{let t=Math.abs(e);return t>=.001953125?Math.sign(e)*Math.pow(t,.5555555555555556):16*e},Xa=({x:e,y:t,z:r,alpha:o})=>{let n={mode:"prophoto",r:Sr(e*1.3457868816471585-t*.2555720873797946-.0511018649755453*r),g:Sr(e*-.5446307051249019+t*1.5082477428451466+.0205274474364214*r),b:Sr(e*0+t*0+1.2119675456389452*r)};return o!==void 0&&(n.alpha=o),n},Ct=Xa;var Xr=e=>{let t=Math.abs(e);return t>=.03125?Math.sign(e)*Math.pow(t,1.8):e/16},Pa=e=>{let t=Xr(e.r),r=Xr(e.g),o=Xr(e.b),n={mode:"xyz50",x:.7977666449006423*t+.1351812974005331*r+.0313477341283922*o,y:.2880748288194013*t+.7118352342418731*r+899369387256e-16*o,z:0*t+0*r+.8251046025104602*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},Ot=Pa;var Na={...Z,mode:"prophoto",parse:["prophoto-rgb"],serialize:"prophoto-rgb",fromMode:{xyz50:Ct,rgb:e=>Ct(U(e))},toMode:{xyz50:Ot,rgb:e=>W(Ot(e))}},Eo=Na;var jo=1.09929682680944,$a=.018053968510807,Pr=e=>{let t=Math.abs(e);return t>$a?(Math.sign(e)||1)*(jo*Math.pow(t,.45)-(jo-1)):4.5*e},Ca=({x:e,y:t,z:r,alpha:o})=>{let n={mode:"rec2020",r:Pr(e*1.7166511879712683-t*.3556707837763925-.2533662813736599*r),g:Pr(e*-.6666843518324893+t*1.6164812366349395+.0157685458139111*r),b:Pr(e*.0176398574453108-t*.0427706132578085+.9421031212354739*r)};return o!==void 0&&(n.alpha=o),n},It=Ca;var Yo=1.09929682680944,Oa=.018053968510807,Nr=e=>{let t=Math.abs(e);return t<Oa*4.5?e/4.5:(Math.sign(e)||1)*Math.pow((t+Yo-1)/Yo,1/.45)},Ia=e=>{let t=Nr(e.r),r=Nr(e.g),o=Nr(e.b),n={mode:"xyz65",x:.6369580483012911*t+.1446169035862083*r+.1688809751641721*o,y:.262700212011267*t+.6779980715188708*r+.059301716469862*o,z:0*t+.0280726930490874*r+1.0609850577107909*o};return e.alpha!==void 0&&(n.alpha=e.alpha),n},At=Ia;var Aa={...Z,mode:"rec2020",fromMode:{xyz65:It,rgb:e=>It(E(e))},toMode:{xyz65:At,rgb:e=>Y(At(e))},parse:["rec2020"],serialize:"rec2020"},Go=Aa;var fe=.0037930732552754493,qt=Math.cbrt(fe);var $r=e=>Math.cbrt(e)-qt,qa=e=>{let{r:t,g:r,b:o,alpha:n}=J(e),a=$r(.3*t+.622*r+.078*o+fe),f=$r(.23*t+.692*r+.078*o+fe),i=$r(.2434226892454782*t+.2047674442449682*r+.5518098665095535*o+fe),l={mode:"xyb",x:(a-f)/2,y:(a+f)/2,b:i-(a+f)/2};return n!==void 0&&(l.alpha=n),l},Cr=qa;var Or=e=>Math.pow(e+qt,3),Da=({x:e,y:t,b:r,alpha:o})=>{let n=Or(e+t)-fe,a=Or(t-e)-fe,f=Or(r+t)-fe,i=j({r:11.031566904639861*n-9.866943908131562*a-.16462299650829934*f,g:-3.2541473810744237*n+4.418770377582723*a-.16462299650829934*f,b:-3.6588512867136815*n+2.7129230459360922*a+1.9459282407775895*f});return o!==void 0&&(i.alpha=o),i},Ir=Da;var Ja={mode:"xyb",channels:["x","y","b","alpha"],parse:["--xyb"],serialize:"--xyb",toMode:{rgb:Ir},fromMode:{rgb:Cr},ranges:{x:[-.0154,.0281],y:[0,.8453],b:[-.2778,.388]},interpolate:{x:u,y:u,b:u,alpha:{use:u,fixup:y}}},Bo=Ja;var Ea={mode:"xyz50",parse:["xyz-d50"],serialize:"xyz-d50",toMode:{rgb:W,lab:ve},fromMode:{rgb:U,lab:ge},channels:["x","y","z","alpha"],ranges:{x:[0,.964],y:[0,.999],z:[0,.825]},interpolate:{x:u,y:u,z:u,alpha:{use:u,fixup:y}}},Zo=Ea;var ja=e=>{let{x:t,y:r,z:o,alpha:n}=e,a={mode:"xyz50",x:1.0479298208405488*t+.0229467933410191*r-.0501922295431356*o,y:.0296278156881593*t+.990434484573249*r-.0170738250293851*o,z:-.0092430581525912*t+.0150551448965779*r+.7518742899580008*o};return n!==void 0&&(a.alpha=n),a},Ar=ja;var Ya=e=>{let{x:t,y:r,z:o,alpha:n}=e,a={mode:"xyz65",x:.9554734527042182*t-.0230985368742614*r+.0632593086610217*o,y:-.0283697069632081*t+1.0099954580058226*r+.021041398966943*o,z:.0123140016883199*t-.0205076964334779*r+1.3303659366080753*o};return n!==void 0&&(a.alpha=n),a},qr=Ya;var Ga={mode:"xyz65",toMode:{rgb:Y,xyz50:Ar},fromMode:{rgb:E,xyz50:qr},ranges:{x:[0,.95],y:[0,1],z:[0,1.088]},channels:["x","y","z","alpha"],parse:["xyz","xyz-d65"],serialize:"xyz-d65",interpolate:{x:u,y:u,z:u,alpha:{use:u,fixup:y}}},Fo=Ga;var Ba=({r:e,g:t,b:r,alpha:o})=>{let n={mode:"yiq",y:.29889531*e+.58662247*t+.11448223*r,i:.59597799*e-.2741761*t-.32180189*r,q:.21147017*e-.52261711*t+.31114694*r};return o!==void 0&&(n.alpha=o),n},Dr=Ba;var Za=({y:e,i:t,q:r,alpha:o})=>{let n={mode:"rgb",r:e+.95608445*t+.6208885*r,g:e-.27137664*t-.6486059*r,b:e-1.10561724*t+1.70250126*r};return o!==void 0&&(n.alpha=o),n},Jr=Za;var Fa={mode:"yiq",toMode:{rgb:Jr},fromMode:{rgb:Dr},channels:["y","i","q","alpha"],parse:["--yiq"],serialize:"--yiq",ranges:{i:[-.595,.595],q:[-.522,.522]},interpolate:{y:u,i:u,q:u,alpha:{use:u,fixup:y}}},Wo=Fa;var Wa=(e,t)=>Math.round(e*(t=Math.pow(10,t)))/t,Ua=(e=4)=>t=>typeof t=="number"?Wa(t,e):t,Er=Ua;var nt=Er(2),at=e=>Math.max(0,Math.min(1,e)),ye=e=>Math.round(at(e)*255),jr=e=>{if(e===void 0)return;let t=ye(e.r),r=ye(e.g),o=ye(e.b);return"#"+(1<<24|t<<16|r<<8|o).toString(16).slice(1)},Uo=e=>{if(e===void 0)return;let t=ye(e.alpha!==void 0?e.alpha:1);return jr(e)+(256|t).toString(16).slice(1)},Ko=e=>{if(e===void 0)return;let t=e.r!==void 0?ye(e.r):"none",r=e.g!==void 0?ye(e.g):"none",o=e.b!==void 0?ye(e.b):"none";return e.alpha===void 0||e.alpha===1?`rgb(${t}, ${r}, ${o})`:`rgba(${t}, ${r}, ${o}, ${nt(at(e.alpha))})`},Qo=e=>{if(e===void 0)return;let t=nt(e.h||0),r=e.s!==void 0?nt(at(e.s)*100)+"%":"none",o=e.l!==void 0?nt(at(e.l)*100)+"%":"none";return e.alpha===void 0||e.alpha===1?`hsl(${t}, ${r}, ${o})`:`hsla(${t}, ${r}, ${o}, ${nt(at(e.alpha))})`},Ka=e=>{let t=P(e);if(!t)return;let r=L(t.mode);if(!r.serialize||typeof r.serialize=="string"){let o=`color(${r.serialize||`--${t.mode}`} `;return r.channels.forEach((n,a)=>{n!=="alpha"&&(o+=(a?" ":"")+(t[n]!==void 0?t[n]:"none"))}),t.alpha!==void 0&&t.alpha<1&&(o+=` / ${t.alpha}`),o+")"}if(typeof r.serialize=="function")return r.serialize(t)},Qa=e=>jr(g("rgb")(e)),Va=e=>Uo(g("rgb")(e)),ef=e=>Ko(g("rgb")(e)),tf=e=>Qo(g("hsl")(e));var rf={normal:(e,t)=>t,multiply:(e,t)=>e*t,screen:(e,t)=>e+t-e*t,"hard-light":(e,t)=>t<.5?e*2*t:2*t*(1-e)-1,overlay:(e,t)=>e<.5?t*2*e:2*e*(1-t)-1,darken:(e,t)=>Math.min(e,t),lighten:(e,t)=>Math.max(e,t),"color-dodge":(e,t)=>e===0?0:t===1?1:Math.min(1,e/(1-t)),"color-burn":(e,t)=>e===1?1:t===0?0:1-Math.min(1,(1-e)/t),"soft-light":(e,t)=>t<.5?e-(1-2*t)*e*(1-e):e+(2*t-1)*((e<.25?((16*e-12)*e+4)*e:Math.sqrt(e))-e),difference:(e,t)=>Math.abs(e-t),exclusion:(e,t)=>e+t-2*e*t},of=(e,t="normal",r="rgb")=>{let o=typeof t=="function"?t:rf[t],n=g(r),a=L(r).channels;return e.map(i=>{let l=n(i);return l.alpha===void 0&&(l.alpha=1),l}).reduce((i,l)=>{if(i===void 0)return l;let m=l.alpha+i.alpha*(1-l.alpha);return a.reduce((s,d)=>(d!=="alpha"&&(m===0?s[d]=0:(s[d]=l.alpha*(1-i.alpha)*l[d]+l.alpha*i.alpha*o(i[d],l[d])+(1-l.alpha)*i.alpha*i[d],s[d]=Math.max(0,Math.min(1,s[d]/m)))),s),{mode:r,alpha:m})})},nf=of;var af=([e,t])=>e+Math.random()*(t-e),ff=e=>Object.keys(e).reduce((t,r)=>{let o=e[r];return t[r]=Array.isArray(o)?o:[o,o],t},{}),lf=(e="rgb",t={})=>{let r=L(e),o=ff(t);return r.channels.reduce((n,a)=>((o.alpha||a!=="alpha")&&(n[a]=af(o[a]||r.ranges[a])),n),{mode:e})},pf=lf;var Te=(e,t="rgb",r=!1)=>{let o=t?L(t).channels:null,n=t?g(t):P;return a=>{let f=n(a);if(!f)return;let i=(o||L(f.mode).channels).reduce((m,s)=>{let d=e(f[s],s,f,t);return d!==void 0&&!isNaN(d)&&(m[s]=d),m},{mode:f.mode});if(!r)return i;let l=P(a);return l&&l.mode!==i.mode?g(l.mode)(i):i}},Yr=(e,t,r)=>t!=="alpha"?(e||0)*(r.alpha!==void 0?r.alpha:1):e,Gr=(e,t,r)=>t!=="alpha"&&r.alpha!==0?(e||0)/(r.alpha!==void 0?r.alpha:1):e,Dt=(e=1,t=0)=>(r,o)=>o!=="alpha"?r*e+t:r,uf=(e=1,t=1,r=0)=>(o,n)=>n!=="alpha"?e*Math.pow(o,t)+r:o;var mf=e=>{e[0]===void 0&&(e[0]=0),e[e.length-1]===void 0&&(e[e.length-1]=1);let t=1,r,o,n,a;for(;t<e.length;){if(e[t]===void 0){for(o=t,n=e[t-1],r=t;e[r]===void 0;)r++;for(a=(e[r]-n)/(r-t+1);t<r;)e[t]=n+(t+1-o)*a,t++}else e[t]<e[t-1]&&(e[t]=e[t-1]);t++}return e},Vo=mf;var sf=(e=.5)=>t=>e<=0?1:e>=1?0:Math.pow(t,Math.log(.5)/Math.log(e)),Br=sf;var Jt=e=>typeof e=="function",ze=e=>e&&typeof e=="object",e0=e=>typeof e=="number",t0=(e,t="rgb",r,o)=>{let n=L(t),a=g(t),f=[],i=[],l={};e.forEach(h=>{Array.isArray(h)?(f.push(a(h[0])),i.push(h[1])):e0(h)||Jt(h)?l[i.length]=h:(f.push(a(h)),i.push(void 0))}),Vo(i);let m=n.channels.reduce((h,c)=>{let b;return ze(r)&&ze(r[c])&&r[c].fixup?b=r[c].fixup:ze(n.interpolate[c])&&n.interpolate[c].fixup?b=n.interpolate[c].fixup:b=M=>M,h[c]=b(f.map(M=>M[c])),h},{});if(o){let h=f.map((c,b)=>n.channels.reduce((M,v)=>(M[v]=m[v][b],M),{mode:t}));m=n.channels.reduce((c,b)=>(c[b]=h.map(M=>{let v=o(M[b],b,M,t);return isNaN(v)?void 0:v}),c),{})}let s=n.channels.reduce((h,c)=>{let b;return Jt(r)?b=r:ze(r)&&Jt(r[c])?b=r[c]:ze(r)&&ze(r[c])&&r[c].use?b=r[c].use:Jt(n.interpolate[c])?b=n.interpolate[c]:ze(n.interpolate[c])&&(b=n.interpolate[c].use),h[c]=b(m[c]),h},{}),d=f.length-1;return h=>{if(h=Math.min(Math.max(0,h),1),h<=i[0])return f[0];if(h>i[d])return f[d];let c=0;for(;i[c]<h;)c++;let b=i[c-1],M=i[c]-b,v=(h-b)/M,z=l[c]||l[0];z!==void 0&&(e0(z)&&(z=Br((z-b)/M)),v=z(v));let _=(c-1+v)/d;return n.channels.reduce((H,N)=>{let $=s[N](_);return $!==void 0&&(H[N]=$),H},{mode:t})}},df=(e,t="rgb",r)=>t0(e,t,r),r0=(e,t)=>(r,o="rgb",n)=>{let a=t?Te(t,o):void 0,f=t0(r,o,n,e);return a?i=>a(f(i)):f},cf=r0(Yr,Gr);var Et=(e,t)=>(e+t)%t,o0=(e,t,r,o,n)=>{let a=n*n,f=a*n;return((1-3*n+3*a-f)*e+(4-6*a+3*f)*t+(1+3*n+3*a-3*f)*r+f*o)/6},Zr=e=>t=>{let r=e.length-1,o=t>=1?r-1:Math.max(0,Math.floor(t*r));return o0(o>0?e[o-1]:2*e[o]-e[o+1],e[o],e[o+1],o<r-1?e[o+2]:2*e[o+1]-e[o],(t-o/r)*r)},Fr=e=>t=>{let r=e.length-1,o=Math.floor(t*r);return o0(e[Et(o-1,e.length)],e[Et(o,e.length)],e[Et(o+1,e.length)],e[Et(o+2,e.length)],(t-o/r)*r)};var n0=e=>{let t,r=e.length-1,o=new Array(r),n=new Array(r),a=new Array(r);for(o[1]=1/4,n[1]=(6*e[1]-e[0])/4,t=2;t<r;++t)o[t]=1/(4-o[t-1]),n[t]=(6*e[t]-(t==r-1?e[r]:0)-n[t-1])*o[t];for(a[0]=e[0],a[r]=e[r],r-1>0&&(a[r-1]=n[r-1]),t=r-2;t>0;--t)a[t]=n[t]-o[t]*a[t+1];return a},hf=e=>Zr(n0(e)),bf=e=>Fr(n0(e));var Oe=Math.sign,Wr=Math.min,G=Math.abs,Ur=e=>{let t=e.length-1,r=[],o=[],n=[];for(let a=0;a<t;a++)r.push((e[a+1]-e[a])*t),o.push(a>0?.5*(e[a+1]-e[a-1])*t:void 0),n.push(a>0?(Oe(r[a-1])+Oe(r[a]))*Wr(G(r[a-1]),G(r[a]),.5*G(o[a])):void 0);return[r,o,n]},Kr=(e,t,r)=>{let o=e.length-1,n=o*o;return a=>{let f;a>=1?f=o-1:f=Math.max(0,Math.floor(a*o));let i=a-f/o,l=i*i,m=l*i;return(t[f]+t[f+1]-2*r[f])*n*m+(3*r[f]-2*t[f]-t[f+1])*o*l+t[f]*i+e[f]}},xf=e=>{if(e.length<3)return u(e);let t=e.length-1,[r,,o]=Ur(e);return o[0]=r[0],o[t]=r[t-1],Kr(e,o,r)},gf=e=>{if(e.length<3)return u(e);let t=e.length-1,[r,o,n]=Ur(e);return o[0]=(e[1]*2-e[0]*1.5-e[2]*.5)*t,o[t]=(e[t]*1.5-e[t-1]*2+e[t-2]*.5)*t,n[0]=o[0]*r[0]<=0?0:G(o[0])>2*G(r[0])?2*r[0]:o[0],n[t]=o[t]*r[t-1]<=0?0:G(o[t])>2*G(r[t-1])?2*r[t-1]:o[t],Kr(e,n,r)},vf=e=>{let t=e.length-1,[r,o,n]=Ur(e);o[0]=.5*(e[1]-e[t])*t,o[t]=.5*(e[0]-e[t-1])*t;let a=(e[0]-e[t])*t,f=a;return n[0]=(Oe(a)+Oe(r[0]))*Wr(G(a),G(r[0]),.5*G(o[0])),n[t]=(Oe(r[t-1])+Oe(f))*Wr(G(r[t-1]),G(f),.5*G(o[t])),Kr(e,n,r)};var Mf=(e=1)=>e===1?t=>t:t=>Math.pow(t,e),Qr=Mf;var yf=(e=2,t=1)=>{let r=Qr(t);if(e<2)return e<1?[]:[r(.5)];let o=[];for(let n=0;n<e;n++)o.push(r(n/(e-1)));return o},Tf=yf;var a0=g("rgb"),f0=e=>{let t={mode:e.mode,r:Math.max(0,Math.min(e.r,1)),g:Math.max(0,Math.min(e.g,1)),b:Math.max(0,Math.min(e.b,1))};return e.alpha!==void 0&&(t.alpha=e.alpha),t},i0=e=>f0(a0(e)),l0=e=>e!==void 0&&e.r>=0&&e.r<=1&&e.g>=0&&e.g<=1&&e.b>=0&&e.b<=1;function Vr(e){return l0(a0(e))}function jt(e="rgb"){let{gamut:t}=L(e);if(!t)return o=>!0;let r=g(typeof t=="string"?t:e);return o=>l0(r(o))}function zf(e){return e=P(e),e===void 0||Vr(e)?e:g(e.mode)(i0(e))}function eo(e="rgb"){let{gamut:t}=L(e);if(!t)return a=>P(a);let r=typeof t=="string"?t:e,o=g(r),n=jt(r);return a=>{let f=P(a);if(!f)return;let i=o(f);if(n(i))return f;let l=f0(i);return f.mode===l.mode?l:g(f.mode)(l)}}function kf(e,t="lch",r="rgb"){e=P(e);let o=r==="rgb"?Vr:jt(r),n=r==="rgb"?i0:eo(r);if(e===void 0||o(e))return e;let a=g(e.mode);e=g(t)(e);let f={...e,c:0};if(!o(f))return a(n(f));let i=0,l=e.c,m=L(t).ranges.c,s=(m[1]-m[0])/Math.pow(2,13),d;for(;l-i>s;)f.c=i+(l-i)*.5,o(f)?(d=f.c,i=f.c):l=f.c;return a(o(f)?f:{...f,c:d})}function Lf(e="rgb",t="oklch",r=be("oklch"),o=.02){let n=g(e);if(!L(e).gamut)return d=>n(d);let a=jt(e),f=eo(e),i=g(t),{ranges:l}=L(t),m=n("white"),s=n("black");return d=>{if(d=P(d),d===void 0)return;let h={...i(d)};if(h.l>=l.l[1]){let z={...m};return d.alpha!==void 0&&(z.alpha=d.alpha),z}if(h.l<=l.l[0]){let z={...s};return d.alpha!==void 0&&(z.alpha=d.alpha),z}if(a(h))return n(h);let c=0,b=h.c,M=(l.c[1]-l.c[0])/4e3,v=f(h);for(;b-c>M;)h.c=(c+b)*.5,v=f(h),a(h)||r&&o>0&&r(h,v)<=o?c=h.c:b=h.c;return n(a(h)?h:v)}}var Rf=(e,t=be(),r=o=>o)=>{let o=e.map((n,a)=>({color:r(n),i:a}));return(n,a=1,f=1/0)=>(isFinite(a)&&(a=Math.max(1,Math.min(a,o.length-1))),o.forEach(i=>{i.d=t(n,i.color)}),o.sort((i,l)=>i.d-l.d).slice(0,a).filter(i=>i.d<f).map(i=>e[i.i]))},wf=Rf;var to=e=>Math.max(e,0),ro=e=>Math.max(Math.min(e,1),0),_f=(e,t,r)=>e===void 0||t===void 0?void 0:e+r*(t-e),Hf=e=>{let t=1-ro(e);return[.393+.607*t,.769-.769*t,.189-.189*t,0,.349-.349*t,.686+.314*t,.168-.168*t,0,.272-.272*t,.534-.534*t,.131+.869*t,0,0,0,0,1]},Sf=e=>{let t=to(e);return[.213+.787*t,.715-.715*t,.072-.072*t,0,.213-.213*t,.715+.285*t,.072-.072*t,0,.213-.213*t,.715-.715*t,.072+.928*t,0,0,0,0,1]},Xf=e=>{let t=1-ro(e);return[.2126+.7874*t,.7152-.7152*t,.0722-.0722*t,0,.2126-.2126*t,.7152+.2848*t,.0722-.0722*t,0,.2126-.2126*t,.7152-.7152*t,.0722+.9278*t,0,0,0,0,1]},Pf=e=>{let t=Math.PI*e/180,r=Math.cos(t),o=Math.sin(t);return[.213+r*.787-o*.213,.715-r*.715-o*.715,.072-r*.072+o*.928,0,.213-r*.213+o*.143,.715+r*.285+o*.14,.072-r*.072-o*.283,0,.213-r*.213-o*.787,.715-r*.715+o*.715,.072+r*.928+o*.072,0,0,0,0,1]},Yt=(e,t,r=!1)=>{let o=g(t),n=L(t).channels;return a=>{let f=o(a);if(!f)return;let i={mode:t},l,m=n.length;for(let d=0;d<e.length;d++)l=n[Math.floor(d/m)],f[l]!==void 0&&(i[l]=(i[l]||0)+e[d]*(f[n[d%m]]||0));if(!r)return i;let s=P(a);return s&&i.mode!==s.mode?g(s.mode)(i):i}},Nf=(e=1,t="rgb")=>{let r=to(e);return Te(Dt(r),t,!0)},$f=(e=1,t="rgb")=>{let r=to(e);return Te(Dt(r,(1-r)/2),t,!0)},Cf=(e=1,t="rgb")=>Yt(Hf(e),t,!0),Of=(e=1,t="rgb")=>Yt(Sf(e),t,!0),If=(e=1,t="rgb")=>Yt(Xf(e),t,!0),Af=(e=1,t="rgb")=>{let r=ro(e);return Te((o,n)=>n==="alpha"?o:_f(r,1-r,o),t,!0)},qf=(e=0,t="rgb")=>Yt(Pf(e),t,!0);var Df=g("rgb"),Jf=[[1,0,-0,0,1,0,-0,-0,1],[.856167,.182038,-.038205,.029342,.955115,.015544,-.00288,-.001563,1.004443],[.734766,.334872,-.069637,.05184,.919198,.028963,-.004928,-.004209,1.009137],[.630323,.465641,-.095964,.069181,.890046,.040773,-.006308,-.007724,1.014032],[.539009,.579343,-.118352,.082546,.866121,.051332,-.007136,-.011959,1.019095],[.458064,.679578,-.137642,.092785,.846313,.060902,-.007494,-.016807,1.024301],[.38545,.769005,-.154455,.100526,.829802,.069673,-.007442,-.02219,1.029632],[.319627,.849633,-.169261,.106241,.815969,.07779,-.007025,-.028051,1.035076],[.259411,.923008,-.18242,.110296,.80434,.085364,-.006276,-.034346,1.040622],[.203876,.990338,-.194214,.112975,.794542,.092483,-.005222,-.041043,1.046265],[.152286,1.052583,-.204868,.114503,.786281,.099216,-.003882,-.048116,1.051998]],Ef=[[1,0,-0,0,1,0,-0,-0,1],[.866435,.177704,-.044139,.049567,.939063,.01137,-.003453,.007233,.99622],[.760729,.319078,-.079807,.090568,.889315,.020117,-.006027,.013325,.992702],[.675425,.43385,-.109275,.125303,.847755,.026942,-.00795,.018572,.989378],[.605511,.52856,-.134071,.155318,.812366,.032316,-.009376,.023176,.9862],[.547494,.607765,-.155259,.181692,.781742,.036566,-.01041,.027275,.983136],[.498864,.674741,-.173604,.205199,.754872,.039929,-.011131,.030969,.980162],[.457771,.731899,-.18967,.226409,.731012,.042579,-.011595,.034333,.977261],[.422823,.781057,-.203881,.245752,.709602,.044646,-.011843,.037423,.974421],[.392952,.82361,-.216562,.263559,.69021,.046232,-.01191,.040281,.97163],[.367322,.860646,-.227968,.280085,.672501,.047413,-.01182,.04294,.968881]],jf=[[1,0,-0,0,1,0,-0,-0,1],[.92667,.092514,-.019184,.021191,.964503,.014306,.008437,.054813,.93675],[.89572,.13333,-.02905,.029997,.9454,.024603,.013027,.104707,.882266],[.905871,.127791,-.033662,.026856,.941251,.031893,.01341,.148296,.838294],[.948035,.08949,-.037526,.014364,.946792,.038844,.010853,.193991,.795156],[1.017277,.027029,-.044306,-.006113,.958479,.047634,.006379,.248708,.744913],[1.104996,-.046633,-.058363,-.032137,.971635,.060503,.001336,.317922,.680742],[1.193214,-.109812,-.083402,-.058496,.97941,.079086,-.002346,.403492,.598854],[1.257728,-.139648,-.118081,-.078003,.975409,.102594,-.003316,.501214,.502102],[1.278864,-.125333,-.153531,-.084748,.957674,.127074,-989e-6,.601151,.399838],[1.255528,-.076749,-.178779,-.078411,.930809,.147602,.004733,.691367,.3039]],oo=(e,t)=>{let r=Math.max(0,Math.min(1,t)),o=Math.round(r/.1),n=Math.round(r%.1),a=e[o];if(n>0&&o<e.length-1){let f=e[o+1];a=a.map((i,l)=>V(a[l],f[l],n))}return f=>{let i=P(f);if(i===void 0)return;let{r:l,g:m,b:s}=Df(i),d={mode:"rgb",r:a[0]*l+a[1]*m+a[2]*s,g:a[3]*l+a[4]*m+a[5]*s,b:a[6]*l+a[7]*m+a[8]*s};return i.alpha!==void 0&&(d.alpha=i.alpha),g(i.mode)(d)}},Yf=(e=1)=>oo(Jf,e),Gf=(e=1)=>oo(Ef,e),Bf=(e=1)=>oo(jf,e);var Zf=e=>e*e*(3-2*e),Ff=e=>.5-Math.sin(Math.asin(1-2*e)/3);var Wf=e=>e*e*e*(e*(e*6-15)+10),Uf=Wf;var Kf=e=>(1-Math.cos(e*Math.PI))/2,Qf=Kf;function no(e){let t=g("lrgb")(e);return .2126*t.r+.7152*t.g+.0722*t.b}function Vf(e,t){let r=no(e),o=no(t);return(Math.max(r,o)+.05)/(Math.min(r,o)+.05)}var e5=T(mo),t5=T(vo),r5=T(To),o5=T(zo),n5=T(ko),a5=T(kt),f5=T(Lt),i5=T(Ro),l5=T(_o),p5=T(Ho),u5=T(Pe),m5=T(So),s5=T(Ne),d5=T(Xo),c5=T($o),h5=T(Co),b5=T(Oo),x5=T(Io),g5=T(Ao),v5=T(qo),M5=T(Do),y5=T(Jo),T5=T(Eo),z5=T(Go),k5=T(Z),L5=T(Bo),R5=T(Zo),w5=T(Fo),_5=T(Wo);export{e5 as a98,tn as average,X as averageAngle,go as averageNumber,nf as blend,or as blerp,kf as clampChroma,eo as clampGamut,zf as clampRgb,Ft as colorsNamed,ct as convertA98ToXyz65,ur as convertCubehelixToRgb,we as convertDlchToLab65,Mt as convertHsiToRgb,Tt as convertHslToRgb,He as convertHsvToRgb,Rt as convertHwbToRgb,_t as convertJabToJch,Fe as convertJabToRgb,Be as convertJabToXyz65,Ht as convertJchToJab,_e as convertLab65ToDlch,re as convertLab65ToRgb,Ee as convertLab65ToXyz65,q as convertLabToLch,We as convertLabToRgb,ge as convertLabToXyz50,D as convertLchToLab,Xt as convertLchuvToLuv,Ke as convertLrgbToOklab,j as convertLrgbToRgb,St as convertLuvToLchuv,Ce as convertLuvToXyz50,tt as convertOkhslToOklab,ot as convertOkhsvToOklab,K as convertOklabToLrgb,et as convertOklabToOkhsl,rt as convertOklabToOkhsv,ae as convertOklabToRgb,Nt as convertP3ToXyz65,Ot as convertProphotoToXyz50,At as convertRec2020ToXyz65,pr as convertRgbToCubehelix,yt as convertRgbToHsi,zt as convertRgbToHsl,Se as convertRgbToHsv,wt as convertRgbToHwb,Ze as convertRgbToJab,Ue as convertRgbToLab,oe as convertRgbToLab65,J as convertRgbToLrgb,ne as convertRgbToOklab,Cr as convertRgbToXyb,U as convertRgbToXyz50,E as convertRgbToXyz65,Dr as convertRgbToYiq,Ir as convertXybToRgb,ve as convertXyz50ToLab,$e as convertXyz50ToLuv,Ct as convertXyz50ToProphoto,W as convertXyz50ToRgb,qr as convertXyz50ToXyz65,ht as convertXyz65ToA98,Ge as convertXyz65ToJab,je as convertXyz65ToLab65,$t as convertXyz65ToP3,It as convertXyz65ToRec2020,Y as convertXyz65ToRgb,Ar as convertXyz65ToXyz50,Jr as convertYiqToRgb,g as converter,t5 as cubehelix,W0 as differenceCie76,U0 as differenceCie94,K0 as differenceCiede2000,Q0 as differenceCmc,be as differenceEuclidean,te as differenceHueChroma,mr as differenceHueNaive,ee as differenceHueSaturation,V0 as differenceHyab,en as differenceKotsarenkoRamos,Vr as displayable,r5 as dlab,o5 as dlch,Qr as easingGamma,Qf as easingInOutSine,Br as easingMidpoint,Uf as easingSmootherstep,Zf as easingSmoothstep,Ff as easingSmoothstepInverse,Nf as filterBrightness,$f as filterContrast,Gf as filterDeficiencyDeuter,Yf as filterDeficiencyProt,Bf as filterDeficiencyTrit,If as filterGrayscale,qf as filterHueRotate,Af as filterInvert,Of as filterSaturate,Cf as filterSepia,y as fixupAlpha,B0 as fixupHueDecreasing,G0 as fixupHueIncreasing,Y0 as fixupHueLonger,S as fixupHueShorter,Ka as formatCss,Qa as formatHex,Va as formatHex8,tf as formatHsl,ef as formatRgb,L as getMode,n5 as hsi,a5 as hsl,f5 as hsv,i5 as hwb,jt as inGamut,df as interpolate,r0 as interpolateWith,cf as interpolateWithPremultipliedAlpha,u as interpolatorLinear,nr as interpolatorPiecewise,Zr as interpolatorSplineBasis,Fr as interpolatorSplineBasisClosed,xf as interpolatorSplineMonotone,gf as interpolatorSplineMonotone2,vf as interpolatorSplineMonotoneClosed,hf as interpolatorSplineNatural,bf as interpolatorSplineNaturalClosed,l5 as jab,p5 as jch,u5 as lab,m5 as lab65,s5 as lch,d5 as lch65,c5 as lchuv,V as lerp,h5 as lrgb,b5 as luv,Gr as mapAlphaDivide,Yr as mapAlphaMultiply,uf as mapTransferGamma,Dt as mapTransferLinear,Te as mapper,mo as modeA98,vo as modeCubehelix,To as modeDlab,zo as modeDlch,ko as modeHsi,kt as modeHsl,Lt as modeHsv,Ro as modeHwb,_o as modeJab,Ho as modeJch,Pe as modeLab,So as modeLab65,Ne as modeLch,Xo as modeLch65,$o as modeLchuv,Co as modeLrgb,Oo as modeLuv,Io as modeOkhsl,Ao as modeOkhsv,qo as modeOklab,Do as modeOklch,Jo as modeP3,Eo as modeProphoto,Go as modeRec2020,Z as modeRgb,Bo as modeXyb,Zo as modeXyz50,Fo as modeXyz65,Wo as modeYiq,wf as nearest,x5 as okhsl,g5 as okhsv,v5 as oklab,M5 as oklch,y5 as p3,Qt as parse,Ut as parseHex,xr as parseHsl,br as parseHslLegacy,gr as parseHwb,Lr as parseLab,Rr as parseLch,Wt as parseNamed,_r as parseOklab,Hr as parseOklch,tr as parseRgb,Kt as parseRgbLegacy,rr as parseTransparent,T5 as prophoto,pf as random,z5 as rec2020,T0 as removeParser,k5 as rgb,Er as round,Tf as samples,jr as serializeHex,Uo as serializeHex8,Qo as serializeHsl,Ko as serializeRgb,Lf as toGamut,N0 as trilerp,P0 as unlerp,T as useMode,po as useParser,Vf as wcagContrast,no as wcagLuminance,L5 as xyb,R5 as xyz50,w5 as xyz65,_5 as yiq};
