@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer components {
  .auth-container {
    @apply min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20;
  }
  
  .auth-card {
    @apply card w-full max-w-md shadow-2xl bg-base-100;
  }
  
  .auth-form {
    @apply card-body;
  }
  
  .form-title {
    @apply text-3xl font-bold text-center mb-6;
  }
  
  .form-input {
    @apply input input-bordered w-full;
  }
  
  .form-button {
    @apply btn btn-primary w-full;
  }
  
  .form-link {
    @apply link link-primary;
  }
  
  .divider-text {
    @apply divider;
  }

  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}
