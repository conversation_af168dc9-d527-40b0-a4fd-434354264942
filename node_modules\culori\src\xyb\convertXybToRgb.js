import convertLrgbToRgb from '../lrgb/convertLrgbToRgb.js';
import { bias, bias_cbrt } from './constants.js';

const transfer = v => Math.pow(v + bias_cbrt, 3);

const convertXybToRgb = ({ x, y, b, alpha }) => {
	const l = transfer(x + y) - bias;
	const m = transfer(y - x) - bias;
	/* Account for chroma from luma: add Y back to B */
	const s = transfer(b + y) - bias;

	const res = convertLrgbToRgb({
		r:
			11.*************** * l -
			9.*************** * m -
			0.***************** * s,
		g:
			-3.**************** * l +
			4.*************** * m -
			0.***************** * s,
		b:
			-3.**************** * l +
			2.**************** * m +
			1.**************** * s
	});
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

export default convertXybToRgb;
