import type { ChartType, ChartComponentLike, DefaultDataPoint } from 'chart.js';
import type { TypedChartComponent } from './types.js';
export declare function createTypedChart<TType extends ChartType = ChartType, TData = DefaultDataPoint<TType>, TLabel = unknown>(type: TType, registerables: ChartComponentLike): TypedChartComponent<TType, TData, TLabel>;
interface ExtendedDataPoint {
    [key: string]: string | number | null | ExtendedDataPoint;
}
export declare const Bar: TypedChartComponent<"bar", (number | [number, number] | null)[] | ExtendedDataPoint[], unknown>;
export declare const Doughnut: TypedChartComponent<"doughnut", number[], unknown>;
export declare const Line: TypedChartComponent<"line", (number | import("chart.js").Point | null)[], unknown>;
export declare const Pie: TypedChartComponent<"pie", number[], unknown>;
export declare const PolarArea: TypedChartComponent<"polarArea", number[], unknown>;
export declare const Radar: TypedChartComponent<"radar", (number | null)[], unknown>;
export declare const Bubble: TypedChartComponent<"bubble", import("chart.js").BubbleDataPoint[], unknown>;
export declare const Scatter: TypedChartComponent<"scatter", (number | import("chart.js").Point | null)[], unknown>;
export {};
//# sourceMappingURL=typedCharts.d.ts.map