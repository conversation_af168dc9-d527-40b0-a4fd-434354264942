<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" v-if="showDemo" @click.self="closeDemo">
    <div class="bg-base-100 rounded-lg shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-3xl font-bold text-primary">血统繁衍演示</h2>
          <button class="btn btn-circle btn-sm" @click="closeDemo">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Parents Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <!-- Father -->
          <div class="card bg-base-200 shadow-lg">
            <div class="card-body">
              <h3 class="card-title text-blue-600">👨 父亲</h3>
              <div class="space-y-2">
                <p><strong>姓名:</strong> {{ father.name }}</p>
                <p><strong>职业:</strong> {{ father.profession }}</p>
                <p><strong>血脉:</strong> 
                  <span v-for="(bloodline, index) in father.bloodlines" :key="index" class="badge badge-primary mr-1">
                    {{ bloodline.name }} {{ bloodline.percentage }}%
                  </span>
                </p>
                <div class="grid grid-cols-3 gap-2 text-sm">
                  <div v-for="(value, attr) in father.attributes" :key="attr" class="text-center">
                    <div class="font-semibold">{{ ATTRIBUTES[attr] }}</div>
                    <div :class="getRatingColor(value, 'attribute')">{{ Math.round(value) }}</div>
                    <div class="text-xs">{{ getRating(value, 'attribute') }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mother -->
          <div class="card bg-base-200 shadow-lg">
            <div class="card-body">
              <h3 class="card-title text-pink-600">👩 母亲</h3>
              <div class="space-y-2">
                <p><strong>姓名:</strong> {{ mother.name }}</p>
                <p><strong>职业:</strong> {{ mother.profession }}</p>
                <p><strong>血脉:</strong> 
                  <span v-for="(bloodline, index) in mother.bloodlines" :key="index" class="badge badge-secondary mr-1">
                    {{ bloodline.name }} {{ bloodline.percentage }}%
                  </span>
                </p>
                <div class="grid grid-cols-3 gap-2 text-sm">
                  <div v-for="(value, attr) in mother.attributes" :key="attr" class="text-center">
                    <div class="font-semibold">{{ ATTRIBUTES[attr] }}</div>
                    <div :class="getRatingColor(value, 'attribute')">{{ Math.round(value) }}</div>
                    <div class="text-xs">{{ getRating(value, 'attribute') }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Breeding Button -->
        <div class="text-center mb-6">
          <button class="btn btn-success btn-lg" @click="generateChildren" :disabled="isGenerating">
            <span v-if="isGenerating" class="loading loading-spinner"></span>
            {{ isGenerating ? '生育中...' : '💕 生育后代' }}
          </button>
          <button class="btn btn-outline btn-lg ml-4" @click="regenerateParents">
            🔄 重新生成父母
          </button>
          <button v-if="children.length > 0" class="btn btn-ghost btn-lg ml-4" @click="children = []">
            🗑️ 清除后代
          </button>
          <div v-if="children.length > 0" class="mt-4">
            <div class="text-sm text-base-content/70">
              生育概率：单胎 85% | 双胞胎 13% | 三胞胎 2%
            </div>
          </div>
        </div>

        <!-- Children Section -->
        <div v-if="children.length > 0" class="space-y-4">
          <div class="text-center">
            <h3 class="text-2xl font-bold text-accent mb-2">
              🍼 新生儿 ({{ children.length === 1 ? '单胎' : children.length === 2 ? '双胞胎' : '三胞胎' }})
            </h3>
            <div class="text-sm text-base-content/70 mb-4">
              血脉继承：父系贡献 30-70%，母系贡献 30-70%，随机变异 ±2 属性点
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="(child, index) in children" :key="index" class="card bg-gradient-to-br from-yellow-50 to-orange-50 shadow-lg border-2 border-yellow-200">
              <div class="card-body">
                <h4 class="card-title text-orange-600">
                  {{ child.gender === 'male' ? '👶' : '👧' }} {{ child.name }}
                </h4>
                <div class="space-y-2">
                  <p><strong>性别:</strong> {{ child.gender === 'male' ? '男' : '女' }}</p>
                  <p><strong>继承血脉:</strong></p>
                  <div class="flex flex-wrap gap-1 mb-2">
                    <span v-for="(bloodline, bIndex) in child.bloodlines" :key="bIndex"
                          class="badge badge-warning badge-sm"
                          :title="getBloodlineSource(bloodline, child)">
                      {{ bloodline.name }} {{ bloodline.percentage }}%
                    </span>
                  </div>
                  <div class="text-xs text-base-content/60">
                    💡 悬停血脉标签查看继承来源
                  </div>
                  <div class="grid grid-cols-3 gap-1 text-xs">
                    <div v-for="(value, attr) in child.attributes" :key="attr" class="text-center">
                      <div class="font-semibold">{{ ATTRIBUTES[attr] }}</div>
                      <div :class="getRatingColor(value, 'attribute')">{{ Math.round(value) }}</div>
                      <div class="text-xs">{{ getRating(value, 'attribute') }}</div>
                      <div class="text-xs" :class="getAttributeChangeColor(value, attr)">
                        {{ getAttributeChange(value, attr) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { PROFESSIONS, BLOODLINES, ATTRIBUTES, getRating } from '../data/gameData.js'

const props = defineProps({
  showDemo: Boolean
})

const emit = defineEmits(['close'])

const father = ref({})
const mother = ref({})
const children = ref([])
const isGenerating = ref(false)

// Male and female names for random generation
const maleNames = ['李明', '王强', '张伟', '刘军', '陈杰', '杨勇', '赵磊', '孙涛', '周斌', '吴峰']
const femaleNames = ['李娜', '王丽', '张敏', '刘芳', '陈静', '杨雪', '赵艳', '孙莉', '周红', '吴梅']

// Weighted bloodline selection
const weightedBloodlineKeys = Object.entries(BLOODLINES).flatMap(([key, bloodline]) => 
  Array(bloodline.rarity).fill(key)
)

const pickRandomBloodline = () => 
  weightedBloodlineKeys[Math.floor(Math.random() * weightedBloodlineKeys.length)]

const generateRandomBloodline = () => {
  if (Math.random() < 0.7) {
    const key = pickRandomBloodline()
    return [{ key, name: BLOODLINES[key].name, percentage: 100 }]
  } else {
    let key1 = pickRandomBloodline(), key2 = pickRandomBloodline()
    while (key1 === key2) { key2 = pickRandomBloodline() }
    const percentage1 = Math.floor(Math.random() * 81) + 10
    return [
      { key: key1, name: BLOODLINES[key1].name, percentage: percentage1 },
      { key: key2, name: BLOODLINES[key2].name, percentage: 100 - percentage1 }
    ].sort((a, b) => b.percentage - a.percentage)
  }
}

const calculateFinalAttributes = (profession, bloodlines) => {
  const finalAttrs = { ...profession.initial_attributes }
  bloodlines.forEach(b => {
    const percentage = b.percentage / 100
    for (const attr in BLOODLINES[b.key].attribute_modifiers) {
      finalAttrs[attr] += BLOODLINES[b.key].attribute_modifiers[attr] * percentage
    }
  })
  return finalAttrs
}

const generateRandomPerson = (isMale = true) => {
  const professionKeys = Object.keys(PROFESSIONS)
  const professionKey = professionKeys[Math.floor(Math.random() * professionKeys.length)]
  const profession = PROFESSIONS[professionKey]
  const bloodlines = generateRandomBloodline()
  
  return {
    name: isMale ? maleNames[Math.floor(Math.random() * maleNames.length)] : 
                   femaleNames[Math.floor(Math.random() * femaleNames.length)],
    profession: profession.name,
    professionKey,
    bloodlines,
    attributes: calculateFinalAttributes(profession, bloodlines),
    gender: isMale ? 'male' : 'female'
  }
}

const regenerateParents = () => {
  father.value = generateRandomPerson(true)
  mother.value = generateRandomPerson(false)
  children.value = []
}

const inheritBloodlines = (fatherBloodlines, motherBloodlines) => {
  const inherited = []
  const allBloodlines = [...fatherBloodlines, ...motherBloodlines]
  
  // Each parent contributes 50% on average, with some randomness
  const fatherContribution = 0.3 + Math.random() * 0.4 // 30-70%
  const motherContribution = 1 - fatherContribution
  
  // Combine bloodlines
  const bloodlineMap = new Map()
  
  fatherBloodlines.forEach(b => {
    const contribution = (b.percentage / 100) * fatherContribution
    if (bloodlineMap.has(b.key)) {
      bloodlineMap.set(b.key, bloodlineMap.get(b.key) + contribution)
    } else {
      bloodlineMap.set(b.key, contribution)
    }
  })
  
  motherBloodlines.forEach(b => {
    const contribution = (b.percentage / 100) * motherContribution
    if (bloodlineMap.has(b.key)) {
      bloodlineMap.set(b.key, bloodlineMap.get(b.key) + contribution)
    } else {
      bloodlineMap.set(b.key, contribution)
    }
  })
  
  // Convert back to percentage and filter significant bloodlines
  for (const [key, percentage] of bloodlineMap.entries()) {
    const finalPercentage = Math.round(percentage * 100)
    if (finalPercentage >= 5) { // Only include bloodlines with 5%+ contribution
      inherited.push({
        key,
        name: BLOODLINES[key].name,
        percentage: finalPercentage
      })
    }
  }
  
  // Normalize to 100%
  const totalPercentage = inherited.reduce((sum, b) => sum + b.percentage, 0)
  if (totalPercentage > 0) {
    inherited.forEach(b => {
      b.percentage = Math.round((b.percentage / totalPercentage) * 100)
    })
  }
  
  return inherited.sort((a, b) => b.percentage - a.percentage)
}

const generateChild = () => {
  const childGender = Math.random() < 0.5 ? 'male' : 'female'
  const childBloodlines = inheritBloodlines(father.value.bloodlines, mother.value.bloodlines)
  
  // Child inherits a random profession (could be from either parent or random)
  const professionKeys = Object.keys(PROFESSIONS)
  const professionKey = Math.random() < 0.4 ? father.value.professionKey :
                       Math.random() < 0.6 ? mother.value.professionKey :
                       professionKeys[Math.floor(Math.random() * professionKeys.length)]
  
  const profession = PROFESSIONS[professionKey]
  
  // Calculate child attributes (average of parents + bloodline modifiers + some randomness)
  const childAttributes = {}
  Object.keys(ATTRIBUTES).forEach(attr => {
    const fatherAttr = father.value.attributes[attr]
    const motherAttr = mother.value.attributes[attr]
    const baseAttr = profession.initial_attributes[attr]
    
    // Average of parents with some regression to profession mean and randomness
    let childAttr = (fatherAttr + motherAttr) / 2 * 0.7 + baseAttr * 0.3
    childAttr += (Math.random() - 0.5) * 4 // ±2 random variation
    
    // Apply bloodline modifiers
    childBloodlines.forEach(b => {
      const percentage = b.percentage / 100
      childAttr += BLOODLINES[b.key].attribute_modifiers[attr] * percentage
    })
    
    childAttributes[attr] = Math.max(1, childAttr) // Minimum 1
  })
  
  return {
    name: childGender === 'male' ? 
          maleNames[Math.floor(Math.random() * maleNames.length)] :
          femaleNames[Math.floor(Math.random() * femaleNames.length)],
    gender: childGender,
    bloodlines: childBloodlines,
    attributes: childAttributes
  }
}

const generateChildren = async () => {
  isGenerating.value = true
  
  // Simulate breeding time
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  // Determine number of children (single: 85%, twins: 13%, triplets: 2%)
  const rand = Math.random()
  let numChildren = 1
  if (rand < 0.02) numChildren = 3      // 2% triplets
  else if (rand < 0.15) numChildren = 2 // 13% twins
  
  children.value = []
  for (let i = 0; i < numChildren; i++) {
    children.value.push(generateChild())
  }
  
  isGenerating.value = false
}

const getRatingColor = (value, type = 'attribute') => {
  const rating = getRating(value, type)
  const colorMap = {
    'SSS': 'text-red-500 font-extrabold',
    'SS': 'text-orange-500 font-bold',
    'S': 'text-yellow-500 font-bold',
    'A': 'text-green-500 font-semibold',
    'B': 'text-blue-500 font-medium',
    'C': 'text-indigo-500',
    'D': 'text-gray-500',
    'E': 'text-gray-400'
  }
  return colorMap[rating] || 'text-gray-400'
}

const getBloodlineSource = (childBloodline, child) => {
  const fatherHas = father.value.bloodlines.find(b => b.key === childBloodline.key)
  const motherHas = mother.value.bloodlines.find(b => b.key === childBloodline.key)

  if (fatherHas && motherHas) {
    return `继承自父母双方 (父: ${fatherHas.percentage}%, 母: ${motherHas.percentage}%)`
  } else if (fatherHas) {
    return `继承自父亲 (${fatherHas.percentage}%)`
  } else if (motherHas) {
    return `继承自母亲 (${motherHas.percentage}%)`
  } else {
    return '血脉融合产生的新特征'
  }
}

const getAttributeChange = (childValue, attr) => {
  const fatherValue = father.value.attributes[attr]
  const motherValue = mother.value.attributes[attr]
  const parentAverage = (fatherValue + motherValue) / 2
  const change = childValue - parentAverage

  if (Math.abs(change) < 0.5) return '='
  return change > 0 ? `+${Math.round(change)}` : `${Math.round(change)}`
}

const getAttributeChangeColor = (childValue, attr) => {
  const fatherValue = father.value.attributes[attr]
  const motherValue = mother.value.attributes[attr]
  const parentAverage = (fatherValue + motherValue) / 2
  const change = childValue - parentAverage

  if (Math.abs(change) < 0.5) return 'text-gray-500'
  return change > 0 ? 'text-green-600' : 'text-red-600'
}

const closeDemo = () => {
  emit('close')
}

// Initialize parents when component is created
regenerateParents()
</script>

<style scoped>
.loading {
  width: 1rem;
  height: 1rem;
}
</style>
