<template>
  <div class="hero min-h-screen bg-base-200">
    <div class="hero-content text-center">
      <div class="max-w-4xl w-full">
        <h1 class="text-5xl font-bold mb-4">创建你的传奇家族</h1>
        <p class="mb-8">命运的丝线在此交织。输入你创始人的名字，选择他的天职，然后见证血脉的觉醒。</p>

        <div class="card w-full bg-base-100 shadow-2xl p-4">
          <div class="grid grid-cols-1 lg:grid-cols-5 gap-6 items-center">
            <!-- Left Side: Inputs and Actions -->
            <div class="lg:col-span-2">
              <form class="card-body" @submit.prevent="createFamily">
                <div class="form-control">
                  <label class="label"><span class="label-text">创始人姓名</span></label>
                  <input type="text" v-model="patriarchName" placeholder="输入姓名" class="input input-bordered" required />
                </div>
                <div class="form-control mt-4">
                  <label class="label"><span class="label-text">选择职业</span></label>
                  <select v-model="selectedProfessionKey" class="select select-bordered">
                    <option v-for="(prof, key) in PROFESSIONS" :key="key" :value="key">{{ prof.name }}</option>
                  </select>
                </div>

                <div class="form-control mt-4">
                  <label class="label"><span class="label-text font-bold">天选血脉</span></label>
                  <div class="space-y-2">
                    <div v-for="b in generatedBloodline" :key="b.key" class="tooltip w-full" :data-tip="BLOODLINES[b.key].description">
                      <div class="flex items-center gap-2">
                        <span class="font-mono text-sm w-24 text-left truncate">{{ BLOODLINES[b.key].name }}</span>
                        <progress class="progress progress-primary w-full" :value="b.percentage" max="100"></progress>
                        <span class="font-mono text-sm">{{ b.percentage }}%</span>
                      </div>
                    </div>
                  </div>
                  <button type="button" class="btn btn-accent btn-sm mt-4" @click="generateRandomBloodline">随机生成血脉</button>
                </div>

                <div class="form-control mt-6">
                  <button class="btn btn-primary btn-lg" :disabled="!patriarchName || !generatedBloodline.length">开启家族传奇</button>
                </div>
              </form>
            </div>

            <!-- Right Side: Radar Chart and Ratings -->
            <div class="lg:col-span-3 p-4 relative h-[350px] lg:h-[400px] radar-chart-container">
              <!-- Chart Container with enhanced styling -->
              <div class="absolute inset-2 flex items-center justify-center bg-gradient-to-br from-base-100 to-base-200 rounded-xl shadow-inner chart-background">
                <div class="w-full h-full max-w-sm max-h-sm p-4">
                  <Radar :data="chartData" :options="chartOptions" />
                </div>
              </div>

              <!-- Enhanced Attribute Labels positioned around the chart -->
              <div v-for="(label, index) in attributeLabels" :key="label.key"
                   class="absolute w-28 text-center transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-10 attribute-label"
                   :style="label.style">
                <!-- Attribute Name with enhanced styling -->
                <div class="bg-base-100 rounded-md shadow-md px-2 py-1 border border-base-300 mb-1">
                  <div class="font-bold text-sm text-primary">{{ ATTRIBUTES[label.key] }}</div>
                </div>

                <!-- Rating Display with color coding -->
                <div class="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-md shadow-sm px-2 py-1 border border-base-300">
                  <div class="flex flex-col items-center gap-0.5">
                    <!-- Current Attribute Value -->
                    <div class="flex items-center gap-1">
                      <span class="text-xs text-base-content/70">当前:</span>
                      <span :class="getRatingColor(finalAttributes[label.key], 'attribute')"
                            class="font-bold text-sm font-mono">
                        {{ getRating(finalAttributes[label.key], 'attribute') }}
                      </span>
                      <span class="text-xs text-base-content/50">{{ Math.round(finalAttributes[label.key]) }}</span>
                    </div>

                    <!-- Growth Value -->
                    <div class="flex items-center gap-1">
                      <span class="text-xs text-base-content/70">成长:</span>
                      <span :class="getRatingColor(finalGrowthValues[label.key], 'growth')"
                            class="font-bold text-xs font-mono">
                        {{ getRating(finalGrowthValues[label.key], 'growth') }}
                      </span>
                      <span class="text-xs text-base-content/50">{{ Math.round(finalGrowthValues[label.key]) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { PROFESSIONS, BLOODLINES, ATTRIBUTES, getRating } from '../data/gameData.js'
import { Radar } from 'vue-chartjs'
import { Chart as ChartJS, Title, Tooltip, Legend, PointElement, RadialLinearScale, LineElement, Filler } from 'chart.js'

ChartJS.register(Title, Tooltip, Legend, PointElement, RadialLinearScale, LineElement, Filler)

const router = useRouter()

// --- STATE ---
const patriarchName = ref('')
const selectedProfessionKey = ref(Object.keys(PROFESSIONS)[0])
const generatedBloodline = ref([])

// --- BLOODLINE GENERATION ---
const weightedBloodlineKeys = Object.entries(BLOODLINES).flatMap(([key, bloodline]) => Array(bloodline.rarity).fill(key))
const pickRandomBloodline = () => weightedBloodlineKeys[Math.floor(Math.random() * weightedBloodlineKeys.length)]

const generateRandomBloodline = () => {
  if (Math.random() < 0.7) {
    generatedBloodline.value = [{ key: pickRandomBloodline(), percentage: 100 }]
  } else {
    let key1 = pickRandomBloodline(), key2 = pickRandomBloodline()
    while (key1 === key2) { key2 = pickRandomBloodline() }
    const percentage1 = Math.floor(Math.random() * 81) + 10
    generatedBloodline.value = [
      { key: key1, percentage: percentage1 },
      { key: key2, percentage: 100 - percentage1 }
    ].sort((a, b) => b.percentage - a.percentage)
  }
}

// --- COMPUTED PROPERTIES ---
const selectedProfession = computed(() => PROFESSIONS[selectedProfessionKey.value])

const calculateFinalValues = (baseValues, modifierType) => {
  const finalVals = { ...baseValues }
  generatedBloodline.value.forEach(b => {
    const percentage = b.percentage / 100
    for (const attr in BLOODLINES[b.key][modifierType]) {
      finalVals[attr] += BLOODLINES[b.key][modifierType][attr] * percentage
    }
  })
  return finalVals
}

const finalAttributes = computed(() => calculateFinalValues(selectedProfession.value.initial_attributes, 'attribute_modifiers'))
const finalGrowthValues = computed(() => calculateFinalValues(selectedProfession.value.growth_values, 'growth_modifiers'))

// --- CHART & LABELS ---
const getChartColors = () => {
  // 根据最高属性动态调整颜色
  const values = Object.values(finalAttributes.value)
  const maxValue = Math.max(...values)
  const maxIndex = values.indexOf(maxValue)

  const colorSchemes = [
    { bg: 'rgba(239, 68, 68, 0.15)', border: 'rgba(239, 68, 68, 0.8)', point: 'rgba(239, 68, 68, 1)' }, // 力量-红色
    { bg: 'rgba(34, 197, 94, 0.15)', border: 'rgba(34, 197, 94, 0.8)', point: 'rgba(34, 197, 94, 1)' }, // 敏捷-绿色
    { bg: 'rgba(168, 85, 247, 0.15)', border: 'rgba(168, 85, 247, 0.8)', point: 'rgba(168, 85, 247, 1)' }, // 体质-紫色
    { bg: 'rgba(59, 130, 246, 0.15)', border: 'rgba(59, 130, 246, 0.8)', point: 'rgba(59, 130, 246, 1)' }, // 智力-蓝色
    { bg: 'rgba(245, 158, 11, 0.15)', border: 'rgba(245, 158, 11, 0.8)', point: 'rgba(245, 158, 11, 1)' }, // 感知-橙色
    { bg: 'rgba(236, 72, 153, 0.15)', border: 'rgba(236, 72, 153, 0.8)', point: 'rgba(236, 72, 153, 1)' }  // 魅力-粉色
  ]

  return colorSchemes[maxIndex] || colorSchemes[0]
}

const chartData = computed(() => {
  const colors = getChartColors()
  return {
    labels: Object.values(ATTRIBUTES),
    datasets: [{
      label: '属性值',
      data: Object.values(finalAttributes.value),
      backgroundColor: colors.bg,
      borderColor: colors.border,
      borderWidth: 3,
      pointBackgroundColor: colors.point,
      pointBorderColor: '#ffffff',
      pointBorderWidth: 2,
      pointRadius: 6,
      pointHoverRadius: 8,
      pointHoverBackgroundColor: colors.point,
      pointHoverBorderColor: '#ffffff',
      pointHoverBorderWidth: 3,
      fill: true,
      tension: 0.1
    }]
  }
})

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'point'
  },
  scales: {
    r: {
      angleLines: {
        color: 'rgba(148, 163, 184, 0.3)',
        lineWidth: 1
      },
      grid: {
        color: 'rgba(148, 163, 184, 0.2)',
        lineWidth: 1
      },
      pointLabels: { display: false }, // Hide default labels
      ticks: {
        display: false,
        stepSize: 5
      },
      min: 0,
      max: 25,
      backgroundColor: 'rgba(248, 250, 252, 0.1)'
    }
  },
  plugins: {
    legend: { display: false },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(15, 23, 42, 0.9)',
      titleColor: '#ffffff',
      bodyColor: '#ffffff',
      borderColor: 'rgba(99, 102, 241, 0.5)',
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: false,
      callbacks: {
        title: function(context) {
          return context[0].label
        },
        label: function(context) {
          const value = Math.round(context.parsed.r * 10) / 10
          const rating = getRating(value, 'attribute')
          const growth = finalGrowthValues.value[Object.keys(ATTRIBUTES)[context.dataIndex]]
          const growthRating = getRating(growth, 'growth')
          return [
            `当前值: ${value} (${rating})`,
            `成长值: ${Math.round(growth)} (${growthRating})`
          ]
        }
      }
    }
  },
  animation: {
    duration: 1200,
    easing: 'easeInOutQuart',
    animateRotate: true,
    animateScale: true
  },
  elements: {
    line: {
      borderJoinStyle: 'round'
    },
    point: {
      hoverRadius: 10
    }
  }
})

const attributeKeys = Object.keys(ATTRIBUTES)
const attributeLabels = computed(() => {
  const angleSlice = (2 * Math.PI) / attributeKeys.length;
  return attributeKeys.map((key, i) => {
    const angle = angleSlice * i - Math.PI / 2; // Start from top
    const radius = 45; // 减小半径，让标签更靠近图表
    const x = 50 + radius * Math.cos(angle); // 50 is center
    const y = 50 + radius * Math.sin(angle);
    return { key, style: { top: `${y}%`, left: `${x}%` } };
  });
});

// Color coding for ratings
const getRatingColor = (value, type = 'attribute') => {
  const rating = getRating(value, type)
  const colorMap = {
    'SSS': 'text-red-500 font-extrabold',
    'SS': 'text-orange-500 font-bold',
    'S': 'text-yellow-500 font-bold',
    'A': 'text-green-500 font-semibold',
    'B': 'text-blue-500 font-medium',
    'C': 'text-indigo-500',
    'D': 'text-gray-500',
    'E': 'text-gray-400'
  }
  return colorMap[rating] || 'text-gray-400'
}

// --- METHODS ---
const createFamily = () => {
  const familyData = { /* ... data structure ... */ }
  localStorage.setItem('familyData', JSON.stringify(familyData))
  router.push('/')
}

// --- WATCHERS ---
// 监听职业和血脉变化，触发图表更新动画
watch([selectedProfessionKey, generatedBloodline], () => {
  // 触发图表重新渲染动画
  chartOptions.value = { ...chartOptions.value }
}, { deep: true })

// --- LIFECYCLE ---
onMounted(generateRandomBloodline)
</script>

<style scoped>
/* 六维图容器动画 */
.radar-chart-container {
  transition: all 0.3s ease-in-out;
}

.radar-chart-container:hover {
  transform: scale(1.02);
}

/* 属性标签动画效果 */
.attribute-label {
  transition: all 0.2s ease-in-out;
  backdrop-filter: blur(4px);
}

.attribute-label:hover {
  transform: scale(1.05) translate(-50%, -50%);
  z-index: 20;
}

/* 评级颜色渐变效果 */
.rating-sss { @apply bg-gradient-to-r from-red-500 to-pink-500 text-white; }
.rating-ss { @apply bg-gradient-to-r from-orange-500 to-red-500 text-white; }
.rating-s { @apply bg-gradient-to-r from-yellow-500 to-orange-500 text-white; }
.rating-a { @apply bg-gradient-to-r from-green-500 to-yellow-500 text-white; }
.rating-b { @apply bg-gradient-to-r from-blue-500 to-green-500 text-white; }
.rating-c { @apply bg-gradient-to-r from-indigo-500 to-blue-500 text-white; }
.rating-d { @apply bg-gradient-to-r from-gray-500 to-indigo-500 text-white; }
.rating-e { @apply bg-gradient-to-r from-gray-400 to-gray-500 text-white; }

/* 图表背景渐变 */
.chart-background {
  background: radial-gradient(circle at center, rgba(99, 102, 241, 0.05) 0%, rgba(99, 102, 241, 0.02) 50%, transparent 100%);
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .radar-chart-container {
    height: 320px !important;
  }

  .attribute-label {
    font-size: 0.75rem;
    width: 6rem;
  }

  .attribute-label .font-bold {
    font-size: 0.875rem;
  }
}

@media (max-width: 768px) {
  .radar-chart-container {
    height: 280px !important;
  }

  .attribute-label {
    font-size: 0.625rem;
    width: 5rem;
  }

  .attribute-label .font-bold {
    font-size: 0.75rem;
  }

  .attribute-label .text-xs {
    font-size: 0.5rem;
  }
}
</style>
