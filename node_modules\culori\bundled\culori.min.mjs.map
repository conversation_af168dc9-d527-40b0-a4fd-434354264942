{"mappings": "ACAA,wEAAwE;AAEzD,kDAAyB,KAAE,EAAC,KAAE,EAAC,KAAE,EAAC,SAAE,MAAK,EAAE;IACzD,IAAI,IAAI,KAAK,IAAI,GAAG,GAAG,IACtB,IAAI,KAAK,IAAI,GAAG,GAAG;IACpB,IAAI,MAAM;QACT,MAAM;QACN,GAAG,MAAM,IAAI,IAAI,AAAC,CAAA,IAAI,CAAA,IAAM,CAAA,IAAI,KAAK,IAAI,IAAI,IAAI,EAAC;QAClD,GAAG,MAAO,CAAA,IAAI,CAAA;IACf;IACA,IAAI,IAAI,MAAM,GACb,IAAI,IACH,AAAC,CAAA,MAAM,IACJ,AAAC,CAAA,IAAI,CAAA,IAAM,CAAA,IAAI,CAAA,IAAK,AAAC,CAAA,IAAI,CAAA,IAAK,IAC9B,MAAM,IACN,AAAC,CAAA,IAAI,CAAA,IAAM,CAAA,IAAI,CAAA,IAAK,IACpB,AAAC,CAAA,IAAI,CAAA,IAAM,CAAA,IAAI,CAAA,IAAK,CAAA,IAAK;IAC9B,IAAI,UAAU,WAAW,IAAI,QAAQ;IACrC,OAAO;AACR;;AEnBA,MAAM,oCAAc,CAAC,OAAO;IAC3B,IAAI,OAAO,UAAU,UAAU;IAE/B,wBAAwB;IACxB,IAAI,QAAQ,GACX,OAAO;QACN,MAAM;QACN,GAAG,AAAC,CAAA,AAAE,SAAS,IAAK,MAAQ,AAAC,SAAS,IAAK,IAAI,IAAK;QACpD,GAAG,AAAC,CAAA,AAAE,SAAS,IAAK,MAAQ,QAAQ,IAAI,IAAK;QAC7C,GAAG,AAAC,CAAA,AAAC,QAAQ,MAAQ,AAAC,SAAS,IAAK,IAAI,IAAK;IAC9C;IAGD,2BAA2B;IAC3B,IAAI,QAAQ,GACX,OAAO;QACN,MAAM;QACN,GAAG,AAAC,CAAA,AAAE,SAAS,KAAM,MAAQ,AAAC,SAAS,IAAK,IAAI,IAAK;QACrD,GAAG,AAAC,CAAA,AAAE,SAAS,IAAK,MAAQ,AAAC,SAAS,IAAK,IAAI,IAAK;QACpD,GAAG,AAAC,CAAA,AAAE,SAAS,IAAK,MAAQ,QAAQ,IAAI,IAAK;QAC7C,OAAO,AAAC,CAAA,AAAC,QAAQ,MAAQ,AAAC,SAAS,IAAK,IAAI,IAAK;IAClD;IAGD,gBAAgB;IAChB,IAAI,QAAQ,GACX,OAAO;QACN,MAAM;QACN,GAAG,AAAC,CAAA,AAAC,SAAS,KAAM,IAAG,IAAK;QAC5B,GAAG,AAAC,CAAA,AAAC,SAAS,IAAK,IAAG,IAAK;QAC3B,GAAG,AAAC,CAAA,QAAQ,IAAG,IAAK;IACrB;IAGD,kBAAkB;IAClB,IAAI,QAAQ,GACX,OAAO;QACN,MAAM;QACN,GAAG,AAAC,CAAA,AAAC,SAAS,KAAM,IAAG,IAAK;QAC5B,GAAG,AAAC,CAAA,AAAC,SAAS,KAAM,IAAG,IAAK;QAC5B,GAAG,AAAC,CAAA,AAAC,SAAS,IAAK,IAAG,IAAK;QAC3B,OAAO,AAAC,CAAA,QAAQ,IAAG,IAAK;IACzB;AAEF;IAEA,2CAAe;;;AD5Cf,MAAM,4BAAM;AAEZ,MAAM,iCAAW,CAAA;IAChB,IAAI;IACJ,0CAA0C;IAC1C,OAAO,AAAC,CAAA,QAAQ,MAAM,MAAM,0BAAG,IAC5B,CAAA,GAAA,wCAAU,EAAE,SAAS,KAAK,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,UAC7C;AACJ;IAEA,2CAAe;;;AIVf,MAAM,4CAAa,CAAC;AACpB,MAAM,8BAAQ,CAAC;AAEf,MAAM,4CAAU,EAAE;AAClB,MAAM,4CAAgB,CAAC;AAEvB,MAAM,iCAAW,CAAA,IAAK;AAEtB,MAAM,4CAAU,CAAA;IACf,yCAAU,CAAC,WAAW,KAAK,GAAG;QAC7B,GAAG,yCAAU,CAAC,WAAW,KAAK;QAC9B,GAAG,WAAW,MAAM;IACrB;IAEA,OAAO,KAAK,WAAW,YAAY,CAAC,GAAG,QAAQ,CAAA;QAC9C,IAAI,CAAC,yCAAU,CAAC,EAAE,EACjB,yCAAU,CAAC,EAAE,GAAG,CAAC;QAElB,yCAAU,CAAC,EAAE,CAAC,WAAW,KAAK,GAAG,WAAW,QAAQ,CAAC,EAAE;IACxD;IAEA,6BAA6B;IAC7B,IAAI,CAAC,WAAW,QACf,WAAW,SAAS,CAAC;IAGtB,IAAI,CAAC,WAAW,YACf,WAAW,aAAa,CAAC;IAG1B,WAAW,SAAS,QAAQ,CAAA;QAC3B,0DAA0D;QAC1D,IAAI,WAAW,MAAM,CAAC,QAAQ,KAAK,WAClC,WAAW,MAAM,CAAC,QAAQ,GAAG;YAAC;YAAG;SAAE;QAGpC,IAAI,CAAC,WAAW,WAAW,CAAC,QAAQ,EACnC,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,QAAQ,CAAC;QAGvD,IAAI,OAAO,WAAW,WAAW,CAAC,QAAQ,KAAK,YAC9C,WAAW,WAAW,CAAC,QAAQ,GAAG;YACjC,KAAK,WAAW,WAAW,CAAC,QAAQ;QACrC;QAGD,IAAI,CAAC,WAAW,WAAW,CAAC,QAAQ,CAAC,OACpC,WAAW,WAAW,CAAC,QAAQ,CAAC,QAAQ;IAE1C;IAEA,2BAAK,CAAC,WAAW,KAAK,GAAG;IACxB,CAAA,WAAW,SAAS,EAAE,AAAD,EAAG,QAAQ,CAAA;QAChC,0CAAU,QAAQ,WAAW;IAC9B;IAEA,OAAO,CAAA,GAAA,wCAAQ,EAAE,WAAW;AAC7B;AAEA,MAAM,4CAAU,CAAA,OAAQ,2BAAK,CAAC,KAAK;AAEnC,MAAM,4CAAY,CAAC,QAAQ;IAC1B,IAAI,OAAO,WAAW,UAAU;QAC/B,IAAI,CAAC,MACJ,MAAM,IAAI,MAAM,CAAC,yCAAyC,CAAC;QAE5D,yCAAa,CAAC,OAAO,GAAG;IACzB,OAAO,IAAI,OAAO,WAAW,YAC5B;QAAA,IAAI,0CAAQ,QAAQ,UAAU,GAC7B,0CAAQ,KAAK;IACd;AAEF;AAEA,MAAM,4CAAe,CAAA;IACpB,IAAI,OAAO,WAAW,UACrB,OAAO,yCAAa,CAAC,OAAO;SACtB,IAAI,OAAO,WAAW,YAAY;QACxC,MAAM,MAAM,0CAAQ,QAAQ;QAC5B,IAAI,MAAM,GACT,0CAAQ,OAAO,KAAK;IAEtB;AACD;;;;AEnFA,6CAA6C,GAC7C,MAAM,4CAAsB;AAE5B,6CAA6C,GAC7C,MAAM,uCAAiB;AAEhB,MAAM,4CAAM;IAClB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,KAAK;IACL,OAAO;AACR;AAEA,IAAI,2BAAK;AAET;;;CAGC,GACD,SAAS,6BAAO,KAAK;IACpB,IAAI,KAAK,KAAK,CAAC,yBAAG;IAClB,IAAI,MAAM,KAAK,CAAC,2BAAK,EAAE;IACvB,IAAI,OAAO,OAAO,OAAO,KACxB,OAAO,KAAK,KAAK,QAAS,QAAQ,OAAO,KAAK,KAAK,KAAK,CAAC,2BAAK,EAAE;IAEjE,IAAI,OAAO,KACV,OAAO,KAAK,KAAK;IAElB,OAAO,KAAK,KAAK;AAClB;AAEA;;CAEC,GAED,SAAS,+BAAS,KAAK;IACtB,IAAI,4BAAM,MAAM,QACf,OAAO;IAER,IAAI,KAAK,KAAK,CAAC,yBAAG;IAClB,IAAI,0CAAoB,KAAK,KAC5B,OAAO;IAER,IAAI,OAAO,KAAK;QACf,IAAI,MAAM,SAAS,2BAAK,GACvB,OAAO;QAER,IAAI,MAAM,KAAK,CAAC,2BAAK,EAAE;QACvB,IAAI,QAAQ,OAAO,0CAAoB,KAAK,MAC3C,OAAO;QAER,OAAO;IACR;IACA,OAAO;AACR;AAEA;;;CAGC,GAED,MAAM,gCAAU;IACf,KAAK;IACL,KAAK,MAAM,KAAK;IAChB,MAAM;IACN,MAAM;AACP;AAEA,SAAS,0BAAI,KAAK;IACjB,IAAI,QAAQ;IACZ,IAAI,KAAK,CAAC,yBAAG,KAAK,OAAO,KAAK,CAAC,yBAAG,KAAK,KACtC,SAAS,KAAK,CAAC,2BAAK;IAErB,SAAS,6BAAO;IAChB,IAAI,KAAK,CAAC,yBAAG,KAAK,OAAO,KAAK,KAAK,KAAK,CAAC,2BAAK,EAAE,GAC/C,SAAS,KAAK,CAAC,2BAAK,GAAG,6BAAO;IAE/B,IAAI,KAAK,CAAC,yBAAG,KAAK,OAAO,KAAK,CAAC,yBAAG,KAAK,KAAK;QAC3C,IACC,AAAC,CAAA,KAAK,CAAC,2BAAK,EAAE,KAAK,OAAO,KAAK,CAAC,2BAAK,EAAE,KAAK,GAAE,KAC9C,KAAK,KAAK,KAAK,CAAC,2BAAK,EAAE,GAEvB,SAAS,KAAK,CAAC,2BAAK,GAAG,KAAK,CAAC,2BAAK,GAAG,6BAAO;aACtC,IAAI,KAAK,KAAK,KAAK,CAAC,2BAAK,EAAE,GACjC,SAAS,KAAK,CAAC,2BAAK,GAAG,6BAAO;IAEhC;IACA,IAAI,+BAAS,QAAQ;QACpB,IAAI,KAAK,4BAAM;QACf,IAAI,OAAO,SAAS,OAAO,SAAS,OAAO,UAAU,OAAO,QAC3D,OAAO;YAAE,MAAM,0CAAI;YAAK,OAAO,QAAQ,6BAAO,CAAC,GAAG;QAAC;QAEpD,OAAO;IACR;IACA,IAAI,KAAK,CAAC,yBAAG,KAAK,KAAK;QACtB;QACA,OAAO;YAAE,MAAM,0CAAI;YAAY,OAAO,CAAC;QAAM;IAC9C;IACA,OAAO;QAAE,MAAM,0CAAI;QAAQ,OAAO,CAAC;IAAM;AAC1C;AAEA;;CAEC,GACD,SAAS,6BAAO,KAAK;IACpB,IAAI,IAAI;IACR,MAAO,KAAK,KAAK,KAAK,CAAC,yBAAG,EACzB,KAAK,KAAK,CAAC,2BAAK;IAEjB,OAAO;AACR;AAEA;;CAEC,GACD,SAAS,4BAAM,KAAK;IACnB,IAAI,IAAI;IACR,MAAO,2BAAK,MAAM,UAAU,qCAAe,KAAK,KAAK,CAAC,yBAAG,EACxD,KAAK,KAAK,CAAC,2BAAK;IAEjB,OAAO;AACR;AAEA;;CAEC,GACD,SAAS,gCAAU,KAAK;IACvB,IAAI,IAAI,4BAAM;IACd,IAAI,KAAK,CAAC,yBAAG,KAAK,KAAK;QACtB;QACA,OAAO;YAAE,MAAM,0CAAI;YAAU,OAAO;QAAE;IACvC;IACA,IAAI,MAAM,QACT,OAAO;QAAE,MAAM,0CAAI;QAAM,OAAO;IAAU;IAE3C,OAAO;QAAE,MAAM,0CAAI;QAAO,OAAO;IAAE;AACpC;AAEO,SAAS,0CAAS,MAAM,EAAE;IAChC,IAAI,QAAQ,IAAI;IAChB,IAAI,SAAS,EAAE;IACf,IAAI;IAEJ,iBAAiB,GACjB,2BAAK;IAEL,MAAO,2BAAK,MAAM,OAAQ;QACzB,KAAK,KAAK,CAAC,2BAAK;QAEhB;;GAEC,GACD,IAAI,OAAO,QAAQ,OAAO,OAAQ,OAAO,KAAK;YAC7C,MACC,2BAAK,MAAM,UACV,CAAA,KAAK,CAAC,yBAAG,KAAK,QAAQ,KAAK,CAAC,yBAAG,KAAK,OAAQ,KAAK,CAAC,yBAAG,KAAK,GAAE,EAE7D;YAED;QACD;QAEA,IAAI,OAAO,KACV,OAAO;QAGR,IAAI,OAAO,KAAK;YACf,OAAO,KAAK;gBAAE,MAAM,0CAAI;YAAW;YACnC;QACD;QAEA,IAAI,OAAO,KAAK;YACf;YACA,IAAI,6BAAO,QAAQ;gBAClB,OAAO,KAAK,0BAAI;gBAChB;YACD;YACA,OAAO;QACR;QAEA,IAAI,OAAO,KAAK;YACf;YACA,IAAI,6BAAO,QAAQ;gBAClB,OAAO,KAAK,0BAAI;gBAChB;YACD;YACA,IAAI,+BAAS,QAAQ;gBACpB,OAAO,KAAK;oBAAE,MAAM,0CAAI;oBAAO,OAAO,4BAAM;gBAAO;gBACnD;YACD;YACA,OAAO;QACR;QAEA,IAAI,OAAO,KAAK;YACf;YACA,IAAI,6BAAO,QAAQ;gBAClB,OAAO,KAAK,0BAAI;gBAChB;YACD;YACA,OAAO;QACR;QAEA,IAAI,OAAO,KAAK;YACf,MACC,2BAAK,MAAM,UACV,CAAA,KAAK,CAAC,yBAAG,KAAK,QAAQ,KAAK,CAAC,yBAAG,KAAK,OAAQ,KAAK,CAAC,yBAAG,KAAK,GAAE,EAE7D;YAED,IAAI;YACJ,IAAI,6BAAO,QAAQ;gBAClB,QAAQ,0BAAI;gBACZ,IAAI,MAAM,SAAS,0CAAI,KAAK;oBAC3B,OAAO,KAAK;wBAAE,MAAM,0CAAI;wBAAO,OAAO;oBAAM;oBAC5C;gBACD;YACD;YACA,IAAI,+BAAS,QACZ;gBAAA,IAAI,4BAAM,WAAW,QAAQ;oBAC5B,OAAO,KAAK;wBACX,MAAM,0CAAI;wBACV,OAAO;4BAAE,MAAM,0CAAI;4BAAM,OAAO;wBAAU;oBAC3C;oBACA;gBACD;YAAA;YAED,OAAO;QACR;QAEA,IAAI,KAAK,KAAK,KAAK;YAClB;YACA,OAAO,KAAK,0BAAI;YAChB;QACD;QAEA,IAAI,0CAAoB,KAAK,KAAK;YACjC;YACA,OAAO,KAAK,gCAAU;YACtB;QACD;QAEA;;GAEC,GACD,OAAO;IACR;IAEA,OAAO;AACR;AAEO,SAAS,0CAAiB,MAAM;IACtC,OAAO,KAAK;IACZ,IAAI,QAAQ,MAAM,CAAC,OAAO,KAAK;IAC/B,IAAI,CAAC,SAAS,MAAM,SAAS,0CAAI,YAAY,MAAM,UAAU,SAC5D,OAAO;IAER,QAAQ,MAAM,CAAC,OAAO,KAAK;IAC3B,IAAI,MAAM,SAAS,0CAAI,OACtB,OAAO;IAER,MAAM,OAAO,CAAA,GAAA,yCAAY,CAAC,CAAC,MAAM,MAAM;IACvC,IAAI,CAAC,MACJ,OAAO;IAER,MAAM,MAAM;cAAE;IAAK;IACnB,MAAM,SAAS,oCAAc,QAAQ;IACrC,IAAI,CAAC,QACJ,OAAO;IAER,MAAM,WAAW,CAAA,GAAA,yCAAM,EAAE,MAAM;IAC/B,IAAK,IAAI,KAAK,GAAG,GAAG,KAAK,SAAS,QAAQ,KAAM;QAC/C,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,EAAE,SAAS,0CAAI,MAClB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,0CAAI,SAAS,EAAE,QAAQ,EAAE,QAAQ;IAElE;IACA,OAAO;AACR;AAEA,SAAS,oCAAc,MAAM,EAAE,UAAU;IACxC,MAAM,SAAS,EAAE;IACjB,IAAI;IACJ,MAAO,OAAO,KAAK,OAAO,OAAQ;QACjC,QAAQ,MAAM,CAAC,OAAO,KAAK;QAC3B,IACC,MAAM,SAAS,0CAAI,QACnB,MAAM,SAAS,0CAAI,UACnB,MAAM,SAAS,0CAAI,SACnB,MAAM,SAAS,0CAAI,cAClB,cAAc,MAAM,SAAS,0CAAI,KACjC;YACD,OAAO,KAAK;YACZ;QACD;QACA,IAAI,MAAM,SAAS,0CAAI,YAAY;YAClC,IAAI,OAAO,KAAK,OAAO,QACtB,OAAO;YAER;QACD;QACA,OAAO;IACR;IAEA,IAAI,OAAO,SAAS,KAAK,OAAO,SAAS,GACxC,OAAO;IAGR,IAAI,OAAO,WAAW,GAAG;QACxB,IAAI,MAAM,CAAC,EAAE,CAAC,SAAS,0CAAI,OAC1B,OAAO;QAER,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;IACvB;IACA,IAAI,OAAO,WAAW,GACrB,OAAO,KAAK;QAAE,MAAM,0CAAI;QAAM,OAAO;IAAU;IAGhD,OAAO,OAAO,MAAM,CAAA,IAAK,EAAE,SAAS,0CAAI,SAAS,SAAS;AAC3D;AAEO,SAAS,0CAAkB,MAAM,EAAE,UAAU;IACnD,OAAO,KAAK;IACZ,IAAI,QAAQ,MAAM,CAAC,OAAO,KAAK;IAC/B,IAAI,CAAC,SAAS,MAAM,SAAS,0CAAI,UAChC,OAAO;IAER,IAAI,SAAS,oCAAc,QAAQ;IACnC,IAAI,CAAC,QACJ,OAAO;IAER,OAAO,QAAQ,MAAM;IACrB,OAAO;AACR;AAEA,MAAM,8BAAQ,CAAA;IACb,IAAI,OAAO,UAAU,UACpB,OAAO;IAER,MAAM,SAAS,0CAAS;IACxB,MAAM,SAAS,SAAS,0CAAkB,QAAQ,QAAQ;IAC1D,IAAI,SAAS;IACb,IAAI,IAAI;IACR,IAAI,MAAM,CAAA,GAAA,yCAAM,EAAE;IAClB,MAAO,IAAI,IAAK;QACf,IAAI,AAAC,CAAA,SAAS,CAAA,GAAA,yCAAM,CAAC,CAAC,IAAI,CAAC,OAAO,OAAM,MAAO,WAC9C,OAAO;IAET;IACA,OAAO,SAAS,0CAAiB,UAAU;AAC5C;IAEA,2CAAe;;;ADnWf,MAAM,gCAAU,CAAC,OAAO,OACvB,UAAU,YACP,YACA,OAAO,UAAU,WACjB,CAAA,GAAA,wCAAI,EAAE,SACN,MAAM,SAAS,YACf,QACA,OACA;QAAE,GAAG,KAAK;cAAE;IAAK,IACjB;IAEJ,2CAAe;;;AFVf,MAAM,kCACL,CAAC,cAAc,KAAK,GACpB,CAAA,QACC,AAAC,CAAA,QAAQ,CAAA,GAAA,wCAAM,EAAE,OAAO,YAAW,MAAO,YAEvC,MAAM,SAAS,cAEd,QAEF,gCAAgC;QAChC,CAAA,GAAA,yCAAS,CAAC,CAAC,MAAM,KAAK,CAAC,YAAY,GAEjC,CAAA,GAAA,yCAAS,CAAC,CAAC,MAAM,KAAK,CAAC,YAAY,CAAC,SAEtC,+BAA+B;QAC/B,gBAAgB,QAEd,CAAA,GAAA,yCAAS,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,SAE3B,CAAA,GAAA,yCAAS,EAAE,GAAG,CAAC,YAAY,CAAC,CAAA,GAAA,yCAAS,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,UACxD;IAEL,2CAAe;;;AIzBf,kDAAkD;AAElD,MAAM,0BAAI,CAAC,OAAO,YACjB,KAAK,MAAM,QAAS,CAAA,YAAY,KAAK,IAAI,IAAI,UAAS,KAAM;AAE7D,MAAM,8BACL,CAAC,YAAY,CAAC,GACd,CAAA,QACC,OAAO,UAAU,WAAW,wBAAE,OAAO,aAAa;IAEpD,2CAAe;;;;;ALLf,IAAI,oCAAc,CAAA,GAAA,wCAAI,EAAE;AAExB,MAAM,8BAAQ,CAAA,QAAS,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;AAC/C,MAAM,8BAAQ,CAAA,QAAS,KAAK,MAAM,4BAAM,SAAS;AAE1C,MAAM,4CAAe,CAAA;IAC3B,IAAI,UAAU,WACb,OAAO;IAGR,IAAI,IAAI,4BAAM,MAAM;IACpB,IAAI,IAAI,4BAAM,MAAM;IACpB,IAAI,IAAI,4BAAM,MAAM;IAEpB,OAAO,MAAM,AAAC,CAAA,AAAC,WAAY,KAAK,KAAO,KAAK,IAAK,CAAA,EAAG,SAAS,IAAI,MAAM;AACxE;AAEO,MAAM,4CAAgB,CAAA;IAC5B,IAAI,UAAU,WACb,OAAO;IAGR,IAAI,IAAI,4BAAM,MAAM,UAAU,YAAY,MAAM,QAAQ;IACxD,OAAO,0CAAa,SAAS,AAAC,CAAA,AAAC,MAAU,CAAA,EAAG,SAAS,IAAI,MAAM;AAChE;AAEO,MAAM,4CAAe,CAAA;IAC3B,IAAI,UAAU,WACb,OAAO;IAGR,IAAI,IAAI,MAAM,MAAM,YAAY,4BAAM,MAAM,KAAK;IACjD,IAAI,IAAI,MAAM,MAAM,YAAY,4BAAM,MAAM,KAAK;IACjD,IAAI,IAAI,MAAM,MAAM,YAAY,4BAAM,MAAM,KAAK;IAEjD,IAAI,MAAM,UAAU,aAAa,MAAM,UAAU,GAChD,eAAe;IACf,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SAE9B,oBAAoB;IACpB,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,kCAAY,4BAAM,MAAM,QAAQ,CAAC,CAAC;AAErE;AAEO,MAAM,4CAAe,CAAA;IAC3B,IAAI,UAAU,WACb,OAAO;IAGR,MAAM,IAAI,kCAAY,MAAM,KAAK;IACjC,MAAM,IACL,MAAM,MAAM,YACT,kCAAY,4BAAM,MAAM,KAAK,OAAO,MACpC;IACJ,MAAM,IACL,MAAM,MAAM,YACT,kCAAY,4BAAM,MAAM,KAAK,OAAO,MACpC;IAEJ,IAAI,MAAM,UAAU,aAAa,MAAM,UAAU,GAChD,eAAe;IACf,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SAE9B,oBAAoB;IACpB,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,kCAAY,4BAAM,MAAM,QAAQ,CAAC,CAAC;AAErE;AAEO,MAAM,4CAAY,CAAA;IACxB,MAAM,QAAQ,CAAA,GAAA,wCAAM,EAAE;IACtB,IAAI,CAAC,OACJ,OAAO;IAER,MAAM,MAAM,CAAA,GAAA,yCAAM,EAAE,MAAM;IAC1B,IAAI,CAAC,IAAI,aAAa,OAAO,IAAI,cAAc,UAAU;QACxD,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,aAAa,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,SAAS,QAAQ,CAAC,IAAI;YACzB,IAAI,OAAO,SACV,OACC,AAAC,CAAA,IAAI,MAAM,EAAC,IACX,CAAA,KAAK,CAAC,GAAG,KAAK,YAAY,KAAK,CAAC,GAAG,GAAG,MAAK;QAE/C;QACA,IAAI,MAAM,UAAU,aAAa,MAAM,QAAQ,GAC9C,OAAO,CAAC,GAAG,EAAE,MAAM,MAAM,CAAC;QAE3B,OAAO,MAAM;IACd;IACA,IAAI,OAAO,IAAI,cAAc,YAC5B,OAAO,IAAI,UAAU;IAEtB,OAAO;AACR;AAEO,MAAM,4CAAY,CAAA,IAAK,0CAAa,CAAA,GAAA,wCAAQ,EAAE,OAAO;AACrD,MAAM,4CAAa,CAAA,IAAK,0CAAc,CAAA,GAAA,wCAAQ,EAAE,OAAO;AACvD,MAAM,4CAAY,CAAA,IAAK,0CAAa,CAAA,GAAA,wCAAQ,EAAE,OAAO;AACrD,MAAM,4CAAY,CAAA,IAAK,0CAAa,CAAA,GAAA,wCAAQ,EAAE,OAAO;;;AJpG5D,QAAQ,IACP,CAAA,GAAA,yCAAW,EACV,CAAA,GAAA,wCAAc,EACb,CAAA,GAAA,wCAAO,EAAE", "sources": ["test/tree-shaking/tree-shaken.js", "src/hsl/convertRgbToHsl.js", "src/rgb/parseHex.js", "src/rgb/parseNumber.js", "src/formatter.js", "src/converter.js", "src/modes.js", "src/_prepare.js", "src/parse.js", "src/round.js"], "sourcesContent": ["import { parseHex, convertRgbToHsl, serializeHsl } from '../../src/index-fn.js';\n\nconsole.log(\n\tserializeHsl(\n\t\tconvertRgbToHsl(\n\t\t\tparseHex('#ffcc00')\n\t\t)\n\t)\n);", "// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Formal_derivation\n\nexport default function convertRgbToHsl({ r, g, b, alpha }) {\n\tlet M = Math.max(r, g, b),\n\t\tm = Math.min(r, g, b);\n\tlet res = {\n\t\tmode: 'hsl',\n\t\ts: M === m ? 0 : (M - m) / (1 - Math.abs(M + m - 1)),\n\t\tl: 0.5 * (M + m)\n\t};\n\tif (M - m !== 0)\n\t\tres.h =\n\t\t\t(M === r\n\t\t\t\t? (g - b) / (M - m) + (g < b) * 6\n\t\t\t\t: M === g\n\t\t\t\t? (b - r) / (M - m) + 2\n\t\t\t\t: (r - g) / (M - m) + 4) * 60;\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n}\n", "import parseNumber from './parseNumber.js';\n\nconst hex = /^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i;\n\nconst parseHex = color => {\n\tlet match;\n\t// eslint-disable-next-line no-cond-assign\n\treturn (match = color.match(hex))\n\t\t? parseNumber(parseInt(match[1], 16), match[1].length)\n\t\t: undefined;\n};\n\nexport default parseHex;\n", "const parseNumber = (color, len) => {\n\tif (typeof color !== 'number') return;\n\n\t// hex3: #c93 -> #cc9933\n\tif (len === 3) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: (((color >> 8) & 0xf) | ((color >> 4) & 0xf0)) / 255,\n\t\t\tg: (((color >> 4) & 0xf) | (color & 0xf0)) / 255,\n\t\t\tb: ((color & 0xf) | ((color << 4) & 0xf0)) / 255\n\t\t};\n\t}\n\n\t// hex4: #c931 -> #cc993311\n\tif (len === 4) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: (((color >> 12) & 0xf) | ((color >> 8) & 0xf0)) / 255,\n\t\t\tg: (((color >> 8) & 0xf) | ((color >> 4) & 0xf0)) / 255,\n\t\t\tb: (((color >> 4) & 0xf) | (color & 0xf0)) / 255,\n\t\t\talpha: ((color & 0xf) | ((color << 4) & 0xf0)) / 255\n\t\t};\n\t}\n\n\t// hex6: #f0f1f2\n\tif (len === 6) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: ((color >> 16) & 0xff) / 255,\n\t\t\tg: ((color >> 8) & 0xff) / 255,\n\t\t\tb: (color & 0xff) / 255\n\t\t};\n\t}\n\n\t// hex8: #f0f1f2ff\n\tif (len === 8) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: ((color >> 24) & 0xff) / 255,\n\t\t\tg: ((color >> 16) & 0xff) / 255,\n\t\t\tb: ((color >> 8) & 0xff) / 255,\n\t\t\talpha: (color & 0xff) / 255\n\t\t};\n\t}\n};\n\nexport default parseNumber;\n", "import converter from './converter.js';\nimport round from './round.js';\nimport prepare from './_prepare.js';\nimport { getMode } from './modes.js';\n\nlet twoDecimals = round(2);\n\nconst clamp = value => Math.max(0, Math.min(1, value));\nconst fixup = value => Math.round(clamp(value) * 255);\n\nexport const serializeHex = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet r = fixup(color.r);\n\tlet g = fixup(color.g);\n\tlet b = fixup(color.b);\n\n\treturn '#' + ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1);\n};\n\nexport const serializeHex8 = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet a = fixup(color.alpha !== undefined ? color.alpha : 1);\n\treturn serializeHex(color) + ((1 << 8) | a).toString(16).slice(1);\n};\n\nexport const serializeRgb = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet r = color.r !== undefined ? fixup(color.r) : 'none';\n\tlet g = color.g !== undefined ? fixup(color.g) : 'none';\n\tlet b = color.b !== undefined ? fixup(color.b) : 'none';\n\n\tif (color.alpha === undefined || color.alpha === 1) {\n\t\t// opaque color\n\t\treturn `rgb(${r}, ${g}, ${b})`;\n\t} else {\n\t\t// transparent color\n\t\treturn `rgba(${r}, ${g}, ${b}, ${twoDecimals(clamp(color.alpha))})`;\n\t}\n};\n\nexport const serializeHsl = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tconst h = twoDecimals(color.h || 0);\n\tconst s =\n\t\tcolor.s !== undefined\n\t\t\t? twoDecimals(clamp(color.s) * 100) + '%'\n\t\t\t: 'none';\n\tconst l =\n\t\tcolor.l !== undefined\n\t\t\t? twoDecimals(clamp(color.l) * 100) + '%'\n\t\t\t: 'none';\n\n\tif (color.alpha === undefined || color.alpha === 1) {\n\t\t// opaque color\n\t\treturn `hsl(${h}, ${s}, ${l})`;\n\t} else {\n\t\t// transparent color\n\t\treturn `hsla(${h}, ${s}, ${l}, ${twoDecimals(clamp(color.alpha))})`;\n\t}\n};\n\nexport const formatCss = c => {\n\tconst color = prepare(c);\n\tif (!color) {\n\t\treturn undefined;\n\t}\n\tconst def = getMode(color.mode);\n\tif (!def.serialize || typeof def.serialize === 'string') {\n\t\tlet res = `color(${def.serialize || `--${color.mode}`} `;\n\t\tdef.channels.forEach((ch, i) => {\n\t\t\tif (ch !== 'alpha') {\n\t\t\t\tres +=\n\t\t\t\t\t(i ? ' ' : '') +\n\t\t\t\t\t(color[ch] !== undefined ? color[ch] : 'none');\n\t\t\t}\n\t\t});\n\t\tif (color.alpha !== undefined && color.alpha < 1) {\n\t\t\tres += ` / ${color.alpha}`;\n\t\t}\n\t\treturn res + ')';\n\t}\n\tif (typeof def.serialize === 'function') {\n\t\treturn def.serialize(color);\n\t}\n\treturn undefined;\n};\n\nexport const formatHex = c => serializeHex(converter('rgb')(c));\nexport const formatHex8 = c => serializeHex8(converter('rgb')(c));\nexport const formatRgb = c => serializeRgb(converter('rgb')(c));\nexport const formatHsl = c => serializeHsl(converter('hsl')(c));\n", "import { converters } from './modes.js';\nimport prepare from './_prepare.js';\n\nconst converter =\n\t(target_mode = 'rgb') =>\n\tcolor =>\n\t\t(color = prepare(color, target_mode)) !== undefined\n\t\t\t? // if the color's mode corresponds to our target mode\n\t\t\t  color.mode === target_mode\n\t\t\t\t? // then just return the color\n\t\t\t\t  color\n\t\t\t\t: // otherwise check to see if we have a dedicated\n\t\t\t\t// converter for the target mode\n\t\t\t\tconverters[color.mode][target_mode]\n\t\t\t\t? // and return its result...\n\t\t\t\t  converters[color.mode][target_mode](color)\n\t\t\t\t: // ...otherwise pass through RGB as an intermediary step.\n\t\t\t\t// if the target mode is RGB...\n\t\t\t\ttarget_mode === 'rgb'\n\t\t\t\t? // just return the RGB\n\t\t\t\t  converters[color.mode].rgb(color)\n\t\t\t\t: // otherwise convert color.mode -> RGB -> target_mode\n\t\t\t\t  converters.rgb[target_mode](converters[color.mode].rgb(color))\n\t\t\t: undefined;\n\nexport default converter;\n", "import converter from './converter.js';\n\nconst converters = {};\nconst modes = {};\n\nconst parsers = [];\nconst colorProfiles = {};\n\nconst identity = v => v;\n\nconst useMode = definition => {\n\tconverters[definition.mode] = {\n\t\t...converters[definition.mode],\n\t\t...definition.toMode\n\t};\n\n\tObject.keys(definition.fromMode || {}).forEach(k => {\n\t\tif (!converters[k]) {\n\t\t\tconverters[k] = {};\n\t\t}\n\t\tconverters[k][definition.mode] = definition.fromMode[k];\n\t});\n\n\t// Color space channel ranges\n\tif (!definition.ranges) {\n\t\tdefinition.ranges = {};\n\t}\n\n\tif (!definition.difference) {\n\t\tdefinition.difference = {};\n\t}\n\n\tdefinition.channels.forEach(channel => {\n\t\t// undefined channel ranges default to the [0, 1] interval\n\t\tif (definition.ranges[channel] === undefined) {\n\t\t\tdefinition.ranges[channel] = [0, 1];\n\t\t}\n\n\t\tif (!definition.interpolate[channel]) {\n\t\t\tthrow new Error(`Missing interpolator for: ${channel}`);\n\t\t}\n\n\t\tif (typeof definition.interpolate[channel] === 'function') {\n\t\t\tdefinition.interpolate[channel] = {\n\t\t\t\tuse: definition.interpolate[channel]\n\t\t\t};\n\t\t}\n\n\t\tif (!definition.interpolate[channel].fixup) {\n\t\t\tdefinition.interpolate[channel].fixup = identity;\n\t\t}\n\t});\n\n\tmodes[definition.mode] = definition;\n\t(definition.parse || []).forEach(parser => {\n\t\tuseParser(parser, definition.mode);\n\t});\n\n\treturn converter(definition.mode);\n};\n\nconst getMode = mode => modes[mode];\n\nconst useParser = (parser, mode) => {\n\tif (typeof parser === 'string') {\n\t\tif (!mode) {\n\t\t\tthrow new Error(`'mode' required when 'parser' is a string`);\n\t\t}\n\t\tcolorProfiles[parser] = mode;\n\t} else if (typeof parser === 'function') {\n\t\tif (parsers.indexOf(parser) < 0) {\n\t\t\tparsers.push(parser);\n\t\t}\n\t}\n};\n\nconst removeParser = parser => {\n\tif (typeof parser === 'string') {\n\t\tdelete colorProfiles[parser];\n\t} else if (typeof parser === 'function') {\n\t\tconst idx = parsers.indexOf(parser);\n\t\tif (idx > 0) {\n\t\t\tparsers.splice(idx, 1);\n\t\t}\n\t}\n};\n\nexport {\n\tuseMode,\n\tgetMode,\n\tuseParser,\n\tremoveParser,\n\tconverters,\n\tparsers,\n\tcolorProfiles\n};\n", "import parse from './parse.js';\n\nconst prepare = (color, mode) =>\n\tcolor === undefined\n\t\t? undefined\n\t\t: typeof color !== 'object'\n\t\t? parse(color)\n\t\t: color.mode !== undefined\n\t\t? color\n\t\t: mode\n\t\t? { ...color, mode }\n\t\t: undefined;\n\nexport default prepare;\n", "import { parsers, colorProfiles, getMode } from './modes.js';\n\n/* eslint-disable-next-line no-control-regex */\nconst IdentStartCodePoint = /[^\\x00-\\x7F]|[a-zA-Z_]/;\n\n/* eslint-disable-next-line no-control-regex */\nconst IdentCodePoint = /[^\\x00-\\x7F]|[-\\w]/;\n\nexport const Tok = {\n\tFunction: 'function',\n\tIdent: 'ident',\n\tNumber: 'number',\n\tPercentage: 'percentage',\n\tParenClose: ')',\n\tNone: 'none',\n\tHue: 'hue',\n\tAlpha: 'alpha'\n};\n\nlet _i = 0;\n\n/*\n\t4.3.10. Check if three code points would start a number\n\thttps://drafts.csswg.org/css-syntax/#starts-with-a-number\n */\nfunction is_num(chars) {\n\tlet ch = chars[_i];\n\tlet ch1 = chars[_i + 1];\n\tif (ch === '-' || ch === '+') {\n\t\treturn /\\d/.test(ch1) || (ch1 === '.' && /\\d/.test(chars[_i + 2]));\n\t}\n\tif (ch === '.') {\n\t\treturn /\\d/.test(ch1);\n\t}\n\treturn /\\d/.test(ch);\n}\n\n/*\n\tCheck if the stream starts with an identifier.\n */\n\nfunction is_ident(chars) {\n\tif (_i >= chars.length) {\n\t\treturn false;\n\t}\n\tlet ch = chars[_i];\n\tif (IdentStartCodePoint.test(ch)) {\n\t\treturn true;\n\t}\n\tif (ch === '-') {\n\t\tif (chars.length - _i < 2) {\n\t\t\treturn false;\n\t\t}\n\t\tlet ch1 = chars[_i + 1];\n\t\tif (ch1 === '-' || IdentStartCodePoint.test(ch1)) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\treturn false;\n}\n\n/*\n\t4.3.3. Consume a numeric token\n\thttps://drafts.csswg.org/css-syntax/#consume-numeric-token\n */\n\nconst huenits = {\n\tdeg: 1,\n\trad: 180 / Math.PI,\n\tgrad: 9 / 10,\n\tturn: 360\n};\n\nfunction num(chars) {\n\tlet value = '';\n\tif (chars[_i] === '-' || chars[_i] === '+') {\n\t\tvalue += chars[_i++];\n\t}\n\tvalue += digits(chars);\n\tif (chars[_i] === '.' && /\\d/.test(chars[_i + 1])) {\n\t\tvalue += chars[_i++] + digits(chars);\n\t}\n\tif (chars[_i] === 'e' || chars[_i] === 'E') {\n\t\tif (\n\t\t\t(chars[_i + 1] === '-' || chars[_i + 1] === '+') &&\n\t\t\t/\\d/.test(chars[_i + 2])\n\t\t) {\n\t\t\tvalue += chars[_i++] + chars[_i++] + digits(chars);\n\t\t} else if (/\\d/.test(chars[_i + 1])) {\n\t\t\tvalue += chars[_i++] + digits(chars);\n\t\t}\n\t}\n\tif (is_ident(chars)) {\n\t\tlet id = ident(chars);\n\t\tif (id === 'deg' || id === 'rad' || id === 'turn' || id === 'grad') {\n\t\t\treturn { type: Tok.Hue, value: value * huenits[id] };\n\t\t}\n\t\treturn undefined;\n\t}\n\tif (chars[_i] === '%') {\n\t\t_i++;\n\t\treturn { type: Tok.Percentage, value: +value };\n\t}\n\treturn { type: Tok.Number, value: +value };\n}\n\n/*\n\tConsume digits.\n */\nfunction digits(chars) {\n\tlet v = '';\n\twhile (/\\d/.test(chars[_i])) {\n\t\tv += chars[_i++];\n\t}\n\treturn v;\n}\n\n/*\n\tConsume an identifier.\n */\nfunction ident(chars) {\n\tlet v = '';\n\twhile (_i < chars.length && IdentCodePoint.test(chars[_i])) {\n\t\tv += chars[_i++];\n\t}\n\treturn v;\n}\n\n/*\n\tConsume an ident-like token.\n */\nfunction identlike(chars) {\n\tlet v = ident(chars);\n\tif (chars[_i] === '(') {\n\t\t_i++;\n\t\treturn { type: Tok.Function, value: v };\n\t}\n\tif (v === 'none') {\n\t\treturn { type: Tok.None, value: undefined };\n\t}\n\treturn { type: Tok.Ident, value: v };\n}\n\nexport function tokenize(str = '') {\n\tlet chars = str.trim();\n\tlet tokens = [];\n\tlet ch;\n\n\t/* reset counter */\n\t_i = 0;\n\n\twhile (_i < chars.length) {\n\t\tch = chars[_i++];\n\n\t\t/*\n\t\t\tConsume whitespace without emitting it\n\t\t */\n\t\tif (ch === '\\n' || ch === '\\t' || ch === ' ') {\n\t\t\twhile (\n\t\t\t\t_i < chars.length &&\n\t\t\t\t(chars[_i] === '\\n' || chars[_i] === '\\t' || chars[_i] === ' ')\n\t\t\t) {\n\t\t\t\t_i++;\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (ch === ',') {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === ')') {\n\t\t\ttokens.push({ type: Tok.ParenClose });\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (ch === '+') {\n\t\t\t_i--;\n\t\t\tif (is_num(chars)) {\n\t\t\t\ttokens.push(num(chars));\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === '-') {\n\t\t\t_i--;\n\t\t\tif (is_num(chars)) {\n\t\t\t\ttokens.push(num(chars));\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (is_ident(chars)) {\n\t\t\t\ttokens.push({ type: Tok.Ident, value: ident(chars) });\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === '.') {\n\t\t\t_i--;\n\t\t\tif (is_num(chars)) {\n\t\t\t\ttokens.push(num(chars));\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === '/') {\n\t\t\twhile (\n\t\t\t\t_i < chars.length &&\n\t\t\t\t(chars[_i] === '\\n' || chars[_i] === '\\t' || chars[_i] === ' ')\n\t\t\t) {\n\t\t\t\t_i++;\n\t\t\t}\n\t\t\tlet alpha;\n\t\t\tif (is_num(chars)) {\n\t\t\t\talpha = num(chars);\n\t\t\t\tif (alpha.type !== Tok.Hue) {\n\t\t\t\t\ttokens.push({ type: Tok.Alpha, value: alpha });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (is_ident(chars)) {\n\t\t\t\tif (ident(chars) === 'none') {\n\t\t\t\t\ttokens.push({\n\t\t\t\t\t\ttype: Tok.Alpha,\n\t\t\t\t\t\tvalue: { type: Tok.None, value: undefined }\n\t\t\t\t\t});\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (/\\d/.test(ch)) {\n\t\t\t_i--;\n\t\t\ttokens.push(num(chars));\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (IdentStartCodePoint.test(ch)) {\n\t\t\t_i--;\n\t\t\ttokens.push(identlike(chars));\n\t\t\tcontinue;\n\t\t}\n\n\t\t/*\n\t\t\tTreat everything not already handled as an error.\n\t\t */\n\t\treturn undefined;\n\t}\n\n\treturn tokens;\n}\n\nexport function parseColorSyntax(tokens) {\n\ttokens._i = 0;\n\tlet token = tokens[tokens._i++];\n\tif (!token || token.type !== Tok.Function || token.value !== 'color') {\n\t\treturn undefined;\n\t}\n\ttoken = tokens[tokens._i++];\n\tif (token.type !== Tok.Ident) {\n\t\treturn undefined;\n\t}\n\tconst mode = colorProfiles[token.value];\n\tif (!mode) {\n\t\treturn undefined;\n\t}\n\tconst res = { mode };\n\tconst coords = consumeCoords(tokens, false);\n\tif (!coords) {\n\t\treturn undefined;\n\t}\n\tconst channels = getMode(mode).channels;\n\tfor (let ii = 0, c; ii < channels.length; ii++) {\n\t\tc = coords[ii];\n\t\tif (c.type !== Tok.None) {\n\t\t\tres[channels[ii]] = c.type === Tok.Number ? c.value : c.value / 100;\n\t\t}\n\t}\n\treturn res;\n}\n\nfunction consumeCoords(tokens, includeHue) {\n\tconst coords = [];\n\tlet token;\n\twhile (tokens._i < tokens.length) {\n\t\ttoken = tokens[tokens._i++];\n\t\tif (\n\t\t\ttoken.type === Tok.None ||\n\t\t\ttoken.type === Tok.Number ||\n\t\t\ttoken.type === Tok.Alpha ||\n\t\t\ttoken.type === Tok.Percentage ||\n\t\t\t(includeHue && token.type === Tok.Hue)\n\t\t) {\n\t\t\tcoords.push(token);\n\t\t\tcontinue;\n\t\t}\n\t\tif (token.type === Tok.ParenClose) {\n\t\t\tif (tokens._i < tokens.length) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tif (coords.length < 3 || coords.length > 4) {\n\t\treturn undefined;\n\t}\n\n\tif (coords.length === 4) {\n\t\tif (coords[3].type !== Tok.Alpha) {\n\t\t\treturn undefined;\n\t\t}\n\t\tcoords[3] = coords[3].value;\n\t}\n\tif (coords.length === 3) {\n\t\tcoords.push({ type: Tok.None, value: undefined });\n\t}\n\n\treturn coords.every(c => c.type !== Tok.Alpha) ? coords : undefined;\n}\n\nexport function parseModernSyntax(tokens, includeHue) {\n\ttokens._i = 0;\n\tlet token = tokens[tokens._i++];\n\tif (!token || token.type !== Tok.Function) {\n\t\treturn undefined;\n\t}\n\tlet coords = consumeCoords(tokens, includeHue);\n\tif (!coords) {\n\t\treturn undefined;\n\t}\n\tcoords.unshift(token.value);\n\treturn coords;\n}\n\nconst parse = color => {\n\tif (typeof color !== 'string') {\n\t\treturn undefined;\n\t}\n\tconst tokens = tokenize(color);\n\tconst parsed = tokens ? parseModernSyntax(tokens, true) : undefined;\n\tlet result = undefined;\n\tlet i = 0;\n\tlet len = parsers.length;\n\twhile (i < len) {\n\t\tif ((result = parsers[i++](color, parsed)) !== undefined) {\n\t\t\treturn result;\n\t\t}\n\t}\n\treturn tokens ? parseColorSyntax(tokens) : undefined;\n};\n\nexport default parse;\n", "// From: https://github.com/d3/d3-format/issues/32\n\nconst r = (value, precision) =>\n\tMath.round(value * (precision = Math.pow(10, precision))) / precision;\n\nconst round =\n\t(precision = 4) =>\n\tvalue =>\n\t\ttypeof value === 'number' ? r(value, precision) : value;\n\nexport default round;\n"], "names": [], "version": 3, "file": "culori.min.mjs.map"}