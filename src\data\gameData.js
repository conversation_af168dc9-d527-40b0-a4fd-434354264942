export const ATTRIBUTES = {
  STR: '力量',
  AGI: '敏捷',
  CON: '体质',
  INT: '智力',
  WIS: '感知',
  CHA: '魅力'
};

// --- RATING SYSTEM ---
// Recalibrated thresholds to make higher ratings rarer. All values are now integers.
export const RATING_THRESHOLDS = {
  attribute: [ {t:30,r:'SSS'}, {t:26,r:'SS'}, {t:22,r:'S'}, {t:18,r:'A'}, {t:15,r:'B'}, {t:12,r:'C'}, {t:9,r:'D'}, {t:0,r:'E'} ],
  growth:    [ {t:5,r:'SSS'}, {t:4,r:'SS'}, {t:3,r:'S'}, {t:3,r:'A'}, {t:2,r:'B'}, {t:2,r:'C'}, {t:1,r:'D'}, {t:0,r:'E'} ],
};

export const getRating = (value, type = 'attribute') => {
  for (const {t, r} of RATING_THRESHOLDS[type]) {
    if (value >= t) return r;
  }
  return 'E'; // Default lowest rating
};

// --- PROFESSIONS ---
// Lowered base stats to emphasize the impact of bloodlines. All values are now integers.
export const PROFESSIONS = {
  WARRIOR: {
    name: '战士',
    description: '精通各种武器与战斗技巧，拥有强大的力量和体魄。',
    initial_attributes: { STR: 14, AGI: 10, CON: 12, INT: 7, WIS: 8, CHA: 9 },
    growth_values: { STR: 3, AGI: 1, CON: 2, INT: 1, WIS: 1, CHA: 1 }
  },
  SCOUT: {
    name: '斥候',
    description: '行动敏捷，善于侦察与陷阱，在野外生存能力极强。',
    initial_attributes: { STR: 9, AGI: 14, CON: 9, INT: 10, WIS: 13, CHA: 8 },
    growth_values: { STR: 1, AGI: 3, CON: 1, INT: 1, WIS: 2, CHA: 1 }
  },
  PRIEST: {
    name: '祭司',
    description: '与神灵沟通，使用神圣的力量治疗盟友或惩戒敌人。',
    initial_attributes: { STR: 7, AGI: 8, CON: 9, INT: 13, WIS: 14, CHA: 12 },
    growth_values: { STR: 1, AGI: 1, CON: 1, INT: 3, WIS: 3, CHA: 2 }
  },
  HUNTER: {
    name: '猎人',
    description: '优秀的射手和追踪者，与野兽为伴，熟悉自然。',
    initial_attributes: { STR: 11, AGI: 13, CON: 11, INT: 8, WIS: 12, CHA: 7 },
    growth_values: { STR: 2, AGI: 2, CON: 1, INT: 1, WIS: 2, CHA: 1 }
  }
};

// --- BLOODLINES ---
// Rarity is a direct probability weight. Higher is more common. All values are now integers.
export const BLOODLINES = {
  // Common Bloodlines (Approx. 45% total chance)
  JISESI: {
    name: '脊塞斯人',
    rarity: 200,
    description: '一个古老而骄傲的民族，以其坚韧的意志和强健的体魄著称。',
    attribute_modifiers: { STR: 2, CON: 1, AGI: 0, INT: 0, WIS: 0, CHA: 0 },
    growth_modifiers: { STR: 1, CON: 1, AGI: 0, INT: 0, WIS: 0, CHA: 0 }
  },
  NORTHMAN: {
    name: '北境人',
    rarity: 200,
    description: '生活在寒冷北方的坚毅民族，适应了严酷的环境。',
    attribute_modifiers: { CON: 2, STR: 1, WIS: 0, AGI: 0, INT: -1, CHA: 0 },
    growth_modifiers: { CON: 1, STR: 1, AGI: 0, INT: 0, WIS: 0, CHA: 0 }
  },
  PLAINSMAN: {
    name: '平原民',
    rarity: 180,
    description: '生活在广阔草原上的游牧民族，擅长骑射和驯兽。',
    attribute_modifiers: { AGI: 2, WIS: 1, STR: 0, CON: 0, INT: 0, CHA: 0 },
    growth_modifiers: { AGI: 1, WIS: 1, STR: 0, CON: 0, INT: 0, CHA: 0 }
  },
  ISLANDER: {
    name: '海岛民',
    rarity: 180,
    description: '居住在海岛上的民族，善于航海和贸易，性格开朗。',
    attribute_modifiers: { CHA: 2, AGI: 1, STR: 0, CON: 0, INT: 0, WIS: 0 },
    growth_modifiers: { CHA: 1, AGI: 1, STR: 0, CON: 0, INT: 0, WIS: 0 }
  },
  SCHOLAR: {
    name: '学者族',
    rarity: 160,
    description: '崇尚知识的民族，世代传承着古老的智慧和学问。',
    attribute_modifiers: { INT: 2, WIS: 1, STR: -1, CON: 0, AGI: 0, CHA: 0 },
    growth_modifiers: { INT: 1, WIS: 1, STR: 0, CON: 0, AGI: 0, CHA: 0 }
  },

  // Uncommon Bloodlines (Approx. 30% total chance)
  ELF: {
    name: '精灵',
    rarity: 120,
    description: '森林的宠儿，身手敏捷，与自然有天生的亲和力。',
    attribute_modifiers: { AGI: 2, WIS: 1, STR: -1, CON: -1, INT: 0, CHA: 1 },
    growth_modifiers: { AGI: 1, WIS: 1, STR: 0, CON: 0, INT: 0, CHA: 1 }
  },
  DWARF: {
    name: '矮人',
    rarity: 120,
    description: '山脉的子民，强壮、坚韧，是天生的工匠和战士。',
    attribute_modifiers: { STR: 1, CON: 2, AGI: -1, CHA: -1, INT: 0, WIS: 0 },
    growth_modifiers: { STR: 1, CON: 1, AGI: 0, INT: 0, WIS: 0, CHA: 0 }
  },
  HALFLING: {
    name: '半身人',
    rarity: 100,
    description: '身材矮小但心灵纯净的民族，拥有惊人的运气和勇气。',
    attribute_modifiers: { AGI: 1, CHA: 2, CON: 1, STR: -2, INT: 0, WIS: 0 },
    growth_modifiers: { AGI: 1, CHA: 1, CON: 1, STR: 0, INT: 0, WIS: 0 }
  },
  BEASTKIN: {
    name: '兽人',
    rarity: 100,
    description: '拥有野兽特征的强壮民族，保持着原始的野性和力量。',
    attribute_modifiers: { STR: 3, CON: 2, AGI: 1, INT: -2, WIS: 0, CHA: -1 },
    growth_modifiers: { STR: 1, CON: 1, AGI: 1, INT: 0, WIS: 0, CHA: 0 }
  },
  DARKELVES: {
    name: '暗精灵',
    rarity: 80,
    description: '居住在地下的精灵分支，掌握着黑暗魔法和诡计。',
    attribute_modifiers: { INT: 2, AGI: 2, CHA: 1, STR: -1, CON: -1, WIS: -1 },
    growth_modifiers: { INT: 1, AGI: 1, CHA: 1, STR: 0, CON: 0, WIS: 0 }
  },

  // Rare Bloodlines (Approx. 15% total chance)
  DRAGONBORN: {
    name: '龙裔',
    rarity: 40,
    description: '流淌着巨龙之血的后裔，天生就拥有强大的力量和魅力。',
    attribute_modifiers: { STR: 4, CHA: 3, AGI: -1, INT: 1, CON: 2, WIS: 0 },
    growth_modifiers: { STR: 2, CHA: 1, CON: 1, INT: 1, AGI: 0, WIS: 0 }
  },
  GIANTKIN: {
    name: '巨人后裔',
    rarity: 35,
    description: '拥有巨人血脉的凡人，身材高大，力量惊人。',
    attribute_modifiers: { STR: 5, CON: 4, AGI: -3, INT: -3, WIS: 0, CHA: 0 },
    growth_modifiers: { STR: 2, CON: 2, AGI: 0, INT: 0, WIS: 0, CHA: 0 }
  },
  PHOENIX: {
    name: '凤凰血脉',
    rarity: 30,
    description: '传说中不死鸟的后裔，拥有重生的力量和炽热的意志。',
    attribute_modifiers: { CHA: 4, WIS: 3, CON: 2, STR: 0, AGI: 1, INT: 1 },
    growth_modifiers: { CHA: 2, WIS: 1, CON: 1, STR: 0, AGI: 1, INT: 1 }
  },
  VAMPIRE: {
    name: '血族',
    rarity: 25,
    description: '夜晚的统治者，拥有永恒的生命和诱人的魅力。',
    attribute_modifiers: { CHA: 3, AGI: 3, INT: 2, STR: 1, CON: -2, WIS: 0 },
    growth_modifiers: { CHA: 1, AGI: 1, INT: 1, STR: 1, CON: 0, WIS: 0 }
  },
  ELEMENTAL: {
    name: '元素之子',
    rarity: 25,
    description: '与元素力量融合的存在，掌握着自然的原始力量。',
    attribute_modifiers: { INT: 3, WIS: 3, AGI: 2, STR: 0, CON: 1, CHA: 0 },
    growth_modifiers: { INT: 1, WIS: 1, AGI: 1, STR: 0, CON: 1, CHA: 0 }
  },

  // Legendary Bloodlines (Approx. 5% total chance)
  CELESTIAL: {
    name: '神民',
    rarity: 15,
    description: '神明在人间的行走，拥有超凡的智慧和感知，受众生敬仰。',
    attribute_modifiers: { INT: 5, WIS: 5, CHA: 4, STR: -2, CON: -2, AGI: -1 },
    growth_modifiers: { INT: 2, WIS: 2, CHA: 2, STR: 0, CON: 0, AGI: 0 }
  },
  ABYSSAL: {
    name: '深渊之子',
    rarity: 15,
    description: '被深渊力量侵染的血脉，获得了混乱而强大的力量。',
    attribute_modifiers: { STR: 4, INT: 4, CON: 3, CHA: -2, WIS: -2, AGI: 0 },
    growth_modifiers: { STR: 2, INT: 2, CON: 1, CHA: 0, WIS: 0, AGI: 0 }
  },
  ANCIENT: {
    name: '远古血脉',
    rarity: 10,
    description: '来自远古时代的神秘血脉，蕴含着被遗忘的强大力量。',
    attribute_modifiers: { STR: 3, AGI: 3, CON: 3, INT: 3, WIS: 3, CHA: 3 },
    growth_modifiers: { STR: 1, AGI: 1, CON: 1, INT: 1, WIS: 1, CHA: 1 }
  },
  VOID: {
    name: '虚空行者',
    rarity: 8,
    description: '来自虚空的神秘存在，超越了常人的理解范畴。',
    attribute_modifiers: { INT: 6, WIS: 4, AGI: 3, STR: -1, CON: -1, CHA: 2 },
    growth_modifiers: { INT: 2, WIS: 2, AGI: 1, STR: 0, CON: 0, CHA: 1 }
  },
  PRIMORDIAL: {
    name: '原初之血',
    rarity: 5,
    description: '世界诞生之初的原始力量，拥有创造和毁灭的双重本质。',
    attribute_modifiers: { STR: 4, AGI: 4, CON: 4, INT: 4, WIS: 4, CHA: 4 },
    growth_modifiers: { STR: 2, AGI: 2, CON: 2, INT: 2, WIS: 2, CHA: 2 }
  }
};